@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vitest@2.1.9_@types+node@22_aaee210e0c37a2901262e525d413ccdd\node_modules\vitest\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vitest@2.1.9_@types+node@22_aaee210e0c37a2901262e525d413ccdd\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vitest@2.1.9_@types+node@22_aaee210e0c37a2901262e525d413ccdd\node_modules\vitest\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vitest@2.1.9_@types+node@22_aaee210e0c37a2901262e525d413ccdd\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\vitest@2.1.9_@types+node@22_aaee210e0c37a2901262e525d413ccdd\node_modules\vitest\vitest.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\vitest@2.1.9_@types+node@22_aaee210e0c37a2901262e525d413ccdd\node_modules\vitest\vitest.mjs" %*
)
