{"version": 3, "file": "JsonEncoderStable.js", "sourceRoot": "", "sources": ["../../src/json/JsonEncoderStable.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,sEAA2D;AAC3D,+DAA0D;AAE1D,MAAa,iBAAkB,SAAQ,yBAAW;IACzC,QAAQ,CAAC,GAA4B;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAA,iBAAI,EAAC,IAAI,EAAE,qBAAS,CAAC,CAAC;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM;YAAE,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACvC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACnB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAClB,CAAC;QACD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IACpC,CAAC;CACF;AAlBD,8CAkBC"}