@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7\node_modules\webpack-dev-server\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7\node_modules\webpack-dev-server\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7\node_modules\webpack-dev-server\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7\node_modules\webpack-dev-server\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\..\webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7\node_modules\webpack-dev-server\bin\webpack-dev-server.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\..\webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7\node_modules\webpack-dev-server\bin\webpack-dev-server.js" %*
)
