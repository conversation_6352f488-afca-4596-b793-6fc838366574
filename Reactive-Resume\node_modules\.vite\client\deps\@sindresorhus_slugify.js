import {
  require_escape_string_regexp
} from "./chunk-3HETNJN3.js";
import {
  __commonJS
} from "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/lodash.deburr@4.1.0/node_modules/lodash.deburr/index.js
var require_lodash = __commonJS({
  "node_modules/.pnpm/lodash.deburr@4.1.0/node_modules/lodash.deburr/index.js"(exports, module) {
    var INFINITY = 1 / 0;
    var symbolTag = "[object Symbol]";
    var reLatin = /[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g;
    var rsComboMarksRange = "\\u0300-\\u036f\\ufe20-\\ufe23";
    var rsComboSymbolsRange = "\\u20d0-\\u20f0";
    var rsCombo = "[" + rsComboMarksRange + rsComboSymbolsRange + "]";
    var reComboMark = RegExp(rsCombo, "g");
    var deburredLetters = {
      // Latin-1 Supplement block.
      "À": "A",
      "Á": "A",
      "Â": "A",
      "Ã": "A",
      "Ä": "A",
      "Å": "A",
      "à": "a",
      "á": "a",
      "â": "a",
      "ã": "a",
      "ä": "a",
      "å": "a",
      "Ç": "C",
      "ç": "c",
      "Ð": "D",
      "ð": "d",
      "È": "E",
      "É": "E",
      "Ê": "E",
      "Ë": "E",
      "è": "e",
      "é": "e",
      "ê": "e",
      "ë": "e",
      "Ì": "I",
      "Í": "I",
      "Î": "I",
      "Ï": "I",
      "ì": "i",
      "í": "i",
      "î": "i",
      "ï": "i",
      "Ñ": "N",
      "ñ": "n",
      "Ò": "O",
      "Ó": "O",
      "Ô": "O",
      "Õ": "O",
      "Ö": "O",
      "Ø": "O",
      "ò": "o",
      "ó": "o",
      "ô": "o",
      "õ": "o",
      "ö": "o",
      "ø": "o",
      "Ù": "U",
      "Ú": "U",
      "Û": "U",
      "Ü": "U",
      "ù": "u",
      "ú": "u",
      "û": "u",
      "ü": "u",
      "Ý": "Y",
      "ý": "y",
      "ÿ": "y",
      "Æ": "Ae",
      "æ": "ae",
      "Þ": "Th",
      "þ": "th",
      "ß": "ss",
      // Latin Extended-A block.
      "Ā": "A",
      "Ă": "A",
      "Ą": "A",
      "ā": "a",
      "ă": "a",
      "ą": "a",
      "Ć": "C",
      "Ĉ": "C",
      "Ċ": "C",
      "Č": "C",
      "ć": "c",
      "ĉ": "c",
      "ċ": "c",
      "č": "c",
      "Ď": "D",
      "Đ": "D",
      "ď": "d",
      "đ": "d",
      "Ē": "E",
      "Ĕ": "E",
      "Ė": "E",
      "Ę": "E",
      "Ě": "E",
      "ē": "e",
      "ĕ": "e",
      "ė": "e",
      "ę": "e",
      "ě": "e",
      "Ĝ": "G",
      "Ğ": "G",
      "Ġ": "G",
      "Ģ": "G",
      "ĝ": "g",
      "ğ": "g",
      "ġ": "g",
      "ģ": "g",
      "Ĥ": "H",
      "Ħ": "H",
      "ĥ": "h",
      "ħ": "h",
      "Ĩ": "I",
      "Ī": "I",
      "Ĭ": "I",
      "Į": "I",
      "İ": "I",
      "ĩ": "i",
      "ī": "i",
      "ĭ": "i",
      "į": "i",
      "ı": "i",
      "Ĵ": "J",
      "ĵ": "j",
      "Ķ": "K",
      "ķ": "k",
      "ĸ": "k",
      "Ĺ": "L",
      "Ļ": "L",
      "Ľ": "L",
      "Ŀ": "L",
      "Ł": "L",
      "ĺ": "l",
      "ļ": "l",
      "ľ": "l",
      "ŀ": "l",
      "ł": "l",
      "Ń": "N",
      "Ņ": "N",
      "Ň": "N",
      "Ŋ": "N",
      "ń": "n",
      "ņ": "n",
      "ň": "n",
      "ŋ": "n",
      "Ō": "O",
      "Ŏ": "O",
      "Ő": "O",
      "ō": "o",
      "ŏ": "o",
      "ő": "o",
      "Ŕ": "R",
      "Ŗ": "R",
      "Ř": "R",
      "ŕ": "r",
      "ŗ": "r",
      "ř": "r",
      "Ś": "S",
      "Ŝ": "S",
      "Ş": "S",
      "Š": "S",
      "ś": "s",
      "ŝ": "s",
      "ş": "s",
      "š": "s",
      "Ţ": "T",
      "Ť": "T",
      "Ŧ": "T",
      "ţ": "t",
      "ť": "t",
      "ŧ": "t",
      "Ũ": "U",
      "Ū": "U",
      "Ŭ": "U",
      "Ů": "U",
      "Ű": "U",
      "Ų": "U",
      "ũ": "u",
      "ū": "u",
      "ŭ": "u",
      "ů": "u",
      "ű": "u",
      "ų": "u",
      "Ŵ": "W",
      "ŵ": "w",
      "Ŷ": "Y",
      "ŷ": "y",
      "Ÿ": "Y",
      "Ź": "Z",
      "Ż": "Z",
      "Ž": "Z",
      "ź": "z",
      "ż": "z",
      "ž": "z",
      "Ĳ": "IJ",
      "ĳ": "ij",
      "Œ": "Oe",
      "œ": "oe",
      "ŉ": "'n",
      "ſ": "ss"
    };
    var freeGlobal = typeof global == "object" && global && global.Object === Object && global;
    var freeSelf = typeof self == "object" && self && self.Object === Object && self;
    var root = freeGlobal || freeSelf || Function("return this")();
    function basePropertyOf(object) {
      return function(key) {
        return object == null ? void 0 : object[key];
      };
    }
    var deburrLetter = basePropertyOf(deburredLetters);
    var objectProto = Object.prototype;
    var objectToString = objectProto.toString;
    var Symbol = root.Symbol;
    var symbolProto = Symbol ? Symbol.prototype : void 0;
    var symbolToString = symbolProto ? symbolProto.toString : void 0;
    function baseToString(value) {
      if (typeof value == "string") {
        return value;
      }
      if (isSymbol(value)) {
        return symbolToString ? symbolToString.call(value) : "";
      }
      var result = value + "";
      return result == "0" && 1 / value == -INFINITY ? "-0" : result;
    }
    function isObjectLike(value) {
      return !!value && typeof value == "object";
    }
    function isSymbol(value) {
      return typeof value == "symbol" || isObjectLike(value) && objectToString.call(value) == symbolTag;
    }
    function toString(value) {
      return value == null ? "" : baseToString(value);
    }
    function deburr(string) {
      string = toString(string);
      return string && string.replace(reLatin, deburrLetter).replace(reComboMark, "");
    }
    module.exports = deburr;
  }
});

// node_modules/.pnpm/escape-string-regexp@2.0.0/node_modules/escape-string-regexp/index.js
var require_escape_string_regexp2 = __commonJS({
  "node_modules/.pnpm/escape-string-regexp@2.0.0/node_modules/escape-string-regexp/index.js"(exports, module) {
    "use strict";
    var matchOperatorsRegex = /[|\\{}()[\]^$+*?.-]/g;
    module.exports = (string) => {
      if (typeof string !== "string") {
        throw new TypeError("Expected a string");
      }
      return string.replace(matchOperatorsRegex, "\\$&");
    };
  }
});

// node_modules/.pnpm/@sindresorhus+transliterate@0.1.2/node_modules/@sindresorhus/transliterate/replacements.js
var require_replacements = __commonJS({
  "node_modules/.pnpm/@sindresorhus+transliterate@0.1.2/node_modules/@sindresorhus/transliterate/replacements.js"(exports, module) {
    "use strict";
    module.exports = [
      // German umlauts
      ["ß", "ss"],
      ["ä", "ae"],
      ["Ä", "Ae"],
      ["ö", "oe"],
      ["Ö", "Oe"],
      ["ü", "ue"],
      ["Ü", "Ue"],
      // Latin
      ["À", "A"],
      ["Á", "A"],
      ["Â", "A"],
      ["Ã", "A"],
      ["Ä", "Ae"],
      ["Å", "A"],
      ["Æ", "AE"],
      ["Ç", "C"],
      ["È", "E"],
      ["É", "E"],
      ["Ê", "E"],
      ["Ë", "E"],
      ["Ì", "I"],
      ["Í", "I"],
      ["Î", "I"],
      ["Ï", "I"],
      ["Ð", "D"],
      ["Ñ", "N"],
      ["Ò", "O"],
      ["Ó", "O"],
      ["Ô", "O"],
      ["Õ", "O"],
      ["Ö", "Oe"],
      ["Ő", "O"],
      ["Ø", "O"],
      ["Ù", "U"],
      ["Ú", "U"],
      ["Û", "U"],
      ["Ü", "Ue"],
      ["Ű", "U"],
      ["Ý", "Y"],
      ["Þ", "TH"],
      ["ß", "ss"],
      ["à", "a"],
      ["á", "a"],
      ["â", "a"],
      ["ã", "a"],
      ["ä", "ae"],
      ["å", "a"],
      ["æ", "ae"],
      ["ç", "c"],
      ["è", "e"],
      ["é", "e"],
      ["ê", "e"],
      ["ë", "e"],
      ["ì", "i"],
      ["í", "i"],
      ["î", "i"],
      ["ï", "i"],
      ["ð", "d"],
      ["ñ", "n"],
      ["ò", "o"],
      ["ó", "o"],
      ["ô", "o"],
      ["õ", "o"],
      ["ö", "oe"],
      ["ő", "o"],
      ["ø", "o"],
      ["ù", "u"],
      ["ú", "u"],
      ["û", "u"],
      ["ü", "ue"],
      ["ű", "u"],
      ["ý", "y"],
      ["þ", "th"],
      ["ÿ", "y"],
      ["ẞ", "SS"],
      // Vietnamese
      ["à", "a"],
      ["À", "A"],
      ["á", "a"],
      ["Á", "A"],
      ["â", "a"],
      ["Â", "A"],
      ["ã", "a"],
      ["Ã", "A"],
      ["è", "e"],
      ["È", "E"],
      ["é", "e"],
      ["É", "E"],
      ["ê", "e"],
      ["Ê", "E"],
      ["ì", "i"],
      ["Ì", "I"],
      ["í", "i"],
      ["Í", "I"],
      ["ò", "o"],
      ["Ò", "O"],
      ["ó", "o"],
      ["Ó", "O"],
      ["ô", "o"],
      ["Ô", "O"],
      ["õ", "o"],
      ["Õ", "O"],
      ["ù", "u"],
      ["Ù", "U"],
      ["ú", "u"],
      ["Ú", "U"],
      ["ý", "y"],
      ["Ý", "Y"],
      ["ă", "a"],
      ["Ă", "A"],
      ["Đ", "D"],
      ["đ", "d"],
      ["ĩ", "i"],
      ["Ĩ", "I"],
      ["ũ", "u"],
      ["Ũ", "U"],
      ["ơ", "o"],
      ["Ơ", "O"],
      ["ư", "u"],
      ["Ư", "U"],
      ["ạ", "a"],
      ["Ạ", "A"],
      ["ả", "a"],
      ["Ả", "A"],
      ["ấ", "a"],
      ["Ấ", "A"],
      ["ầ", "a"],
      ["Ầ", "A"],
      ["ẩ", "a"],
      ["Ẩ", "A"],
      ["ẫ", "a"],
      ["Ẫ", "A"],
      ["ậ", "a"],
      ["Ậ", "A"],
      ["ắ", "a"],
      ["Ắ", "A"],
      ["ằ", "a"],
      ["Ằ", "A"],
      ["ẳ", "a"],
      ["Ẳ", "A"],
      ["ẵ", "a"],
      ["Ẵ", "A"],
      ["ặ", "a"],
      ["Ặ", "A"],
      ["ẹ", "e"],
      ["Ẹ", "E"],
      ["ẻ", "e"],
      ["Ẻ", "E"],
      ["ẽ", "e"],
      ["Ẽ", "E"],
      ["ế", "e"],
      ["Ế", "E"],
      ["ề", "e"],
      ["Ề", "E"],
      ["ể", "e"],
      ["Ể", "E"],
      ["ễ", "e"],
      ["Ễ", "E"],
      ["ệ", "e"],
      ["Ệ", "E"],
      ["ỉ", "i"],
      ["Ỉ", "I"],
      ["ị", "i"],
      ["Ị", "I"],
      ["ọ", "o"],
      ["Ọ", "O"],
      ["ỏ", "o"],
      ["Ỏ", "O"],
      ["ố", "o"],
      ["Ố", "O"],
      ["ồ", "o"],
      ["Ồ", "O"],
      ["ổ", "o"],
      ["Ổ", "O"],
      ["ỗ", "o"],
      ["Ỗ", "O"],
      ["ộ", "o"],
      ["Ộ", "O"],
      ["ớ", "o"],
      ["Ớ", "O"],
      ["ờ", "o"],
      ["Ờ", "O"],
      ["ở", "o"],
      ["Ở", "O"],
      ["ỡ", "o"],
      ["Ỡ", "O"],
      ["ợ", "o"],
      ["Ợ", "O"],
      ["ụ", "u"],
      ["Ụ", "U"],
      ["ủ", "u"],
      ["Ủ", "U"],
      ["ứ", "u"],
      ["Ứ", "U"],
      ["ừ", "u"],
      ["Ừ", "U"],
      ["ử", "u"],
      ["Ử", "U"],
      ["ữ", "u"],
      ["Ữ", "U"],
      ["ự", "u"],
      ["Ự", "U"],
      ["ỳ", "y"],
      ["Ỳ", "Y"],
      ["ỵ", "y"],
      ["Ỵ", "Y"],
      ["ỷ", "y"],
      ["Ỷ", "Y"],
      ["ỹ", "y"],
      ["Ỹ", "Y"],
      // Arabic
      ["ء", "e"],
      ["آ", "a"],
      ["أ", "a"],
      ["ؤ", "w"],
      ["إ", "i"],
      ["ئ", "y"],
      ["ا", "a"],
      ["ب", "b"],
      ["ة", "t"],
      ["ت", "t"],
      ["ث", "th"],
      ["ج", "j"],
      ["ح", "h"],
      ["خ", "kh"],
      ["د", "d"],
      ["ذ", "dh"],
      ["ر", "r"],
      ["ز", "z"],
      ["س", "s"],
      ["ش", "sh"],
      ["ص", "s"],
      ["ض", "d"],
      ["ط", "t"],
      ["ظ", "z"],
      ["ع", "e"],
      ["غ", "gh"],
      ["ـ", "_"],
      ["ف", "f"],
      ["ق", "q"],
      ["ك", "k"],
      ["ل", "l"],
      ["م", "m"],
      ["ن", "n"],
      ["ه", "h"],
      ["و", "w"],
      ["ى", "a"],
      ["ي", "y"],
      ["َ‎", "a"],
      ["ُ", "u"],
      ["ِ‎", "i"],
      ["٠", "0"],
      ["١", "1"],
      ["٢", "2"],
      ["٣", "3"],
      ["٤", "4"],
      ["٥", "5"],
      ["٦", "6"],
      ["٧", "7"],
      ["٨", "8"],
      ["٩", "9"],
      // Persian / Farsi
      ["چ", "ch"],
      ["ک", "k"],
      ["گ", "g"],
      ["پ", "p"],
      ["ژ", "zh"],
      ["ی", "y"],
      ["۰", "0"],
      ["۱", "1"],
      ["۲", "2"],
      ["۳", "3"],
      ["۴", "4"],
      ["۵", "5"],
      ["۶", "6"],
      ["۷", "7"],
      ["۸", "8"],
      ["۹", "9"],
      // Pashto
      ["ټ", "p"],
      ["ځ", "z"],
      ["څ", "c"],
      ["ډ", "d"],
      ["ﺫ", "d"],
      ["ﺭ", "r"],
      ["ړ", "r"],
      ["ﺯ", "z"],
      ["ږ", "g"],
      ["ښ", "x"],
      ["ګ", "g"],
      ["ڼ", "n"],
      ["ۀ", "e"],
      ["ې", "e"],
      ["ۍ", "ai"],
      // Urdu
      ["ٹ", "t"],
      ["ڈ", "d"],
      ["ڑ", "r"],
      ["ں", "n"],
      ["ہ", "h"],
      ["ھ", "h"],
      ["ے", "e"],
      // Russian
      ["А", "A"],
      ["а", "a"],
      ["Б", "B"],
      ["б", "b"],
      ["В", "V"],
      ["в", "v"],
      ["Г", "G"],
      ["г", "g"],
      ["Д", "D"],
      ["д", "d"],
      ["Е", "E"],
      ["е", "e"],
      ["Ж", "Zh"],
      ["ж", "zh"],
      ["З", "Z"],
      ["з", "z"],
      ["И", "I"],
      ["и", "i"],
      ["Й", "J"],
      ["й", "j"],
      ["К", "K"],
      ["к", "k"],
      ["Л", "L"],
      ["л", "l"],
      ["М", "M"],
      ["м", "m"],
      ["Н", "N"],
      ["н", "n"],
      ["О", "O"],
      ["о", "o"],
      ["П", "P"],
      ["п", "p"],
      ["Р", "R"],
      ["р", "r"],
      ["С", "S"],
      ["с", "s"],
      ["Т", "T"],
      ["т", "t"],
      ["У", "U"],
      ["у", "u"],
      ["Ф", "F"],
      ["ф", "f"],
      ["Х", "H"],
      ["х", "h"],
      ["Ц", "Cz"],
      ["ц", "cz"],
      ["Ч", "Ch"],
      ["ч", "ch"],
      ["Ш", "Sh"],
      ["ш", "sh"],
      ["Щ", "Shh"],
      ["щ", "shh"],
      ["Ъ", ""],
      ["ъ", ""],
      ["Ы", "Y"],
      ["ы", "y"],
      ["Ь", ""],
      ["ь", ""],
      ["Э", "E"],
      ["э", "e"],
      ["Ю", "Yu"],
      ["ю", "yu"],
      ["Я", "Ya"],
      ["я", "ya"],
      ["Ё", "Yo"],
      ["ё", "yo"],
      // Romanian
      ["ă", "a"],
      ["Ă", "A"],
      ["ș", "s"],
      ["Ș", "S"],
      ["ț", "t"],
      ["Ț", "T"],
      ["ţ", "t"],
      ["Ţ", "T"],
      // Turkish
      ["ş", "s"],
      ["Ş", "S"],
      ["ç", "c"],
      ["Ç", "C"],
      ["ğ", "g"],
      ["Ğ", "G"],
      ["ı", "i"],
      ["İ", "I"],
      // Armenian
      ["ա", "a"],
      ["Ա", "A"],
      ["բ", "b"],
      ["Բ", "B"],
      ["գ", "g"],
      ["Գ", "G"],
      ["դ", "d"],
      ["Դ", "D"],
      ["ե", "ye"],
      ["Ե", "Ye"],
      ["զ", "z"],
      ["Զ", "Z"],
      ["է", "e"],
      ["Է", "E"],
      ["ը", "y"],
      ["Ը", "Y"],
      ["թ", "t"],
      ["Թ", "T"],
      ["ժ", "zh"],
      ["Ժ", "Zh"],
      ["ի", "i"],
      ["Ի", "I"],
      ["լ", "l"],
      ["Լ", "L"],
      ["խ", "kh"],
      ["Խ", "Kh"],
      ["ծ", "ts"],
      ["Ծ", "Ts"],
      ["կ", "k"],
      ["Կ", "K"],
      ["հ", "h"],
      ["Հ", "H"],
      ["ձ", "dz"],
      ["Ձ", "Dz"],
      ["ղ", "gh"],
      ["Ղ", "Gh"],
      ["ճ", "tch"],
      ["Ճ", "Tch"],
      ["մ", "m"],
      ["Մ", "M"],
      ["յ", "y"],
      ["Յ", "Y"],
      ["ն", "n"],
      ["Ն", "N"],
      ["շ", "sh"],
      ["Շ", "Sh"],
      ["ո", "vo"],
      ["Ո", "Vo"],
      ["չ", "ch"],
      ["Չ", "Ch"],
      ["պ", "p"],
      ["Պ", "P"],
      ["ջ", "j"],
      ["Ջ", "J"],
      ["ռ", "r"],
      ["Ռ", "R"],
      ["ս", "s"],
      ["Ս", "S"],
      ["վ", "v"],
      ["Վ", "V"],
      ["տ", "t"],
      ["Տ", "T"],
      ["ր", "r"],
      ["Ր", "R"],
      ["ց", "c"],
      ["Ց", "C"],
      ["ու", "u"],
      ["ՈՒ", "U"],
      ["Ու", "U"],
      ["փ", "p"],
      ["Փ", "P"],
      ["ք", "q"],
      ["Ք", "Q"],
      ["օ", "o"],
      ["Օ", "O"],
      ["ֆ", "f"],
      ["Ֆ", "F"],
      ["և", "yev"],
      // Georgian
      ["ა", "a"],
      ["ბ", "b"],
      ["გ", "g"],
      ["დ", "d"],
      ["ე", "e"],
      ["ვ", "v"],
      ["ზ", "z"],
      ["თ", "t"],
      ["ი", "i"],
      ["კ", "k"],
      ["ლ", "l"],
      ["მ", "m"],
      ["ნ", "n"],
      ["ო", "o"],
      ["პ", "p"],
      ["ჟ", "zh"],
      ["რ", "r"],
      ["ს", "s"],
      ["ტ", "t"],
      ["უ", "u"],
      ["ფ", "ph"],
      ["ქ", "q"],
      ["ღ", "gh"],
      ["ყ", "k"],
      ["შ", "sh"],
      ["ჩ", "ch"],
      ["ც", "ts"],
      ["ძ", "dz"],
      ["წ", "ts"],
      ["ჭ", "tch"],
      ["ხ", "kh"],
      ["ჯ", "j"],
      ["ჰ", "h"],
      // Czech
      ["č", "c"],
      ["ď", "d"],
      ["ě", "e"],
      ["ň", "n"],
      ["ř", "r"],
      ["š", "s"],
      ["ť", "t"],
      ["ů", "u"],
      ["ž", "z"],
      ["Č", "C"],
      ["Ď", "D"],
      ["Ě", "E"],
      ["Ň", "N"],
      ["Ř", "R"],
      ["Š", "S"],
      ["Ť", "T"],
      ["Ů", "U"],
      ["Ž", "Z"],
      // Dhivehi
      ["ހ", "h"],
      ["ށ", "sh"],
      ["ނ", "n"],
      ["ރ", "r"],
      ["ބ", "b"],
      ["ޅ", "lh"],
      ["ކ", "k"],
      ["އ", "a"],
      ["ވ", "v"],
      ["މ", "m"],
      ["ފ", "f"],
      ["ދ", "dh"],
      ["ތ", "th"],
      ["ލ", "l"],
      ["ގ", "g"],
      ["ޏ", "gn"],
      ["ސ", "s"],
      ["ޑ", "d"],
      ["ޒ", "z"],
      ["ޓ", "t"],
      ["ޔ", "y"],
      ["ޕ", "p"],
      ["ޖ", "j"],
      ["ޗ", "ch"],
      ["ޘ", "tt"],
      ["ޙ", "hh"],
      ["ޚ", "kh"],
      ["ޛ", "th"],
      ["ޜ", "z"],
      ["ޝ", "sh"],
      ["ޞ", "s"],
      ["ޟ", "d"],
      ["ޠ", "t"],
      ["ޡ", "z"],
      ["ޢ", "a"],
      ["ޣ", "gh"],
      ["ޤ", "q"],
      ["ޥ", "w"],
      ["ަ", "a"],
      ["ާ", "aa"],
      ["ި", "i"],
      ["ީ", "ee"],
      ["ު", "u"],
      ["ޫ", "oo"],
      ["ެ", "e"],
      ["ޭ", "ey"],
      ["ޮ", "o"],
      ["ޯ", "oa"],
      ["ް", ""],
      // Greek
      ["α", "a"],
      ["β", "v"],
      ["γ", "g"],
      ["δ", "d"],
      ["ε", "e"],
      ["ζ", "z"],
      ["η", "i"],
      ["θ", "th"],
      ["ι", "i"],
      ["κ", "k"],
      ["λ", "l"],
      ["μ", "m"],
      ["ν", "n"],
      ["ξ", "ks"],
      ["ο", "o"],
      ["π", "p"],
      ["ρ", "r"],
      ["σ", "s"],
      ["τ", "t"],
      ["υ", "y"],
      ["φ", "f"],
      ["χ", "x"],
      ["ψ", "ps"],
      ["ω", "o"],
      ["ά", "a"],
      ["έ", "e"],
      ["ί", "i"],
      ["ό", "o"],
      ["ύ", "y"],
      ["ή", "i"],
      ["ώ", "o"],
      ["ς", "s"],
      ["ϊ", "i"],
      ["ΰ", "y"],
      ["ϋ", "y"],
      ["ΐ", "i"],
      ["Α", "A"],
      ["Β", "B"],
      ["Γ", "G"],
      ["Δ", "D"],
      ["Ε", "E"],
      ["Ζ", "Z"],
      ["Η", "I"],
      ["Θ", "TH"],
      ["Ι", "I"],
      ["Κ", "K"],
      ["Λ", "L"],
      ["Μ", "M"],
      ["Ν", "N"],
      ["Ξ", "KS"],
      ["Ο", "O"],
      ["Π", "P"],
      ["Ρ", "R"],
      ["Σ", "S"],
      ["Τ", "T"],
      ["Υ", "Y"],
      ["Φ", "F"],
      ["Χ", "X"],
      ["Ψ", "PS"],
      ["Ω", "O"],
      ["Ά", "A"],
      ["Έ", "E"],
      ["Ί", "I"],
      ["Ό", "O"],
      ["Ύ", "Y"],
      ["Ή", "I"],
      ["Ώ", "O"],
      ["Ϊ", "I"],
      ["Ϋ", "Y"],
      // Disabled as it conflicts with German and Latin.
      // Hungarian
      // ['ä', 'a'],
      // ['Ä', 'A'],
      // ['ö', 'o'],
      // ['Ö', 'O'],
      // ['ü', 'u'],
      // ['Ü', 'U'],
      // ['ű', 'u'],
      // ['Ű', 'U'],
      // Latvian
      ["ā", "a"],
      ["ē", "e"],
      ["ģ", "g"],
      ["ī", "i"],
      ["ķ", "k"],
      ["ļ", "l"],
      ["ņ", "n"],
      ["ū", "u"],
      ["Ā", "A"],
      ["Ē", "E"],
      ["Ģ", "G"],
      ["Ī", "I"],
      ["Ķ", "K"],
      ["Ļ", "L"],
      ["Ņ", "N"],
      ["Ū", "U"],
      ["č", "c"],
      ["š", "s"],
      ["ž", "z"],
      ["Č", "C"],
      ["Š", "S"],
      ["Ž", "Z"],
      // Lithuanian
      ["ą", "a"],
      ["č", "c"],
      ["ę", "e"],
      ["ė", "e"],
      ["į", "i"],
      ["š", "s"],
      ["ų", "u"],
      ["ū", "u"],
      ["ž", "z"],
      ["Ą", "A"],
      ["Č", "C"],
      ["Ę", "E"],
      ["Ė", "E"],
      ["Į", "I"],
      ["Š", "S"],
      ["Ų", "U"],
      ["Ū", "U"],
      // Macedonian
      ["Ќ", "Kj"],
      ["ќ", "kj"],
      ["Љ", "Lj"],
      ["љ", "lj"],
      ["Њ", "Nj"],
      ["њ", "nj"],
      ["Тс", "Ts"],
      ["тс", "ts"],
      // Polish
      ["ą", "a"],
      ["ć", "c"],
      ["ę", "e"],
      ["ł", "l"],
      ["ń", "n"],
      ["ś", "s"],
      ["ź", "z"],
      ["ż", "z"],
      ["Ą", "A"],
      ["Ć", "C"],
      ["Ę", "E"],
      ["Ł", "L"],
      ["Ń", "N"],
      ["Ś", "S"],
      ["Ź", "Z"],
      ["Ż", "Z"],
      // Disabled as it conflicts with Vietnamese.
      // Serbian
      // ['љ', 'lj'],
      // ['њ', 'nj'],
      // ['Љ', 'Lj'],
      // ['Њ', 'Nj'],
      // ['đ', 'dj'],
      // ['Đ', 'Dj'],
      // ['ђ', 'dj'],
      // ['ј', 'j'],
      // ['ћ', 'c'],
      // ['џ', 'dz'],
      // ['Ђ', 'Dj'],
      // ['Ј', 'j'],
      // ['Ћ', 'C'],
      // ['Џ', 'Dz'],
      // Disabled as it conflicts with German and Latin.
      // Slovak
      // ['ä', 'a'],
      // ['Ä', 'A'],
      // ['ľ', 'l'],
      // ['ĺ', 'l'],
      // ['ŕ', 'r'],
      // ['Ľ', 'L'],
      // ['Ĺ', 'L'],
      // ['Ŕ', 'R'],
      // Disabled as it conflicts with German and Latin.
      // Swedish
      // ['å', 'o'],
      // ['Å', 'o'],
      // ['ä', 'a'],
      // ['Ä', 'A'],
      // ['ë', 'e'],
      // ['Ë', 'E'],
      // ['ö', 'o'],
      // ['Ö', 'O'],
      // Ukrainian
      ["Є", "Ye"],
      ["І", "I"],
      ["Ї", "Yi"],
      ["Ґ", "G"],
      ["є", "ye"],
      ["і", "i"],
      ["ї", "yi"],
      ["ґ", "g"]
      // Danish
      // ['Æ', 'Ae'],
      // ['Ø', 'Oe'],
      // ['Å', 'Aa'],
      // ['æ', 'ae'],
      // ['ø', 'oe'],
      // ['å', 'aa']
    ];
  }
});

// node_modules/.pnpm/@sindresorhus+transliterate@0.1.2/node_modules/@sindresorhus/transliterate/index.js
var require_transliterate = __commonJS({
  "node_modules/.pnpm/@sindresorhus+transliterate@0.1.2/node_modules/@sindresorhus/transliterate/index.js"(exports, module) {
    "use strict";
    var deburr = require_lodash();
    var escapeStringRegexp = require_escape_string_regexp2();
    var builtinReplacements = require_replacements();
    var doCustomReplacements = (string, replacements) => {
      for (const [key, value] of replacements) {
        string = string.replace(new RegExp(escapeStringRegexp(key), "g"), value);
      }
      return string;
    };
    module.exports = (string, options) => {
      if (typeof string !== "string") {
        throw new TypeError(`Expected a string, got \`${typeof string}\``);
      }
      options = {
        customReplacements: [],
        ...options
      };
      const customReplacements = new Map([
        ...builtinReplacements,
        ...options.customReplacements
      ]);
      string = string.normalize();
      string = doCustomReplacements(string, customReplacements);
      string = deburr(string);
      return string;
    };
  }
});

// node_modules/.pnpm/@sindresorhus+slugify@1.1.2/node_modules/@sindresorhus/slugify/overridable-replacements.js
var require_overridable_replacements = __commonJS({
  "node_modules/.pnpm/@sindresorhus+slugify@1.1.2/node_modules/@sindresorhus/slugify/overridable-replacements.js"(exports, module) {
    "use strict";
    module.exports = [
      ["&", " and "],
      ["🦄", " unicorn "],
      ["♥", " love "]
    ];
  }
});

// node_modules/.pnpm/@sindresorhus+slugify@1.1.2/node_modules/@sindresorhus/slugify/index.js
var require_slugify = __commonJS({
  "node_modules/.pnpm/@sindresorhus+slugify@1.1.2/node_modules/@sindresorhus/slugify/index.js"(exports, module) {
    var escapeStringRegexp = require_escape_string_regexp();
    var transliterate = require_transliterate();
    var builtinOverridableReplacements = require_overridable_replacements();
    var decamelize = (string) => {
      return string.replace(/([A-Z]{2,})(\d+)/g, "$1 $2").replace(/([a-z\d]+)([A-Z]{2,})/g, "$1 $2").replace(/([a-z\d])([A-Z])/g, "$1 $2").replace(/([A-Z]+)([A-Z][a-z\d]+)/g, "$1 $2");
    };
    var removeMootSeparators = (string, separator) => {
      const escapedSeparator = escapeStringRegexp(separator);
      return string.replace(new RegExp(`${escapedSeparator}{2,}`, "g"), separator).replace(new RegExp(`^${escapedSeparator}|${escapedSeparator}$`, "g"), "");
    };
    var slugify = (string, options) => {
      if (typeof string !== "string") {
        throw new TypeError(`Expected a string, got \`${typeof string}\``);
      }
      options = {
        separator: "-",
        lowercase: true,
        decamelize: true,
        customReplacements: [],
        preserveLeadingUnderscore: false,
        ...options
      };
      const shouldPrependUnderscore = options.preserveLeadingUnderscore && string.startsWith("_");
      const customReplacements = new Map([
        ...builtinOverridableReplacements,
        ...options.customReplacements
      ]);
      string = transliterate(string, { customReplacements });
      if (options.decamelize) {
        string = decamelize(string);
      }
      let patternSlug = /[^a-zA-Z\d]+/g;
      if (options.lowercase) {
        string = string.toLowerCase();
        patternSlug = /[^a-z\d]+/g;
      }
      string = string.replace(patternSlug, options.separator);
      string = string.replace(/\\/g, "");
      if (options.separator) {
        string = removeMootSeparators(string, options.separator);
      }
      if (shouldPrependUnderscore) {
        string = `_${string}`;
      }
      return string;
    };
    var counter = () => {
      const occurrences = /* @__PURE__ */ new Map();
      const countable = (string, options) => {
        string = slugify(string, options);
        if (!string) {
          return "";
        }
        const stringLower = string.toLowerCase();
        const numberless = occurrences.get(stringLower.replace(/(?:-\d+?)+?$/, "")) || 0;
        const counter2 = occurrences.get(stringLower);
        occurrences.set(stringLower, typeof counter2 === "number" ? counter2 + 1 : 1);
        const newCounter = occurrences.get(stringLower) || 2;
        if (newCounter >= 2 || numberless > 2) {
          string = `${string}-${newCounter}`;
        }
        return string;
      };
      countable.reset = () => {
        occurrences.clear();
      };
      return countable;
    };
    module.exports = slugify;
    module.exports.counter = counter;
  }
});
export default require_slugify();
//# sourceMappingURL=@sindresorhus_slugify.js.map
