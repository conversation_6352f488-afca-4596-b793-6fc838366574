@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\openai@4.82.0_ws@8.18.0_zod@3.24.1\node_modules\openai\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\openai@4.82.0_ws@8.18.0_zod@3.24.1\node_modules\openai\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\openai@4.82.0_ws@8.18.0_zod@3.24.1\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\openai@4.82.0_ws@8.18.0_zod@3.24.1\node_modules\openai\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\openai@4.82.0_ws@8.18.0_zod@3.24.1\node_modules\openai\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\openai@4.82.0_ws@8.18.0_zod@3.24.1\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\openai@4.82.0_ws@8.18.0_zod@3.24.1\node_modules\openai\bin\cli" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\openai@4.82.0_ws@8.18.0_zod@3.24.1\node_modules\openai\bin\cli" %*
)
