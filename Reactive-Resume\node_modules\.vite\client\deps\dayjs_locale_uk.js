import {
  require_dayjs_min
} from "./chunk-M2A4HNR4.js";
import {
  __commonJS
} from "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/uk.js
var require_uk = __commonJS({
  "node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/uk.js"(exports, module) {
    !function(_, e) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = e(require_dayjs_min()) : "function" == typeof define && define.amd ? define(["dayjs"], e) : (_ = "undefined" != typeof globalThis ? globalThis : _ || self).dayjs_locale_uk = e(_.dayjs);
    }(exports, function(_) {
      "use strict";
      function e(_2) {
        return _2 && "object" == typeof _2 && "default" in _2 ? _2 : { default: _2 };
      }
      var t = e(_), s = "січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня".split("_"), n = "січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень".split("_"), o = /D[oD]?(\[[^[\]]*\]|\s)+MMMM?/;
      function d(_2, e2, t2) {
        var s2, n2;
        return "m" === t2 ? e2 ? "хвилина" : "хвилину" : "h" === t2 ? e2 ? "година" : "годину" : _2 + " " + (s2 = +_2, n2 = { ss: e2 ? "секунда_секунди_секунд" : "секунду_секунди_секунд", mm: e2 ? "хвилина_хвилини_хвилин" : "хвилину_хвилини_хвилин", hh: e2 ? "година_години_годин" : "годину_години_годин", dd: "день_дні_днів", MM: "місяць_місяці_місяців", yy: "рік_роки_років" }[t2].split("_"), s2 % 10 == 1 && s2 % 100 != 11 ? n2[0] : s2 % 10 >= 2 && s2 % 10 <= 4 && (s2 % 100 < 10 || s2 % 100 >= 20) ? n2[1] : n2[2]);
      }
      var i = function(_2, e2) {
        return o.test(e2) ? s[_2.month()] : n[_2.month()];
      };
      i.s = n, i.f = s;
      var r = { name: "uk", weekdays: "неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота".split("_"), weekdaysShort: "ндл_пнд_втр_срд_чтв_птн_сбт".split("_"), weekdaysMin: "нд_пн_вт_ср_чт_пт_сб".split("_"), months: i, monthsShort: "січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд".split("_"), weekStart: 1, relativeTime: { future: "за %s", past: "%s тому", s: "декілька секунд", m: d, mm: d, h: d, hh: d, d: "день", dd: d, M: "місяць", MM: d, y: "рік", yy: d }, ordinal: function(_2) {
        return _2;
      }, formats: { LT: "HH:mm", LTS: "HH:mm:ss", L: "DD.MM.YYYY", LL: "D MMMM YYYY р.", LLL: "D MMMM YYYY р., HH:mm", LLLL: "dddd, D MMMM YYYY р., HH:mm" } };
      return t.default.locale(r, null, true), r;
    });
  }
});
export default require_uk();
//# sourceMappingURL=dayjs_locale_uk.js.map
