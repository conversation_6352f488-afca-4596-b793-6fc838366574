{"version": 3, "file": "StreamingReader.js", "sourceRoot": "", "sources": ["../../src/buffers/StreamingReader.ts"], "names": [], "mappings": ";;;AAAA,qCAAgC;AAChC,kDAA6C;AAG7C,MAAa,eAAe;IAQ1B,YAAY,YAAoB,EAAE,GAAG,IAAI;QAF/B,OAAE,GAAG,CAAC,CAAC;QAGf,IAAI,CAAC,MAAM,GAAG,IAAI,eAAM,CAAC,SAAS,CAAC,CAAC;IACtC,CAAC;IAKM,IAAI;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAChC,CAAC;IAOS,UAAU,CAAC,IAAY;QAC/B,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE;YAAE,MAAM,IAAI,UAAU,CAAC,eAAe,CAAC,CAAC;IAChE,CAAC;IASM,IAAI,CAAC,KAAiB;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAMM,OAAO;QACZ,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACd,CAAC;IAID,IAAW,KAAK;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;IAC3B,CAAC;IAED,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED,IAAW,CAAC;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IAClC,CAAC;IAED,IAAW,CAAC,CAAC,CAAS;QACpB,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;IAC/B,CAAC;IAEM,IAAI;QACT,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IAEM,IAAI,CAAC,MAAc;QACxB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACxB,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC;IACnB,CAAC;IAEM,GAAG,CAAC,IAAY;QACrB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtB,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QACb,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,EAAE;QACP,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC;IAEM,EAAE;QACP,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;IAEM,GAAG;QACR,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,GAAG;QACR,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAEM,GAAG;QACR,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAEM,IAAI,CAAC,IAAY;QACtB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtB,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC;QACf,OAAO,IAAA,uBAAU,EAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEM,KAAK,CAAC,MAAc;QACzB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACxB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;YAAE,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QACb,OAAO,GAAG,CAAC;IACb,CAAC;IAIM,KAAK,CAAC,KAAiB;QAC5B,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;CACF;AA9KD,0CA8KC"}