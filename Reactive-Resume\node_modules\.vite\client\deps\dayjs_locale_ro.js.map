{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/ro.js"], "sourcesContent": ["!function(e,i){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=i(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],i):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_ro=i(e.dayjs)}(this,(function(e){\"use strict\";function i(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=i(e),_={name:\"ro\",weekdays:\"Duminică_Luni_Marți_Miercuri_Joi_Vineri_Sâmbătă\".split(\"_\"),weekdaysShort:\"Dum_Lun_Mar_Mie_Joi_Vin_Sâm\".split(\"_\"),weekdaysMin:\"Du_Lu_Ma_Mi_Jo_Vi_Sâ\".split(\"_\"),months:\"<PERSON>uarie_Februarie_Martie_Aprilie_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_August_Septembrie_Octombrie_Noiembrie_Decembrie\".split(\"_\"),monthsShort:\"Ian._Febr._Mart._Apr._Mai_Iun._Iul._Aug._Sept._Oct._Nov._Dec.\".split(\"_\"),weekStart:1,formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY H:mm\",LLLL:\"dddd, D MMMM YYYY H:mm\"},relativeTime:{future:\"peste %s\",past:\"acum %s\",s:\"câteva secunde\",m:\"un minut\",mm:\"%d minute\",h:\"o oră\",hh:\"%d ore\",d:\"o zi\",dd:\"%d zile\",M:\"o lună\",MM:\"%d luni\",y:\"un an\",yy:\"%d ani\"},ordinal:function(e){return e}};return t.default.locale(_,null,!0),_}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,kDAAkD,MAAM,GAAG,GAAE,eAAc,8BAA8B,MAAM,GAAG,GAAE,aAAY,uBAAuB,MAAM,GAAG,GAAE,QAAO,oGAAoG,MAAM,GAAG,GAAE,aAAY,gEAAgE,MAAM,GAAG,GAAE,WAAU,GAAE,SAAQ,EAAC,IAAG,QAAO,KAAI,WAAU,GAAE,cAAa,IAAG,eAAc,KAAI,oBAAmB,MAAK,yBAAwB,GAAE,cAAa,EAAC,QAAO,YAAW,MAAK,WAAU,GAAE,kBAAiB,GAAE,YAAW,IAAG,aAAY,GAAE,SAAQ,IAAG,UAAS,GAAE,QAAO,IAAG,WAAU,GAAE,UAAS,IAAG,WAAU,GAAE,SAAQ,IAAG,SAAQ,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA;AAAA,MAAC,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e"]}