# [postcss][postcss]-normalize-repeat-style

> Normalize repeat styles with PostCSS.

## Install

With [npm](https://npmjs.org/package/postcss-normalize-repeat-style) do:

```
npm install postcss-normalize-repeat-style --save
```

## Example

### Input

```css
h1 {
    background: url(image.jpg) repeat no-repeat
}
```

### Output

```css
h1 {
    background: url(image.jpg) repeat-x
}
```

## Usage

See the [PostCSS documentation](https://github.com/postcss/postcss#usage) for
examples for your environment.

## Contributors

See [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).

## License

MIT © [Ben Briggs](http://beneb.info)

[postcss]: https://github.com/postcss/postcss
