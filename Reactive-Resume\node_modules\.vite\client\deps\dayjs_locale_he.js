import {
  require_dayjs_min
} from "./chunk-M2A4HNR4.js";
import {
  __commonJS
} from "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/he.js
var require_he = __commonJS({
  "node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/he.js"(exports, module) {
    !function(Y, M) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = M(require_dayjs_min()) : "function" == typeof define && define.amd ? define(["dayjs"], M) : (Y = "undefined" != typeof globalThis ? globalThis : Y || self).dayjs_locale_he = M(Y.dayjs);
    }(exports, function(Y) {
      "use strict";
      function M(Y2) {
        return Y2 && "object" == typeof Y2 && "default" in Y2 ? Y2 : { default: Y2 };
      }
      var d = M(Y), e = { s: "מספר שניות", ss: "%d שניות", m: "דקה", mm: "%d דקות", h: "שעה", hh: "%d שעות", hh2: "שעתיים", d: "יום", dd: "%d ימים", dd2: "יומיים", M: "חודש", MM: "%d חודשים", MM2: "חודשיים", y: "שנה", yy: "%d שנים", yy2: "שנתיים" };
      function _(Y2, M2, d2) {
        return (e[d2 + (2 === Y2 ? "2" : "")] || e[d2]).replace("%d", Y2);
      }
      var l = { name: "he", weekdays: "ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת".split("_"), weekdaysShort: "א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳".split("_"), weekdaysMin: "א׳_ב׳_ג׳_ד׳_ה׳_ו_ש׳".split("_"), months: "ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר".split("_"), monthsShort: "ינו_פבר_מרץ_אפר_מאי_יונ_יול_אוג_ספט_אוק_נוב_דצמ".split("_"), relativeTime: { future: "בעוד %s", past: "לפני %s", s: _, m: _, mm: _, h: _, hh: _, d: _, dd: _, M: _, MM: _, y: _, yy: _ }, ordinal: function(Y2) {
        return Y2;
      }, format: { LT: "HH:mm", LTS: "HH:mm:ss", L: "DD/MM/YYYY", LL: "D [ב]MMMM YYYY", LLL: "D [ב]MMMM YYYY HH:mm", LLLL: "dddd, D [ב]MMMM YYYY HH:mm", l: "D/M/YYYY", ll: "D MMM YYYY", lll: "D MMM YYYY HH:mm", llll: "ddd, D MMM YYYY HH:mm" }, formats: { LT: "HH:mm", LTS: "HH:mm:ss", L: "DD/MM/YYYY", LL: "D [ב]MMMM YYYY", LLL: "D [ב]MMMM YYYY HH:mm", LLLL: "dddd, D [ב]MMMM YYYY HH:mm", l: "D/M/YYYY", ll: "D MMM YYYY", lll: "D MMM YYYY HH:mm", llll: "ddd, D MMM YYYY HH:mm" } };
      return d.default.locale(l, null, true), l;
    });
  }
});
export default require_he();
//# sourceMappingURL=dayjs_locale_he.js.map
