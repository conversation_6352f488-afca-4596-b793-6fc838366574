@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770\node_modules\ts-jest\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770\node_modules\ts-jest\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770\node_modules\ts-jest\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770\node_modules\ts-jest\cli.js" %*
)
