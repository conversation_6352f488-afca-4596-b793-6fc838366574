{
  "extends": ["plugin:@nx/react", "../../.eslintrc.json"],
  "ignorePatterns": ["!**/*"],
  "overrides": [
    {
      "files": ["*.ts", "*.tsx"],
      "extends": [
        "plugin:tailwindcss/recommended",
        "plugin:@tanstack/eslint-plugin-query/recommended"
      ],
      "parserOptions": {
        "projectService": "./apps/client/tsconfig.json"
      },
      "settings": {
        "tailwindcss": {
          "callees": ["cn", "clsx", "cva"],
          "config": "tailwind.config.js",
          "whitelist": ["ph", "ph\\-.*", "si", "si\\-.*"]
        }
      },
      "plugins": ["lingui"],
      "rules": {
        // eslint
        "@typescript-eslint/no-require-imports": "off",

        // react
        "react/no-unescaped-entities": "off",
        "react/jsx-sort-props": [
          "error",
          {
            "reservedFirst": true,
            "callbacksLast": true,
            "shorthandFirst": true,
            "noSortAlphabetically": true
          }
        ],

        // react-hooks
        "react-hooks/exhaustive-deps": "off",

        // lingui
        "lingui/no-unlocalized-strings": [
          2,
          {
            "ignore": [
              // Ignore strings which are a single "word" (no spaces)
              // and doesn't start with an uppercase letter
              "^(?![A-Z])\\S+$",
              // Ignore UPPERCASE literals
              // Example: const test = "FOO"
              "^[A-Z0-9_-]+$"
            ],
            "ignoreNames": [
              // Ignore matching className (case-insensitive)
              { "regex": { "pattern": "className", "flags": "i" } },
              // Ignore UPPERCASE names
              // Example: test.FOO = "ola!"
              { "regex": { "pattern": "^[A-Z0-9_-]+$" } },
              "id",
              "src",
              "srcSet",
              "styleName",
              "placeholder",
              "alt",
              "type",
              "width",
              "height",
              "displayName",
              "Authorization"
            ],
            "ignoreFunctions": [
              "cn",
              "cva",
              "track",
              "Error",
              "console.*",
              "*headers.set",
              "*.addEventListener",
              "*.removeEventListener",
              "*.postMessage",
              "*.getElementById",
              "*.dispatch",
              "*.commit",
              "*.includes",
              "*.indexOf",
              "*.endsWith",
              "*.startsWith",
              "require"
            ],
            // Following settings require typed linting https://typescript-eslint.io/getting-started/typed-linting/
            "useTsTypes": true,
            "ignoreMethodsOnTypes": [
              // Ignore specified methods on Map and Set types
              "Map.get",
              "Map.has",
              "Set.has"
            ]
          }
        ],
        "lingui/t-call-in-function": 2,
        "lingui/no-single-variables-to-translate": 2,
        "lingui/no-expression-in-message": 2,
        "lingui/no-single-tag-to-translate": 2,
        "lingui/no-trans-inside-trans": 2,
        "lingui/text-restrictions": [
          2,
          {
            "rules": [
              {
                "patterns": ["''", "\"", "’", "“"],
                "message": "This string contains forbidden characters"
              }
            ]
          }
        ]
      }
    },
    {
      "files": ["*.ts", "*.tsx"],
      "rules": {}
    },
    {
      "files": ["*.js", "*.jsx"],
      "rules": {}
    }
  ]
}
