{"version": 3, "sources": ["../../../.pnpm/use-breakpoint@4.0.6_react-_d6f9684c5fc81e6917d1df0ad24612f4/node_modules/use-breakpoint/dist/esm/createMediaQueries.js", "../../../.pnpm/use-breakpoint@4.0.6_react-_d6f9684c5fc81e6917d1df0ad24612f4/node_modules/use-breakpoint/dist/esm/getCSSMediaQueries.js", "../../../.pnpm/use-breakpoint@4.0.6_react-_d6f9684c5fc81e6917d1df0ad24612f4/node_modules/use-breakpoint/dist/esm/useBreakpoint.js"], "sourcesContent": ["/**\n * Create media query objects\n * @param breakpoints the list of configured breakpoint names and their pixel values\n */\nconst createMediaQueries = (breakpoints) => {\n    const sortedBreakpoints = Object.keys(breakpoints).sort((a, b) => breakpoints[b] - breakpoints[a]);\n    return sortedBreakpoints.map((breakpoint, index) => {\n        let query = '';\n        const minWidth = breakpoints[breakpoint];\n        const nextBreakpoint = sortedBreakpoints[index - 1];\n        const maxWidth = nextBreakpoint ? breakpoints[nextBreakpoint] : null;\n        if (minWidth >= 0) {\n            query = `(min-width: ${minWidth}px)`;\n        }\n        if (maxWidth !== null) {\n            if (query) {\n                query += ' and ';\n            }\n            query += `(max-width: ${maxWidth - 1}px)`;\n        }\n        const mediaQuery = {\n            breakpoint,\n            maxWidth: maxWidth ? maxWidth - 1 : null,\n            minWidth,\n            query,\n        };\n        return mediaQuery;\n    });\n};\nexport default createMediaQueries;\n", "import createMediaQueries from './createMediaQueries.js';\n/**\n * Get CSS native media queries for given breakpoint configuration.\n * @param breakpoints the list of configured breakpoint names and their pixel values\n *\n * @example\n * const breakpoints = { mobile: -1, tablet: 768, desktop: 1280 }\n * const queries = getCSSMediaQueries(breakpoints)\n * // {\n   //   \"mobile\": \"@media (max-width: 767px)\",\n   //   \"tablet\": \"@media (min-width: 768px) and (max-width: 1279px)\",\n   //   \"desktop\": \"@media (min-width: 1280px)\",\n   // }\n *\n * @example <caption>Targeting only screen</caption>\n * const breakpoints = { mobile: -1, tablet: 768, desktop: 1280 }\n * const queries = getCSSMediaQueries(breakpoints, 'screen')\n * // {\n   //   \"mobile\": \"@media only screen (max-width: 767px)\",\n   //   \"tablet\": \"@media only screen (min-width: 768px) and (max-width: 1279px)\",\n   //   \"desktop\": \"@media only screen (min-width: 1280px)\",\n   // }\n */\nconst getCSSMediaQueries = (breakpoints, type) => {\n    const typePrefix = type ? `only ${type} and ` : '';\n    const queries = createMediaQueries(breakpoints);\n    return queries.reduce((queries, { breakpoint, query }) => ({\n        ...queries,\n        [breakpoint]: `@media ${typePrefix}${query}`,\n    }), {});\n};\nexport default getCSSMediaQueries;\n", "import { useCallback, useDebugValue, useMemo, useSyncExternalStore, } from 'react';\nimport createMediaQueries from './createMediaQueries.js';\nconst EMPTY_BREAKPOINT = {\n    breakpoint: null,\n    minWidth: null,\n    maxWidth: null,\n    query: null,\n};\n/**\n * A React hook to use the current responsive breakpoint.\n * Will listen to changes using the window.matchMedia API.\n * @param {*} config the list of configured breakpoint names and their pixel values\n * @param {*} [defaultBreakpoint] the optional default breakpoint\n *\n * @example\n * const breakpoints = { mobile: 0, tablet: 768, desktop: 1280 }\n * ...\n * const result = useBreakpoint(breakpoints)\n * // { breakpoint: string; minWidth: number; maxWidth: number | null } | { breakpoint: undefined; minWidth: undefined; maxWidth: undefined }\n *\n * @example <caption>With default value</caption>\n * const breakpoints = { mobile: 0, tablet: 768, desktop: 1280 }\n * ...\n * const result = useBreakpoint(breakpoints, 'mobile')\n * // breakpoint: { breakpoint: string; minWidth: number; maxWidth: number | null }\n */\nconst useBreakpoint = (config, defaultBreakpoint) => {\n    /** Memoize list of calculated media queries from config */\n    const mediaQueries = useMemo(() => createMediaQueries(config), [config]);\n    const subscribe = useCallback((callback) => {\n        const unsubscribers = [];\n        mediaQueries.forEach(({ query }) => {\n            const list = window.matchMedia(query);\n            const supportsNewEventListeners = 'addEventListener' in list && 'removeEventListener' in list;\n            if (supportsNewEventListeners) {\n                list.addEventListener('change', callback);\n            }\n            else {\n                ;\n                list.addListener(callback);\n            }\n            /** Map the unsubscribers array to a list of unsubscriber methods */\n            unsubscribers.push(supportsNewEventListeners\n                ? () => list.removeEventListener('change', callback)\n                : () => list.removeListener(callback));\n        });\n        /** Return a function that when called, will call all unsubscribers */\n        return () => unsubscribers.forEach((unsubscriber) => unsubscriber());\n    }, [mediaQueries]);\n    const getSnapshot = useCallback(() => {\n        const mediaMatch = mediaQueries.find((mediaQuery) => window.matchMedia(mediaQuery.query).matches);\n        if (mediaMatch)\n            return mediaMatch;\n        if (defaultBreakpoint) {\n            const defaultMatch = mediaQueries.find((mediaQuery) => mediaQuery.breakpoint === defaultBreakpoint);\n            if (defaultMatch)\n                return defaultMatch;\n        }\n        return EMPTY_BREAKPOINT;\n    }, [defaultBreakpoint, mediaQueries]);\n    const getServerSnapshot = useCallback(() => {\n        const defaultMatch = mediaQueries.find((mediaQuery) => mediaQuery.breakpoint === defaultBreakpoint);\n        return defaultMatch ?? EMPTY_BREAKPOINT;\n    }, [defaultBreakpoint, mediaQueries]);\n    const currentBreakpoint = useSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n    /** Print a nice debug value for React Devtools */\n    useDebugValue(currentBreakpoint, (c) => typeof c.breakpoint === 'string'\n        ? `${c.breakpoint} (${c.minWidth} ≤ x${c.maxWidth ? ` < ${c.maxWidth + 1}` : ''})`\n        : '');\n    return currentBreakpoint;\n};\nexport default useBreakpoint;\n"], "mappings": ";;;;;;;;AAIA,IAAM,qBAAqB,CAAC,gBAAgB;AACxC,QAAM,oBAAoB,OAAO,KAAK,WAAW,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,IAAI,YAAY,CAAC,CAAC;AACjG,SAAO,kBAAkB,IAAI,CAAC,YAAY,UAAU;AAChD,QAAI,QAAQ;AACZ,UAAM,WAAW,YAAY,UAAU;AACvC,UAAM,iBAAiB,kBAAkB,QAAQ,CAAC;AAClD,UAAM,WAAW,iBAAiB,YAAY,cAAc,IAAI;AAChE,QAAI,YAAY,GAAG;AACf,cAAQ,eAAe,QAAQ;AAAA,IACnC;AACA,QAAI,aAAa,MAAM;AACnB,UAAI,OAAO;AACP,iBAAS;AAAA,MACb;AACA,eAAS,eAAe,WAAW,CAAC;AAAA,IACxC;AACA,UAAM,aAAa;AAAA,MACf;AAAA,MACA,UAAU,WAAW,WAAW,IAAI;AAAA,MACpC;AAAA,MACA;AAAA,IACJ;AACA,WAAO;AAAA,EACX,CAAC;AACL;AACA,IAAO,6BAAQ;;;ACNf,IAAM,qBAAqB,CAAC,aAAa,SAAS;AAC9C,QAAM,aAAa,OAAO,QAAQ,IAAI,UAAU;AAChD,QAAM,UAAU,2BAAmB,WAAW;AAC9C,SAAO,QAAQ,OAAO,CAACA,UAAS,EAAE,YAAY,MAAM,OAAO;AAAA,IACvD,GAAGA;AAAA,IACH,CAAC,UAAU,GAAG,UAAU,UAAU,GAAG,KAAK;AAAA,EAC9C,IAAI,CAAC,CAAC;AACV;AACA,IAAO,6BAAQ;;;AC/Bf,mBAA2E;AAE3E,IAAM,mBAAmB;AAAA,EACrB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AACX;AAmBA,IAAM,gBAAgB,CAAC,QAAQ,sBAAsB;AAEjD,QAAM,mBAAe,sBAAQ,MAAM,2BAAmB,MAAM,GAAG,CAAC,MAAM,CAAC;AACvE,QAAM,gBAAY,0BAAY,CAAC,aAAa;AACxC,UAAM,gBAAgB,CAAC;AACvB,iBAAa,QAAQ,CAAC,EAAE,MAAM,MAAM;AAChC,YAAM,OAAO,OAAO,WAAW,KAAK;AACpC,YAAM,4BAA4B,sBAAsB,QAAQ,yBAAyB;AACzF,UAAI,2BAA2B;AAC3B,aAAK,iBAAiB,UAAU,QAAQ;AAAA,MAC5C,OACK;AACD;AACA,aAAK,YAAY,QAAQ;AAAA,MAC7B;AAEA,oBAAc,KAAK,4BACb,MAAM,KAAK,oBAAoB,UAAU,QAAQ,IACjD,MAAM,KAAK,eAAe,QAAQ,CAAC;AAAA,IAC7C,CAAC;AAED,WAAO,MAAM,cAAc,QAAQ,CAAC,iBAAiB,aAAa,CAAC;AAAA,EACvE,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,kBAAc,0BAAY,MAAM;AAClC,UAAM,aAAa,aAAa,KAAK,CAAC,eAAe,OAAO,WAAW,WAAW,KAAK,EAAE,OAAO;AAChG,QAAI;AACA,aAAO;AACX,QAAI,mBAAmB;AACnB,YAAM,eAAe,aAAa,KAAK,CAAC,eAAe,WAAW,eAAe,iBAAiB;AAClG,UAAI;AACA,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX,GAAG,CAAC,mBAAmB,YAAY,CAAC;AACpC,QAAM,wBAAoB,0BAAY,MAAM;AACxC,UAAM,eAAe,aAAa,KAAK,CAAC,eAAe,WAAW,eAAe,iBAAiB;AAClG,WAAO,gBAAgB;AAAA,EAC3B,GAAG,CAAC,mBAAmB,YAAY,CAAC;AACpC,QAAM,wBAAoB,mCAAqB,WAAW,aAAa,iBAAiB;AAExF,kCAAc,mBAAmB,CAAC,MAAM,OAAO,EAAE,eAAe,WAC1D,GAAG,EAAE,UAAU,KAAK,EAAE,QAAQ,OAAO,EAAE,WAAW,MAAM,EAAE,WAAW,CAAC,KAAK,EAAE,MAC7E,EAAE;AACR,SAAO;AACX;AACA,IAAO,wBAAQ;", "names": ["queries"]}