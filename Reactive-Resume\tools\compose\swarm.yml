version: "3.8"

# In this Docker Compose example, we use Docker Swarm to deploy Reactive Resume on multiple servers, with <PERSON><PERSON><PERSON><PERSON> as the load balancer.
# Ensure that the overlay network is created before deploying this stack. You can do so by running the following command:
# docker network create --driver=overlay --attachable reactive_resume_network

services:
  # Database (Postgres)
  postgres:
    image: postgres:16-alpine
    networks:
      - reactive_resume_network
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure

  # Storage (for image uploads)
  minio:
    image: minio/minio:latest
    command: server /data
    networks:
      - reactive_resume_network
    volumes:
      - minio_data:/data
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
      labels:
        - traefik.enable=true
        - traefik.http.routers.storage.rule=Host(`storage.example.com`)
        - traefik.http.routers.storage.entrypoints=websecure
        - traefik.http.routers.storage.tls.certresolver=letsencrypt
        - traefik.http.services.storage.loadbalancer.server.port=9000

  # Chrome Browser (for printing and previews)
  chrome:
    image: ghcr.io/browserless/chromium:latest
    networks:
      - reactive_resume_network
    environment:
      HEALTH: "true"
      TOKEN: chrome_token
      PROXY_HOST: "printer.example.com"
      PROXY_PORT: 443
      PROXY_SSL: "true"
    deploy:
      replicas: 2
      restart_policy:
        condition: on-failure
      labels:
        - traefik.enable=true
        - traefik.http.routers.printer.rule=Host(`printer.example.com`)
        - traefik.http.routers.printer.entrypoints=websecure
        - traefik.http.routers.printer.tls.certresolver=letsencrypt
        - traefik.http.services.printer.loadbalancer.server.port=3000

  app:
    image: amruthpillai/reactive-resume:latest
    networks:
      - reactive_resume_network
    environment:
      # -- Environment Variables --
      PORT: 3000
      NODE_ENV: production

      # -- URLs --
      PUBLIC_URL: https://example.com
      STORAGE_URL: https://storage.example.com

      # -- Printer (Chrome) --
      CHROME_TOKEN: chrome_token
      CHROME_URL: wss://printer.example.com

      # -- Database (Postgres) --
      DATABASE_URL: ********************************************/postgres

      # -- Auth --
      ACCESS_TOKEN_SECRET: access_token_secret
      REFRESH_TOKEN_SECRET: refresh_token_secret

      # -- Emails --
      MAIL_FROM: <EMAIL>
      # SMTP_URL: smtp://user:pass@smtp:587 # Optional

      # -- Storage (Minio) --
      STORAGE_ENDPOINT: minio
      STORAGE_PORT: 9000
      STORAGE_REGION: us-east-1 # Optional
      STORAGE_BUCKET: default
      STORAGE_ACCESS_KEY: minioadmin
      STORAGE_SECRET_KEY: minioadmin
      STORAGE_USE_SSL: "false"
      STORAGE_SKIP_BUCKET_CHECK: "false"

      # -- Crowdin (Optional) --
      # CROWDIN_PROJECT_ID:
      # CROWDIN_PERSONAL_TOKEN:

      # -- Feature Flags (Optional) --
      # DISABLE_SIGNUPS: "false"
      # DISABLE_EMAIL_AUTH: "false"

      # -- GitHub (Optional) --
      # GITHUB_CLIENT_ID: github_client_id
      # GITHUB_CLIENT_SECRET: github_client_secret
      # GITHUB_CALLBACK_URL: https://example.com/api/auth/github/callback

      # -- Google (Optional) --
      # GOOGLE_CLIENT_ID: google_client_id
      # GOOGLE_CLIENT_SECRET: google_client_secret
      # GOOGLE_CALLBACK_URL: https://example.com/api/auth/google/callback

      # -- OpenID (Optional) --
      # VITE_OPENID_NAME: OpenID
      # OPENID_AUTHORIZATION_URL:
      # OPENID_CALLBACK_URL: https://example.com/api/auth/openid/callback
      # OPENID_CLIENT_ID:
      # OPENID_CLIENT_SECRET:
      # OPENID_ISSUER:
      # OPENID_SCOPE: openid profile email
      # OPENID_TOKEN_URL:
      # OPENID_USER_INFO_URL:
    deploy:
      replicas: 2
      restart_policy:
        condition: on-failure
      labels:
        - traefik.enable=true
        - traefik.http.routers.app.rule=Host(`example.com`)
        - traefik.http.routers.app.entrypoints=websecure
        - traefik.http.routers.app.tls.certresolver=letsencrypt
        - traefik.http.services.app.loadbalancer.server.port=3000

  traefik:
    image: traefik
    command:
      - --api=true
      - --providers.docker=true
      - --providers.docker.swarmMode=true
      - --providers.docker.exposedbydefault=false
      - --providers.docker.network=reactive_resume_network
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json

      # Let's Encrypt Staging Server (for testing)
      # - --certificatesResolvers.letsencrypt.acme.caServer=https://acme-staging-v02.api.letsencrypt.org/directory

      # Redirect all HTTP requests to HTTPS
      - --entrypoints.web.http.redirections.entrypoint.to=websecure
      - --entrypoints.web.http.redirections.entrypoint.scheme=https
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    networks:
      - reactive_resume_network
    volumes:
      - letsencrypt_data:/letsencrypt
      - /var/run/docker.sock:/var/run/docker.sock
    deploy:
      placement:
        constraints: [node.role == manager]

volumes:
  minio_data:
  postgres_data:
  letsencrypt_data:

networks:
  reactive_resume_network:
    external: true
