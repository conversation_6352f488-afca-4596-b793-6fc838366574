{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/sv.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_sv=t(e.dayjs)}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var a=t(e),d={name:\"sv\",weekdays:\"söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag\".split(\"_\"),weekdaysShort:\"sön_mån_tis_ons_tor_fre_lör\".split(\"_\"),weekdaysMin:\"sö_må_ti_on_to_fr_lö\".split(\"_\"),months:\"januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december\".split(\"_\"),monthsShort:\"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec\".split(\"_\"),weekStart:1,yearStart:4,ordinal:function(e){var t=e%10;return\"[\"+e+(1===t||2===t?\"a\":\"e\")+\"]\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY [kl.] HH:mm\",LLLL:\"dddd D MMMM YYYY [kl.] HH:mm\",lll:\"D MMM YYYY HH:mm\",llll:\"ddd D MMM YYYY HH:mm\"},relativeTime:{future:\"om %s\",past:\"för %s sedan\",s:\"några sekunder\",m:\"en minut\",mm:\"%d minuter\",h:\"en timme\",hh:\"%d timmar\",d:\"en dag\",dd:\"%d dagar\",M:\"en månad\",MM:\"%d månader\",y:\"ett år\",yy:\"%d år\"}};return a.default.locale(d,null,!0),d}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,oDAAoD,MAAM,GAAG,GAAE,eAAc,8BAA8B,MAAM,GAAG,GAAE,aAAY,uBAAuB,MAAM,GAAG,GAAE,QAAO,wFAAwF,MAAM,GAAG,GAAE,aAAY,kDAAkD,MAAM,GAAG,GAAE,WAAU,GAAE,WAAU,GAAE,SAAQ,SAASA,IAAE;AAAC,YAAIC,KAAED,KAAE;AAAG,eAAM,MAAIA,MAAG,MAAIC,MAAG,MAAIA,KAAE,MAAI,OAAK;AAAA,MAAG,GAAE,SAAQ,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,eAAc,KAAI,2BAA0B,MAAK,gCAA+B,KAAI,oBAAmB,MAAK,uBAAsB,GAAE,cAAa,EAAC,QAAO,SAAQ,MAAK,gBAAe,GAAE,kBAAiB,GAAE,YAAW,IAAG,cAAa,GAAE,YAAW,IAAG,aAAY,GAAE,UAAS,IAAG,YAAW,GAAE,YAAW,IAAG,cAAa,GAAE,UAAS,IAAG,QAAO,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e", "t"]}