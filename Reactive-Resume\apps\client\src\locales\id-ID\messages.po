msgid ""
msgstr ""
"POT-Creation-Date: 2023-11-10 13:15+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: id\n"
"Project-Id-Version: reactive-resume\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-02-03 09:13\n"
"Last-Translator: \n"
"Language-Team: Indonesian\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: reactive-resume\n"
"X-Crowdin-Project-ID: 503410\n"
"X-Crowdin-Language: id\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 494\n"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:171
msgid "You have enabled two-factor authentication successfully."
msgstr "Anda telah berhasil mengaktifkan autentikasi dua faktor."

#: apps/client/src/pages/home/<USER>/features/index.tsx:57
msgid "{templatesCount} resume templates to choose from"
msgstr "{templatesCount} templat resume yang dapat dipilih"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:142
msgid "{value, plural, one {Column} other {Columns}}"
msgstr "{value, plural, other {Kolom}}"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:20
msgid "<0>I built Reactive Resume mostly by myself during my spare time, with a lot of help from other great open-source contributors.</0><1>If you like the app and want to support keeping it free forever, please donate whatever you can afford to give.</1>"
msgstr "<0>Saya membuat Reactive Resume sendiri seringnya di waktu senggang, dengan banyak bantuan dari kontributor open source hebat lainnya.</0> <1>Jika Anda menyukai aplikasi ini dan ingin mendukung agar aplikasi ini tetap gratis selamanya, silakan sumbangkan berapa pun yang Anda mampu berikan.</1>"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:51
msgid "<0>I'm sure the app is not perfect, but I'd like for it to be.</0><1>If you faced any issues while creating your resume, or have an idea that would help you and other users in creating your resume more easily, drop an issue on the repository or send me an email about it.</1>"
msgstr "<0>Saya yakin aplikasi ini belum sempurna, tetapi saya ingin aplikasi ini sempurna.</0><1>Jika Anda menghadapi masalah apa pun saat membuat resume, atau memiliki ide yang dapat membantu Anda dan pengguna lain dalam membuat resume dengan lebih mudah, kirimkan masalah ke repositori atau kirimkan email kepada saya.</1>"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:201
msgid "<0>Note: </0>By utilizing the OpenAI API, you acknowledge and accept the <1>terms of use</1> and <2>privacy policy</2> outlined by OpenAI. Please note that Reactive Resume bears no responsibility for any improper or unauthorized utilization of the service, and any resulting repercussions or liabilities solely rest on the user."
msgstr "<0>Catatan: </0>Dengan menggunakan API OpenAI, Anda mengakui dan menerima <1>persyaratan penggunaan</1> dan <2>kebijakan privasi</2> yang diuraikan oleh OpenAI. Harap diperhatikan bahwa Reactive Resume tidak bertanggung jawab atas penggunaan layanan yang tidak tepat atau tidak sah, dan segala dampak atau kewajiban yang timbul sepenuhnya menjadi tanggung jawab pengguna."

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:85
msgid "<0>The community has spent a lot of time writing the documentation for Reactive Resume, and I'm sure it will help you get started with the app.</0><1>There are also a lot of examples to help you get started, and features that you might not know about which could help you build your perfect resume.</1>"
msgstr "<0>Komunitas ini telah menghabiskan banyak waktu untuk menulis dokumentasi untuk Reactive Resume, dan saya yakin ini akan membantu Anda mulai menggunakan aplikasi ini.</0><1>Ada juga banyak contoh untuk membantu Anda memulai, dan fitur-fitur yang mungkin tidak Anda ketahui yang dapat membantu Anda membuat resume yang sempurna.</1>"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:140
msgid "<0>Two-factor authentication is currently disabled.</0> You can enable it by adding an authenticator app to your account."
msgstr "<0>Autentikasi dua faktor saat ini dinonaktifkan.</0> Anda dapat mengaktifkannya dengan menambahkan aplikasi pengautentikasi ke akun Anda."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:133
msgid "<0>Two-factor authentication is enabled.</0> You will be asked to enter a code every time you sign in."
msgstr "<0>Autentikasi dua faktor diaktifkan.</0> Anda akan diminta untuk memasukkan kode setiap kali masuk."

#: apps/client/src/pages/home/<USER>
#: apps/client/src/pages/home/<USER>/hero/index.tsx:40
msgid "A free and open-source resume builder"
msgstr "Pembuat resume gratis dan bersumber terbuka"

#: apps/client/src/pages/home/<USER>/footer.tsx:21
#: apps/client/src/pages/home/<USER>/hero/index.tsx:45
msgid "A free and open-source resume builder that simplifies the process of creating, updating, and sharing your resume."
msgstr "Pembuat resume gratis dan bersumber terbuka yang menyederhanakan proses pembuatan, pembaruan, dan berbagi resume Anda."

#: apps/client/src/pages/builder/_components/toolbar.tsx:59
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:29
msgid "A link has been copied to your clipboard."
msgstr "Tautan telah disalin ke papan klip Anda."

#: apps/client/src/components/copyright.tsx:29
msgid "A passion project by <0>Amruth Pillai</0>"
msgstr "Sebuah proyek penuh semangat dari <0>Amruth Pillai</0>"

#: apps/client/src/pages/auth/forgot-password/page.tsx:57
msgid "A password reset link should have been sent to your inbox, if an account existed with the email you provided."
msgstr "Tautan pengaturan ulang kata sandi seharusnya sudah dikirim ke kotak masuk Anda, jika ada akun dengan email yang Anda berikan."

#: apps/client/src/services/errors/translate-error.ts:43
msgid "A resume with this slug already exists, please pick a different unique identifier."
msgstr "Resume dengan slug ini sudah ada, silakan pilih pengenal unik yang berbeda."

#: apps/client/src/services/errors/translate-error.ts:10
msgid "A user with this email address and/or username already exists."
msgstr "Pengguna dengan alamat email dan/atau nama pengguna ini sudah ada."

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:43
msgid "A4"
msgstr "A4"

#. Helper text to let the user know what filetypes are accepted. {accept} can be .pdf or .json.
#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:270
msgid "Accepts only {accept} files"
msgstr "Hanya menerima file {accept}"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:105
msgid "Account"
msgstr "Akun"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:175
msgid "Add a custom field"
msgstr "Tambahkan kolom kustom"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-base.tsx:119
#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-base.tsx:170
msgctxt "For example, add a new work experience, or add a new profile."
msgid "Add a new item"
msgstr "Tambah item baru"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:91
msgid "Add a new item"
msgstr "Tambah item baru"

#: apps/client/src/pages/builder/sidebars/left/index.tsx:146
#: apps/client/src/pages/builder/sidebars/left/index.tsx:263
msgid "Add a new section"
msgstr "Tambahkan bagian baru"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:261
msgid "Add New Page"
msgstr "Tambah halaman baru"

#: apps/client/src/components/ai-actions.tsx:79
msgid "AI"
msgstr "Kecerdasan buatan"

#: apps/client/src/pages/auth/register/page.tsx:71
msgid "Already have an account?"
msgstr "Sudah memiliki akun?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:144
msgid "An error occurred while validating the file."
msgstr "Terjadi kesalahan saat memvalidasi file."

#: apps/client/src/pages/public/error.tsx:23
msgid "An internal server error occurred."
msgstr ""

#: apps/client/src/pages/public/error.tsx:32
msgid "An unexpected error occurred."
msgstr ""

#: apps/client/src/pages/home/<USER>/features/index.tsx:134
msgid "and many more..."
msgstr "dan masih banyak lagi..."

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:57
msgid "Anyone with the link can view and download the resume."
msgstr "Siapa pun yang memiliki tautan dapat melihat dan mengunduh resume."

#: apps/client/src/pages/builder/_components/toolbar.tsx:60
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:30
msgid "Anyone with this link can view and download the resume. Share it on your profile or with recruiters."
msgstr "Siapa pun yang memiliki tautan ini dapat melihat dan mengunduh resume tesebut. Bagikan di profil Anda atau dengan para perekrut."

#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:41
msgid "Apply Custom CSS"
msgstr "Menerapkan CSS Khusus"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:128
msgid "Are you sure you want to delete this item?"
msgstr "Apakah Anda yakin Anda ingin menghapus item ini?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:149
msgid "Are you sure you want to delete your resume?"
msgstr "Apakah Anda yakin ingin menghapus resume Anda?"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:125
msgid "Are you sure you want to disable two-factor authentication?"
msgstr "Apakah Anda yakin ingin menonaktifkan autentikasi dua faktor?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:38
msgid "Are you sure you want to lock this resume?"
msgstr "Apakah Anda yakin ingin mengunci resume ini?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:39
msgid "Are you sure you want to unlock this resume?"
msgstr "Apakah Anda yakin ingin membuka resume ini?"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:94
msgid "Are you sure?"
msgstr "Apakah Anda yakin?"

#. For example, Computer Science or Business Administration
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:73
msgid "Area of Study"
msgstr "Area belajar"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:79
msgid "Aspect Ratio"
msgstr "Rasio Aspek"

#: apps/client/src/pages/home/<USER>/features/index.tsx:51
msgid "Available in {languagesCount} languages"
msgstr "Tersedia dalam {languagesCount} bahasa"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:53
msgid "Awarder"
msgstr "Pemberi penghargaan"

#: apps/client/src/pages/auth/backup-otp/page.tsx:99
#: apps/client/src/pages/auth/forgot-password/page.tsx:100
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:243
msgid "Back"
msgstr "Kembali"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:73
msgid "Background Color"
msgstr "Warna Latar Belakang"

#: apps/client/src/pages/auth/backup-otp/page.tsx:75
msgid "Backup Code"
msgstr "Kode Cadangan"

#: apps/client/src/pages/auth/backup-otp/page.tsx:81
msgid "Backup Codes may contain only lowercase letters or numbers, and must be exactly 10 characters."
msgstr "Kode Cadangan hanya boleh berisi huruf kecil atau angka, dan harus tepat 10 karakter."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:132
msgid "Base URL"
msgstr "URL dasar"

#: apps/client/src/pages/builder/sidebars/left/index.tsx:55
msgctxt "The basics section of a resume consists of User's Picture, Full Name, Location etc."
msgid "Basics"
msgstr "Dasar-dasar"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:21
msgid "Basics"
msgstr "Dasar-dasar"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:191
msgid "Border"
msgstr "Perbatasan"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:124
msgid "Border Radius"
msgstr "Radius Perbatasan"

#: apps/client/src/pages/public/page.tsx:93
msgid "Built with"
msgstr "Dibangun dengan"

#: apps/client/src/components/copyright.tsx:27
#: apps/client/src/pages/home/<USER>/contributors/index.tsx:20
msgid "By the community, for the community."
msgstr "Oleh komunitas, untuk komunitas."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:135
#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:49
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:156
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:137
msgid "Cancel"
msgstr "Batalkan"

#: apps/client/src/components/ai-actions.tsx:103
#: apps/client/src/components/ai-actions.tsx:106
msgid "Casual"
msgstr "Santai"

#: apps/client/src/pages/builder/_components/toolbar.tsx:130
msgid "Center Artboard"
msgstr "Papan Seni Tengah"

#: apps/client/src/pages/auth/reset-password/page.tsx:99
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:115
msgid "Change Password"
msgstr "Ubah Kata Sandi"

#: apps/client/src/components/ai-actions.tsx:97
msgid "Change Tone"
msgstr "Ubah Nada"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:186
msgid "Changed your mind about the name? Give it a new one."
msgstr "Berubah pikiran tentang nama? Berikan nama yang baru."

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:70
msgid "Check your email for the confirmation link to update your email address."
msgstr "Periksa email Anda untuk mendapatkan tautan konfirmasi untuk memperbarui alamat email Anda."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:144
msgid "Circle"
msgstr "Lingkaran"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:249
msgid "Close"
msgstr "Tutup"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:201
msgid "Code"
msgstr "Kode"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:52
msgid "Code must be exactly 6 digits long."
msgstr "Kode harus terdiri dari 6 digit."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:136
msgid "Columns"
msgstr "Kolom"

#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:39
msgid "Company"
msgstr "Perusahaan"

#: apps/client/src/components/ai-actions.tsx:115
#: apps/client/src/components/ai-actions.tsx:118
msgid "Confident"
msgstr "Percaya diri"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:234
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:246
msgid "Continue"
msgstr "Lanjutkan"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:96
msgid "Copy"
msgstr "Salin"

#: apps/client/src/pages/builder/_components/toolbar.tsx:164
msgid "Copy Link to Resume"
msgstr "Salin Tautan ke Resume"

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:78
msgid "Copy to Clipboard"
msgstr "Menyalin ke Papan Klip"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:179
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:244
msgid "Create"
msgstr "Membuat"

#: apps/client/src/pages/auth/register/page.tsx:64
#: apps/client/src/pages/auth/register/page.tsx:69
msgid "Create a new account"
msgstr "Membuat akun baru"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:163
msgid "Create a new item"
msgstr "Membuat item baru"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:178
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/create-card.tsx:28
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/create-item.tsx:18
msgid "Create a new resume"
msgstr "Buat resume baru"

#: apps/client/src/pages/auth/login/page.tsx:65
msgctxt "This is a link to create a new account"
msgid "Create one now"
msgstr "Buat satu sekarang"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:259
msgid "Create Sample Resume"
msgstr "Buat Contoh Resume"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:82
msgid "Current Password"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/index.tsx:93
#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:27
#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:28
msgid "Custom CSS"
msgstr "CSS khusus"

#: apps/client/src/pages/home/<USER>/features/index.tsx:62
msgid "Custom resume sections"
msgstr "Bagian resume khusus"

#: apps/client/src/stores/resume.ts:47
msgid "Custom Section"
msgstr "Bagian Khusus"

#: apps/client/src/pages/home/<USER>/features/index.tsx:60
msgid "Customisable colour palettes"
msgstr "Palet warna yang dapat disesuaikan"

#: apps/client/src/pages/home/<USER>/features/index.tsx:61
msgid "Customisable layouts"
msgstr "Tata letak yang dapat disesuaikan"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:62
msgid "Danger Zone"
msgstr "Zona Bahaya"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:87
msgid "Dark"
msgstr "Gelap"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:67
msgid "Date"
msgstr "Tanggal"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:110
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:72
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:67
msgid "Date or Date Range"
msgstr "Tanggal atau Rentang Tanggal"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:137
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:158
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:121
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:127
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:172
msgid "Delete"
msgstr "Menghapus"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:79
#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:94
msgid "Delete Account"
msgstr "Menghapus Akun"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:73
#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:50
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:53
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:63
msgid "Description"
msgstr "Deskripsi"

#: apps/client/src/pages/home/<USER>/features/index.tsx:58
msgid "Design single/multi page resumes"
msgstr "Desain resume satu / beberapa halaman"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:139
msgid "Disable"
msgstr "Nonaktifkan"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:154
msgid "Disable 2FA"
msgstr "Nonaktifkan 2FA"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:302
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:220
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:134
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:118
msgid "Discard"
msgstr "Buang"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:105
msgid "Documentation"
msgstr "Dokumentasi"

#: apps/client/src/pages/auth/login/page.tsx:62
msgid "Don't have an account?"
msgstr "Belum memiliki akun?"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:83
msgid "Don't know where to begin? Hit the docs!"
msgstr "Tidak tahu mulai dari mana? Buka dokumen!"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:107
msgid "Don't see your language? <0>Help translate the app.</0>"
msgstr "Tidak melihat bahasa Anda? <0>Bantu menerjemahkan aplikasi.</0>"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:40
msgid "Donate to Reactive Resume"
msgstr "Sumbangan ke Reactive Resume"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:56
msgid "Download a JSON snapshot of your resume. This file can be used to import your resume in the future, or can even be shared with others to collaborate."
msgstr "Unduh cuplikan JSON dari resume Anda. File ini dapat digunakan untuk mengimpor resume Anda di masa mendatang, atau bahkan dapat dibagikan dengan orang lain untuk berkolaborasi."

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:74
msgid "Download a PDF of your resume. This file can be used to print your resume, send it to recruiters, or upload on job portals."
msgstr "Unduh file PDF resume Anda. File ini dapat digunakan untuk mencetak resume Anda, mengirimkannya ke perekrut, atau mengunggahnya di portal pekerjaan."

#: apps/client/src/pages/builder/_components/toolbar.tsx:176
msgid "Download PDF"
msgstr "Unduh PDF"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:58
msgid "Downloads"
msgstr "Unduhan"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:181
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:246
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:105
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:95
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:156
msgid "Duplicate"
msgstr "Duplikat"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:165
msgid "Duplicate an existing item"
msgstr "Menduplikasi item yang sudah ada"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:180
msgid "Duplicate an existing resume"
msgstr "Menggandakan resume yang sudah ada"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:92
msgid "Edit"
msgstr "Sunting"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:169
msgid "Effects"
msgstr "Efek"

#: apps/client/src/pages/auth/forgot-password/page.tsx:82
#: apps/client/src/pages/auth/login/page.tsx:90
#: apps/client/src/pages/auth/register/page.tsx:141
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:54
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:181
msgid "Email"
msgstr "Email"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:163
msgid "Enable 2FA"
msgstr "Aktifkan 2FA"

#: apps/client/src/pages/auth/reset-password/page.tsx:67
msgid "Enter a new password below, and make sure it's secure."
msgstr "Masukkan kata sandi baru di bawah ini, dan pastikan kata sandi tersebut aman."

#: apps/client/src/pages/auth/backup-otp/page.tsx:59
msgid "Enter one of the 10 backup codes you saved when you enabled two-factor authentication."
msgstr "Masukkan salah satu dari 10 kode cadangan yang Anda simpan saat mengaktifkan autentikasi dua faktor."

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:63
msgid "Enter Phosphor Icon"
msgstr "Masukkan Ikon Phosphor"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:170
msgid "Enter the 6-digit code from your authenticator app to verify that 2FA has been setup correctly."
msgstr "Masukkan kode 6 digit dari aplikasi autentikator Anda untuk memverifikasi bahwa 2FA telah diatur dengan benar."

#: apps/client/src/pages/auth/verify-otp/page.tsx:60
msgid "Enter the one-time password provided by your authenticator app below."
msgstr "Masukkan kata sandi sekali pakai yang disediakan oleh aplikasi autentikator Anda di bawah ini."

#: apps/client/src/pages/auth/forgot-password/page.tsx:70
msgid "Enter your email address and we will send you a link to reset your password if the account exists."
msgstr "Masukkan alamat email Anda dan kami akan mengirimkan tautan untuk mengatur ulang kata sandi Anda jika akun tersebut ada."

#: apps/client/src/pages/public/error.tsx:46
msgid "Error {statusCode}"
msgstr ""

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:283
msgid "Errors"
msgstr "Kesalahan"

#: apps/client/src/pages/home/<USER>/support/index.tsx:78
msgid "Even if you're not in a position to contribute financially, you can still make a difference by giving the GitHub repository a star, spreading the word to your friends, or dropping a quick message to let me know how Reactive Resume has helped you. Your feedback and support are always welcome and much appreciated!"
msgstr "Meskipun Anda belum bisa berkontribusi secara finansial, Anda masih bisa berkontribusi dengan cara memberikan bintang pada repositori GitHub, menyebarkannya ke teman-teman Anda, atau mengirimkan pesan singkat kepada saya bagaimana Reactive Resume telah membantu Anda. Umpan balik dan dukungan Anda selalu diterima dan sangat kami hargai!"

#: apps/client/src/pages/home/<USER>/templates/index.tsx:12
msgid "Explore the templates available in Reactive Resume and view the resumes crafted with them. They could also serve as examples to help guide the creation of your next resume."
msgstr "Jelajahi templat yang tersedia di Reactive Resume dan melihat resume yang dibuat dengan templat tersebut. Templat ini juga dapat digunakan sebagai contoh untuk membantu dalam pembuatan resume Anda."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:121
#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:39
#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:40
msgid "Export"
msgstr "Ekspor"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:255
msgid "File"
msgstr "Mengajukan"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:221
msgid "Filetype"
msgstr "Jenis file"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:38
msgid "Finally,"
msgstr "Akhirnya,"

#: apps/client/src/components/ai-actions.tsx:90
msgid "Fix Spelling & Grammar"
msgstr "Perbaiki Ejaan & Tata Bahasa"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:107
msgid "Font Family"
msgstr "Keluarga Font"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:148
msgid "Font Size"
msgstr "Ukuran Font"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:122
msgid "Font Subset"
msgstr "Subset Font"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:134
msgid "Font Variants"
msgstr "Varian Font"

#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:35
msgid "For example, information regarding which companies you sent this resume to or the links to the job descriptions can be noted down here."
msgstr "Misalnya, informasi mengenai perusahaan mana yang Anda kirim resume tersebut atau tautan ke deskripsi pekerjaan dapat dicatat di sini."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:182
msgid "Forget"
msgstr "Lupakan."

#: apps/client/src/pages/auth/login/page.tsx:131
msgid "Forgot Password?"
msgstr "Lupa Kata Sandi?"

#: apps/client/src/pages/auth/forgot-password/page.tsx:68
msgid "Forgot your password?"
msgstr "Lupa kata sandi Anda?"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:32
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:40
msgid "Format"
msgstr "Format"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:49
msgid "Found a bug, or have an idea for a new feature?"
msgstr "Ada bug, atau ide untuk fitur baru?"

#: apps/client/src/pages/home/<USER>/features/index.tsx:46
msgid "Free, forever"
msgstr "Gratis, selamanya"

#: apps/client/src/components/ai-actions.tsx:121
#: apps/client/src/components/ai-actions.tsx:124
msgid "Friendly"
msgstr "Ramah"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:31
msgid "Full Name"
msgstr "Nama Lengkap"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:202
msgid "Generate a random title for your resume"
msgstr "Buat judul acak untuk resume Anda"

#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:32
msgid "Get Started"
msgstr "Memulai"

#: apps/client/src/pages/auth/_components/social-auth.tsx:18
msgid "GitHub"
msgstr "GitHub"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:12
msgid "GitHub Stars"
msgstr "Bintang GitHub"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:187
msgid "Give your old resume a new name."
msgstr "Berikan nama baru pada resume lama Anda."

#: apps/client/src/pages/auth/verify-email/page.tsx:67
#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:18
msgid "Go to Dashboard"
msgstr "Buka Dasbor"

#: apps/client/src/pages/public/error.tsx:55
msgid "Go to home"
msgstr ""

#: apps/client/src/pages/auth/_components/social-auth.tsx:31
msgid "Google"
msgstr "Google"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:202
msgid "Grayscale"
msgstr "Skala abu-abu"

#: apps/client/src/pages/dashboard/resumes/page.tsx:43
msgid "Grid"
msgstr "Grid"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:43
msgid "Headline"
msgstr "Judul"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:107
msgid "Here, you can update your account information such as your profile picture, name and username."
msgstr "Di sini, Anda dapat memperbarui informasi akun Anda seperti gambar profil, nama, dan nama pengguna."

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:68
msgid "Here, you can update your profile to customize and personalize your experience."
msgstr "Di sini, Anda dapat memperbarui profil Anda untuk menyesuaikan dan mempersonalisasi pengalaman Anda."

#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:80
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:94
#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:180
msgid "Hidden"
msgstr "Tersembunyi"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:106
msgid "Hide"
msgstr "Sembunyikan"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:192
msgid "Hide Icons"
msgstr "Sembunyikan Ikon"

#: apps/client/src/pages/auth/login/page.tsx:115
#: apps/client/src/pages/auth/register/page.tsx:168
#: apps/client/src/pages/auth/reset-password/page.tsx:88
msgid "Hold <0>Ctrl</0> to display your password temporarily."
msgstr "Tahan <0>Ctrl</0> untuk menampilkan kata sandi Anda sementara."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:93
msgid "Horizontal"
msgstr "Horisontal"

#: apps/client/src/pages/home/<USER>/features/index.tsx:67
msgid "Host your resume publicly"
msgstr "Tampilkan resume Anda secara publik"

#: apps/client/src/pages/home/<USER>/testimonials/index.tsx:70
msgid "I always love to hear from the users of Reactive Resume with feedback or support. Here are some of the messages I've received. If you have any feedback, feel free to drop me an email at <0>{email}</0>."
msgstr "Saya selalu senang mendengar dari para pengguna Reactive Resume dengan umpan balik atau dukungan. Berikut adalah beberapa pesan yang saya terima. Jika Anda memiliki umpan balik, jangan ragu untuk mengirimi saya email di <0>{email}</0>."

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:83
#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:53
msgid "Icon"
msgstr "Ikon"

#: apps/client/src/pages/home/<USER>/logo-cloud/index.tsx:47
msgid "If this app has helped you with your job hunt, let me know by reaching out through <0>this contact form</0>."
msgstr "Jika aplikasi ini telah membantu Anda dalam mencari pekerjaan, beri tahu saya dengan menghubungi saya melalui <0>formulir kontak ini</0>."

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:128
msgid "If you disable two-factor authentication, you will no longer be required to enter a verification code when logging in."
msgstr "Jika Anda menonaktifkan autentikasi dua faktor, Anda tidak akan lagi diminta untuk memasukkan kode verifikasi saat masuk."

#: apps/client/src/pages/home/<USER>/support/index.tsx:59
msgid "If you're multilingual, we'd love your help in bringing the app to more languages and communities. Don't worry if you don't see your language on the list - just give me a shout-out on GitHub, and I'll make sure to include it. Ready to get started? Jump into translation over at Crowdin by clicking the link below."
msgstr "Jika Anda memliki kemampuan multibahasa, kami akan sangat senang jika Anda bisa membantu kami menghadirkan aplikasi ini ke lebih banyak bahasa dan komunitas. Jangan khawatir jika Anda tidak melihat bahasa Anda dalam daftar - cukup beri tahu saya di GitHub, dan saya akan memastikan untuk memasukkannya. Siap untuk memulai? Langsung saja mulai menerjemahkan di Crowdin dengan mengeklik tautan di bawah ini."

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:309
msgid "Import"
msgstr "Impor"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:208
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/import-card.tsx:28
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/import-item.tsx:17
msgid "Import an existing resume"
msgstr "Mengimpor resume yang sudah ada"

#: apps/client/src/components/ai-actions.tsx:85
msgid "Improve Writing"
msgstr "Meningkatkan kualitasPenulisan"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:188
msgid "In case you are unable to scan this QR Code, you can also copy-paste this link into your authenticator app."
msgstr "Jika Anda tidak dapat memindai Kode QR ini, Anda juga dapat menyalin dan tempel tautan ini ke dalam aplikasi autentikator Anda."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:67
msgid "In this section, you can change your password and enable/disable two-factor authentication."
msgstr "Pada bagian ini, Anda dapat mengubah kata sandi dan mengaktifkan/menonaktifkan autentikasi dua faktor."

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:64
msgid "In this section, you can delete your account and all the data associated to your user, but please keep in mind that <0>this action is irreversible</0>."
msgstr "Pada bagian ini, Anda dapat menghapus akun dan semua data yang terkait dengan pengguna Anda, tetapi harap diingat bahwa <0>tindakan ini tidak dapat dibatalkan</0>."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:135
#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:116
#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:117
msgid "Information"
msgstr "Informasi"

#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:39
msgid "Institution"
msgstr "Institusi"

#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:53
msgid "Issuer"
msgstr "Penerbit"

#: apps/client/src/services/errors/translate-error.ts:7
msgid "It doesn't look like a user exists with the credentials you provided."
msgstr "Sepertinya tidak ada pengguna dengan kredensial yang Anda berikan."

#: apps/client/src/services/errors/translate-error.ts:37
msgid "It looks like the backup code you provided is invalid or used. Please try again."
msgstr "Sepertinya kode cadangan yang Anda berikan tidak valid atau sudah digunakan. Silakan coba lagi."

#: apps/client/src/services/errors/translate-error.ts:19
msgid "It looks like the reset token you provided is invalid. Please try restarting the password reset process again."
msgstr "Sepertinya token pengaturan ulang yang Anda berikan tidak valid. Coba mulai kembali proses pengaturan ulang kata sandi."

#: apps/client/src/services/errors/translate-error.ts:46
msgid "It looks like the resume you're looking for doesn't exist."
msgstr "Sepertinya resume yang Anda cari tidak ada."

#: apps/client/src/services/errors/translate-error.ts:34
msgid "It looks like the two-factor authentication code you provided is invalid. Please try again."
msgstr "Sepertinya kode autentikasi dua faktor yang Anda berikan tidak valid. Silakan coba lagi."

#: apps/client/src/services/errors/translate-error.ts:22
msgid "It looks like the verification token you provided is invalid. Please try restarting the verification process again."
msgstr "Sepertinya token verifikasi yang Anda berikan tidak valid. Silakan coba memulai kembali proses verifikasi."

#: apps/client/src/services/errors/translate-error.ts:25
msgid "It looks like your email address has already been verified."
msgstr "Sepertinya alamat email Anda sudah diverifikasi."

#: apps/client/src/pages/auth/register/page.tsx:101
msgctxt "Localized version of a placeholder name. For example, Max Mustermann in German or Jan Kowalski in Polish."
msgid "John Doe"
msgstr "John Doe"

#: apps/client/src/pages/auth/register/page.tsx:123
msgctxt "Localized version of a placeholder username. For example, max.mustermann in German or jan.kowalski in Polish."
msgid "john.doe"
msgstr "john.doe"

#: apps/client/src/pages/auth/register/page.tsx:145
msgctxt "Localized version of a placeholder email. For example, <EMAIL> in <NAME_EMAIL> in Polish."
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:54
msgid "JSON"
msgstr "JSON"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:159
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:63
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:159
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:109
msgid "Keywords"
msgstr "Kata kunci"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:42
#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:52
msgid "Label"
msgstr "Label"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:101
msgid "Language"
msgstr "Bahasa"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:83
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:139
msgid "Last updated {lastUpdated}"
msgstr "Terakhir diperbarui {lastUpdated}"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:72
#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:197
#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:198
msgid "Layout"
msgstr "Tata letak"

#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:38
msgid "Learn more"
msgstr "Pelajari lebih lanjut"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:44
msgid "Letter"
msgstr "Surat"

#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:64
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:77
msgid "Level"
msgstr "Tingkat"

#: apps/client/src/components/copyright.tsx:16
msgid "Licensed under <0>MIT</0>"
msgstr "Berlisensi <0>MIT</0>"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:86
msgid "Light"
msgstr "Cahaya"

#: apps/client/src/pages/home/<USER>/features/index.tsx:69
msgid "Light or dark theme"
msgstr "Tema terang atau gelap"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:165
msgid "Line Height"
msgstr "Tinggi Garis"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/import-card.tsx:33
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/import-item.tsx:22
msgid "LinkedIn, JSON Resume, etc."
msgstr "LinkedIn, Resume JSON, dll."

#: apps/client/src/pages/dashboard/resumes/page.tsx:47
msgid "List"
msgstr "Daftar"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:86
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:81
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:93
msgid "Location"
msgstr "Lokasi"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:51
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:115
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:115
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:166
msgid "Lock"
msgstr "Kunci"

#: apps/client/src/pages/home/<USER>/features/index.tsx:64
msgid "Lock a resume to prevent editing"
msgstr "Mengunci resume untuk mencegah pengeditan"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:43
msgid "Locking a resume will prevent any further changes to it. This is useful when you have already shared your resume with someone and you don't want to accidentally make any changes to it."
msgstr "Mengunci resume akan mencegah perubahan lebih lanjut pada resume tersebut. Hal ini berguna ketika Anda telah membagikan resume Anda kepada seseorang dan Anda tidak ingin membuat perubahan secara tidak sengaja."

#: apps/client/src/components/user-options.tsx:38
#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:23
msgid "Logout"
msgstr "Keluar"

#: apps/client/src/pages/auth/verify-otp/page.tsx:64
msgid "Lost your device?"
msgstr "Kehilangan perangkat Anda?"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:247
msgid "Main"
msgstr "Utama"

#: apps/client/src/pages/home/<USER>/features/index.tsx:59
msgid "Manage multiple resumes"
msgstr "Mengelola beberapa resume"

#. The month and year should be uniform across all languages.
#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:71
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:69
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:69
msgid "March 2023"
msgstr "Maret 2023"

#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:112
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:74
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:103
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:69
msgid "March 2023 - Present"
msgstr "Maret 2023 - Sekarang"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:50
msgid "Margin"
msgstr "Margin"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:158
msgid "Max Tokens"
msgstr "Token Max"

#: apps/client/src/pages/home/<USER>/features/index.tsx:48
msgid "MIT License"
msgstr "Lisensi MIT"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:145
msgid "Model"
msgstr "Model"

#: apps/client/src/pages/auth/register/page.tsx:98
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:59
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:48
#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:36
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:73
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:39
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:39
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:49
#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:88
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:153
msgid "Name"
msgstr "Nama"

#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:39
msgctxt "Name of the Certification"
msgid "Name"
msgstr "Nama"

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:40
msgid "Network"
msgstr "Jaringan"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:96
msgid "New Password"
msgstr "Kata Sandi Baru"

#: apps/client/src/components/locale-combobox.tsx:45
msgid "No results found"
msgstr "Tidak ada hasil yang ditemukan"

#: apps/client/src/pages/home/<USER>/features/index.tsx:49
msgid "No user tracking or advertising"
msgstr "Tidak ada pelacakan pengguna atau iklan"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:133
msgid "Note: This will make your account less secure."
msgstr "Catatan: Hal ini akan membuat akun Anda kurang aman."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:128
#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:16
#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:17
msgid "Notes"
msgstr "Catatan"

#: apps/client/src/pages/auth/verify-otp/page.tsx:82
msgid "One-Time Password"
msgstr "Kata Sandi Sekali Pakai"

#: apps/client/src/components/ai-actions.tsx:56
#: apps/client/src/libs/axios.ts:30
#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:188
#: apps/client/src/services/resume/print.tsx:26
msgid "Oops, the server returned an error."
msgstr "Ups, server mengembalikan kesalahan."

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:97
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:77
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:148
msgid "Open"
msgstr "Buka"

#: apps/client/src/pages/home/<USER>/features/index.tsx:47
msgid "Open Source"
msgstr "Sumber Terbuka"

#: apps/client/src/services/openai/change-tone.ts:35
#: apps/client/src/services/openai/fix-grammar.ts:33
#: apps/client/src/services/openai/improve-writing.ts:33
msgid "OpenAI did not return any choices for your text."
msgstr "OpenAI tidak mengembalikan pilihan apa pun untuk teks Anda."

#: apps/client/src/pages/home/<USER>/features/index.tsx:52
msgid "OpenAI Integration"
msgstr "Integrasi OpenAI"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:119
msgid "OpenAI/Ollama API Key"
msgstr "Kunci API OpenAI/Ollama"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:79
msgid "OpenAI/Ollama Integration"
msgstr "Integrasi OpenAI/Ollama"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:67
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:182
msgid "Options"
msgstr "Pilihan"

#: apps/client/src/pages/auth/layout.tsx:47
msgctxt "The user can either login with email/password, or continue with GitHub or Google."
msgid "or continue with"
msgstr "atau lanjutkan dengan"

#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:39
msgid "Organization"
msgstr "Organisasi"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:100
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:25
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:26
msgid "Page"
msgstr "Halaman"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:228
msgid "Page {pageNumber}"
msgstr "Halaman {pageNumber}"

#: apps/client/src/pages/auth/login/page.tsx:110
#: apps/client/src/pages/auth/register/page.tsx:163
#: apps/client/src/pages/auth/reset-password/page.tsx:83
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:73
msgid "Password"
msgstr "Kata sandi"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:72
msgid "PDF"
msgstr "PDF"

#: apps/client/src/pages/home/<USER>/features/index.tsx:63
msgid "Personal notes for each resume"
msgstr "Catatan pribadi untuk setiap resume"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:81
msgid "Phone"
msgstr "Telepon"

#: apps/client/src/pages/auth/layout.tsx:76
msgid "Photograph by Patrick Tomasso"
msgstr "Foto oleh Patrick Tomasso"

#: apps/client/src/pages/home/<USER>/features/index.tsx:66
msgid "Pick any font from Google Fonts"
msgstr "Pilih font apa pun dari Google Fonts"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/section.tsx:69
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:121
msgid "Picture"
msgstr "Gambar"

#: apps/client/src/pages/auth/verify-email/page.tsx:59
msgid "Please note that this step is completely optional."
msgstr "Harap diperhatikan, bahwa langkah ini sepenuhnya opsional."

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:225
msgid "Please select a file type"
msgstr "Pilih jenis file"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:228
msgid "Please store your backup codes in a secure location. You can use one of these one-time use codes to login in case you lose access to your authenticator app."
msgstr "Simpanlah kode cadangan Anda di tempat yang aman. Anda dapat menggunakan salah satu dari kode sekali pakai ini untuk masuk jika Anda kehilangan akses ke aplikasi autentikator."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:99
msgid "Portrait"
msgstr "Potret"

#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:54
msgctxt "Position held at a company, for example, Software Engineer"
msgid "Position"
msgstr "Posisi"

#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:53
msgid "Position"
msgstr "Posisi"

#: apps/client/src/pages/home/<USER>/features/index.tsx:96
msgid "Powered by"
msgstr "Didukung oleh"

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:94
msgid "Powered by <0>Simple Icons</0>"
msgstr "Didukung oleh <0>Simple Icons</0>"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:43
msgid "Primary Color"
msgstr "Warna Primer"

#: apps/client/src/pages/home/<USER>/footer.tsx:50
msgid "Privacy Policy"
msgstr "Kebijakan Privasi"

#: apps/client/src/components/ai-actions.tsx:109
#: apps/client/src/components/ai-actions.tsx:112
msgid "Professional"
msgstr "Profesional"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:66
msgid "Profile"
msgstr "Profil"

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:55
msgid "Public"
msgstr "Publik"

#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:53
msgid "Publisher"
msgstr "Penerbit"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:69
msgid "Raise an issue"
msgstr "Mengajukan masalah"

#: apps/client/src/components/copyright.tsx:35
#: apps/client/src/pages/auth/backup-otp/page.tsx:52
#: apps/client/src/pages/auth/forgot-password/page.tsx:49
#: apps/client/src/pages/auth/login/page.tsx:55
#: apps/client/src/pages/auth/register/page.tsx:64
#: apps/client/src/pages/auth/reset-password/page.tsx:60
#: apps/client/src/pages/auth/verify-email/page.tsx:43
#: apps/client/src/pages/auth/verify-otp/page.tsx:52
#: apps/client/src/pages/builder/page.tsx:60
#: apps/client/src/pages/dashboard/resumes/page.tsx:20
#: apps/client/src/pages/dashboard/settings/page.tsx:16
#: apps/client/src/pages/home/<USER>/footer.tsx:18
#: apps/client/src/pages/home/<USER>
#: apps/client/src/pages/public/page.tsx:74
#: apps/client/src/pages/public/page.tsx:95
msgid "Reactive Resume"
msgstr "Reactive Resume"

#: apps/client/src/pages/home/<USER>/logo-cloud/index.tsx:39
msgid "Reactive Resume has helped people land jobs at these great companies:"
msgstr "Reactive Resume telah membantu banyak orang mendapatkan pekerjaan di perusahaan-perusahaan hebat ini:"

#: apps/client/src/pages/home/<USER>/support/index.tsx:12
msgid "Reactive Resume is a free and open-source project crafted mostly by me, and your support would be greatly appreciated. If you're inclined to contribute, and only if you can afford to, consider making a donation through any of the listed platforms. Additionally, donations to Reactive Resume through Open Collective are tax-exempt, as the project is fiscally hosted by Open Collective Europe."
msgstr "Reactive Resume adalah proyek sumber terbuka dan gratis yang sebagian besar dibuat oleh saya, dan dukungan Anda akan sangat dihargai. Jika Anda ingin berkontribusi, dan hanya jika Anda mampu, pertimbangkan untuk memberikan donasi melalui salah satu platform yang terdaftar. Selain itu, donasi untuk Reactive Resume melalui Open Collective bebas pajak, karena proyek ini secara fiskal di-host oleh Open Collective Europe."

#: apps/client/src/pages/home/<USER>/features/index.tsx:107
msgid "Reactive Resume is a passion project of over 3 years of hard work, and with that comes a number of re-iterated ideas and features that have been built to (near) perfection."
msgstr "Reactive Resume adalah proyek penuh semangat yang telah dikerjakan selama lebih dari 3 tahun, dan dengan itu muncullah sejumlah ide dan fitur yang diulang-ulang yang telah dibangun dengan (hampir) sempurna."

#: apps/client/src/pages/home/<USER>/contributors/index.tsx:22
msgid "Reactive Resume thrives thanks to its vibrant community. This project owes its progress to numerous individuals who've dedicated their time and skills. Below, we celebrate the coders who've enhanced its features on GitHub and the linguists whose translations on Crowdin have made it accessible to a broader audience."
msgstr "Reactive Resume berkembang berkat komunitasnya yang dinamis. Proyek ini berkembang berkat banyak orang yang telah mendedikasikan waktu dan keterampilan mereka. Di bawah ini, kami merayakan para pembuat kode yang telah meningkatkan fitur-fiturnya di GitHub dan ahli bahasa yang terjemahannya di Crowdin telah membuatnya dapat diakses oleh audiens yang lebih luas."

#: apps/client/src/pages/builder/_components/toolbar.tsx:89
msgid "Redo"
msgstr "Ulangi."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:100
#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:157
msgid "Remove"
msgstr "Menghapus"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:231
msgid "Remove Page"
msgstr "Hapus Halaman"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:111
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:101
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:86
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:152
msgid "Rename"
msgstr "Ganti nama"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:199
msgid "Resend email confirmation link"
msgstr "Kirim ulang tautan konfirmasi email"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:152
msgid "Reset"
msgstr "Atur ulang"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:201
msgid "Reset Layout"
msgstr "Atur Ulang Tata Letak"

#: apps/client/src/pages/auth/reset-password/page.tsx:60
#: apps/client/src/pages/auth/reset-password/page.tsx:65
msgid "Reset your password"
msgstr "Atur ulang kata sandi Anda"

#: apps/client/src/pages/builder/_components/toolbar.tsx:124
msgid "Reset Zoom"
msgstr "Atur Ulang Zoom"

#: apps/client/src/pages/dashboard/_components/sidebar.tsx:86
#: apps/client/src/pages/dashboard/resumes/page.tsx:20
#: apps/client/src/pages/dashboard/resumes/page.tsx:37
msgid "Resumes"
msgstr "Resume"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:14
msgid "Resumes Generated"
msgstr "Resume yang Dihasilkan"

#: apps/client/src/pages/home/<USER>/features/index.tsx:105
msgid "Rich in features, not in pricing."
msgstr "Kaya di fitur, bukan di harga."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:138
msgid "Rounded"
msgstr "Bulat"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:180
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:245
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:217
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:131
msgid "Save Changes"
msgstr "Menyimpan Perubahan"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:176
msgid "Save Locally"
msgstr "Simpan Secara Lokal"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:176
msgid "Saved"
msgstr "Tersimpan"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:168
msgid "Scan the QR code below with your authenticator app to setup 2FA on your account."
msgstr "Pindai kode QR di bawah ini dengan aplikasi autentikator Anda untuk menyiapkan 2FA di akun Anda."

#. Score or honors for the degree, for example, CGPA or magna cum laude
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:92
msgid "Score"
msgstr "Skor"

#: apps/client/src/pages/builder/_components/toolbar.tsx:104
msgid "Scroll to Pan"
msgstr "Gulir ke Geser"

#: apps/client/src/pages/builder/_components/toolbar.tsx:104
msgid "Scroll to Zoom"
msgstr "Gulir ke Zoom"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:111
msgid "Search for a font family"
msgstr "Mencari keluarga font"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:126
msgid "Search for a font subset"
msgstr "Mencari subset font"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:139
msgid "Search for a font variant"
msgstr "Mencari varian font"

#: apps/client/src/components/locale-combobox.tsx:41
msgid "Search for a language"
msgstr "Mencari bahasa"

#: apps/client/src/pages/home/<USER>/features/index.tsx:56
msgid "Secure with two-factor authentication"
msgstr "Aman dengan autentikasi dua faktor"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:65
msgid "Security"
msgstr "Keamanan"

#: apps/client/src/pages/home/<USER>/features/index.tsx:50
msgid "Self-host with Docker"
msgstr "Host mandiri dengan Docker"

#: apps/client/src/pages/auth/forgot-password/page.tsx:104
msgid "Send Email"
msgstr "Kirim Email"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:74
msgid "Send me a message"
msgstr "Kirimi saya pesan"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:97
msgid "Separate Links"
msgstr "Tautan Terpisah"

#: apps/client/src/components/user-options.tsx:32
#: apps/client/src/pages/dashboard/_components/sidebar.tsx:92
#: apps/client/src/pages/dashboard/settings/page.tsx:16
#: apps/client/src/pages/dashboard/settings/page.tsx:26
msgid "Settings"
msgstr "Pengaturan"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:159
msgid "Setup two-factor authentication on your account"
msgstr "Menyiapkan autentikasi dua faktor di akun Anda"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:107
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:38
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:39
msgid "Sharing"
msgstr "Berbagi"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:106
msgid "Show"
msgstr "Tampilkan"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:78
msgid "Show Break Line"
msgstr "Tampilkan Garis Istirahat"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:91
msgid "Show Page Numbers"
msgstr "Tampilkan Nomor Halaman"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:248
msgid "Sidebar"
msgstr "Bilah samping"

#: apps/client/src/pages/auth/backup-otp/page.tsx:103
#: apps/client/src/pages/auth/login/page.tsx:127
#: apps/client/src/pages/auth/verify-otp/page.tsx:92
msgid "Sign in"
msgstr "Masuk"

#: apps/client/src/pages/auth/register/page.tsx:74
msgid "Sign in now"
msgstr "Masuk sekarang"

#: apps/client/src/pages/auth/login/page.tsx:55
#: apps/client/src/pages/auth/login/page.tsx:60
msgid "Sign in to your account"
msgstr "Masuk ke akun Anda"

#: apps/client/src/pages/home/<USER>/features/index.tsx:55
msgid "Sign in with Email"
msgstr "Masuk dengan Email"

#: apps/client/src/pages/home/<USER>/features/index.tsx:53
msgid "Sign in with GitHub"
msgstr "Masuk dengan GitHub"

#: apps/client/src/pages/home/<USER>/features/index.tsx:54
msgid "Sign in with Google"
msgstr "Masuk dengan Google"

#: apps/client/src/pages/auth/register/page.tsx:179
msgid "Sign up"
msgstr "Daftar"

#: apps/client/src/pages/auth/login/page.tsx:74
msgid "Signing in via email is currently disabled by the administrator."
msgstr "Masuk melalui email saat ini dinonaktifkan oleh administrator."

#: apps/client/src/pages/auth/register/page.tsx:82
msgid "Signups are currently disabled by the administrator."
msgstr "Pendaftaran saat ini dinonaktifkan oleh administrator."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:65
msgid "Size (in px)"
msgstr "Ukuran (dalam px)"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:228
msgid "Slug"
msgstr "Siput"

#: apps/client/src/services/errors/translate-error.ts:55
msgid "Something went wrong while grabbing a preview your resume. Please try again later or raise an issue on GitHub."
msgstr "Terjadi kesalahan saat mengambil pratinjau resume Anda. Silakan coba lagi nanti atau ajukan masalah di GitHub."

#: apps/client/src/services/errors/translate-error.ts:52
msgid "Something went wrong while printing your resume. Please try again later or raise an issue on GitHub."
msgstr "Terjadi kesalahan saat mencetak resume Anda. Silakan coba lagi nanti atau ajukan masalah di GitHub."

#: apps/client/src/services/errors/translate-error.ts:58
msgid "Something went wrong while processing your request. Please try again later or raise an issue on GitHub."
msgstr "Terjadi kesalahan saat memproses permintaan Anda. Silakan coba lagi nanti atau ajukan masalah di GitHub."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:87
#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:132
msgid "Square"
msgstr "Persegi"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/create-card.tsx:33
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/create-item.tsx:23
msgid "Start building from scratch"
msgstr "Mulai membangun dari awal"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:185
msgid "Start building your resume by giving it a name."
msgstr "Mulailah membuat resume Anda dengan memberikan nama."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:114
#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:22
#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:23
msgid "Statistics"
msgstr "Statistik"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:38
msgid "Statistics are available only for public resumes."
msgstr "Statistik hanya tersedia untuk resume publik."

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:162
msgid "Store your backup codes securely"
msgstr "Menyimpan kode cadangan Anda dengan aman"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:95
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:129
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:138
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:114
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:129
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:95
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:109
msgid "Summary"
msgstr "Ringkasan"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:18
msgid "Support the app by donating what you can!"
msgstr "Dukung aplikasi ini dengan menyumbangkan apa yang Anda bisa!"

#: apps/client/src/pages/home/<USER>/support/index.tsx:9
msgid "Supporting Reactive Resume"
msgstr "Mendukung Resume Reaktif"

#: apps/client/src/pages/home/<USER>/features/index.tsx:65
msgid "Supports A4/Letter page formats"
msgstr "Mendukung format halaman A4/Surat"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:85
msgid "System"
msgstr "Sistem"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:65
#: apps/client/src/pages/builder/sidebars/right/sections/template.tsx:18
#: apps/client/src/pages/builder/sidebars/right/sections/template.tsx:19
msgid "Template"
msgstr "Templat"

#: apps/client/src/pages/home/<USER>/templates/index.tsx:9
msgid "Templates"
msgstr "Templat"

#: apps/client/src/pages/home/<USER>/testimonials/index.tsx:68
msgid "Testimonials"
msgstr "Testimonial"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:103
msgid "Text Color"
msgstr "Warna Teks"

#: apps/client/src/pages/public/error.tsx:17
msgid "The page you're looking for doesn't exist."
msgstr ""

#: apps/client/src/pages/public/error.tsx:29
msgid "The request was invalid."
msgstr ""

#: apps/client/src/services/errors/translate-error.ts:49
msgid "The resume you want to update is locked, please unlock if you wish to make any changes to it."
msgstr "Resume yang ingin Anda perbarui terkunci, silakan buka kuncinya jika Anda ingin melakukan perubahan."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:86
#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:19
#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:20
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:79
msgid "Theme"
msgstr "Tema"

#: apps/client/src/services/errors/translate-error.ts:40
msgid "There was an error connecting to the browser. Please make sure 'chrome' is running and reachable."
msgstr "Terjadi kesalahan saat menghubungkan ke browser. Pastikan 'chrome' berjalan dan dapat dijangkau."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:130
msgid "This action can be reverted by clicking on the undo button in the floating toolbar."
msgstr "Tindakan ini dapat dikembalikan dengan mengeklik tombol undo di toolbar mengambang."

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:151
msgid "This action cannot be undone. This will permanently delete your resume and cannot be recovered."
msgstr "Tindakan ini tidak dapat dibatalkan. Tindakan ini akan menghapus resume Anda secara permanen dan tidak dapat dipulihkan."

#: apps/client/src/services/errors/translate-error.ts:16
msgid "This email address is associated with an OAuth account. Please sign in with your OAuth provider."
msgstr "Alamat email ini dikaitkan dengan akun OAuth. Silakan masuk dengan penyedia OAuth Anda."

#: apps/client/src/pages/builder/_components/header.tsx:57
msgid "This resume is locked, please unlock to make further changes."
msgstr "Resume ini terkunci, silakan buka kunci untuk membuat perubahan lebih lanjut."

#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:23
msgid "This section is reserved for your personal notes specific to this resume. The content here remains private and is not shared with anyone else."
msgstr "Bagian ini disediakan untuk catatan pribadi Anda yang khusus untuk resume ini. Konten di sini bersifat pribadi dan tidak dibagikan dengan orang lain."

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:216
msgid "Tip: You can name the resume referring to the position you are applying for."
msgstr "Tip: Anda dapat memberi nama resume yang mengacu pada posisi yang Anda lamar."

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:39
msgctxt "Name of the Award"
msgid "Title"
msgstr "Judul"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:196
msgid "Title"
msgstr "Judul"

#: apps/client/src/pages/builder/_components/toolbar.tsx:138
msgid "Toggle Page Break Line"
msgstr "Beralih ke Garis Hentian Halaman"

#: apps/client/src/pages/builder/_components/toolbar.tsx:150
msgid "Toggle Page Numbers"
msgstr "Beralih Nomor Halaman"

#: apps/client/src/pages/home/<USER>/features/index.tsx:68
msgid "Track views and downloads"
msgstr "Melacak tampilan dan unduhan"

#: apps/client/src/pages/auth/verify-otp/page.tsx:52
#: apps/client/src/pages/auth/verify-otp/page.tsx:57
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:129
msgid "Two-Factor Authentication"
msgstr "Autentikasi Dua Faktor"

#: apps/client/src/services/errors/translate-error.ts:31
msgid "Two-factor authentication is already enabled for this account."
msgstr "Autentikasi dua faktor sudah diaktifkan untuk akun ini."

#: apps/client/src/services/errors/translate-error.ts:28
msgid "Two-factor authentication is not enabled for this account."
msgstr "Autentikasi dua faktor tidak diaktifkan untuk akun ini."

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:84
msgid "Type <0>delete</0> to confirm deleting your account."
msgstr "Ketik <0>hapus</0> untuk mengonfirmasi penghapusan akun Anda."

#. For example, Bachelor's Degree or Master's Degree
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:54
msgid "Type of Study"
msgstr "Jenis Studi"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:79
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:76
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:77
msgid "Typography"
msgstr "Tipografi"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:203
msgid "Underline Links"
msgstr "Garis bawahi Tautan"

#: apps/client/src/pages/builder/_components/toolbar.tsx:76
msgid "Undo"
msgstr "Membatalkan"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:52
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:110
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:105
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:161
msgid "Unlock"
msgstr "Membuka kunci"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:44
msgid "Unlocking a resume will allow you to make changes to it again."
msgstr "Membuka kunci resume akan memungkinkan Anda untuk membuat perubahan lagi."

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:192
msgid "Unverified"
msgstr "Tidak diverifikasi"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:164
msgid "Update an existing item"
msgstr "Memperbarui item yang sudah ada"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:179
msgid "Update an existing resume"
msgstr "Memperbarui resume yang sudah ada"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:212
msgid "Upload a file from one of the accepted sources to parse existing data and import it into Reactive Resume for easier editing."
msgstr "Unggah file dari salah satu sumber yang diterima untuk mengurai data yang ada dan mengimpornya ke dalam Reactive Resume untuk pengeditan yang lebih mudah."

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:73
msgid "URL"
msgstr "URL"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:61
msgid "URL must start with https://"
msgstr "URL harus dimulai dengan https://"

#: apps/client/src/pages/auth/backup-otp/page.tsx:52
#: apps/client/src/pages/auth/backup-otp/page.tsx:57
msgid "Use your backup code"
msgstr "Gunakan kode cadangan Anda"

#: apps/client/src/services/errors/translate-error.ts:13
msgid "User does not have an associated 'secrets' record. Please report this issue on GitHub."
msgstr "Pengguna tidak memiliki catatan 'rahasia' yang terkait. Silakan laporkan masalah ini di GitHub."

#: apps/client/src/pages/auth/register/page.tsx:119
#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:55
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:167
msgid "Username"
msgstr "Nama pengguna"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:13
msgid "Users Signed Up"
msgstr "Pengguna Mendaftar"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:296
msgid "Validate"
msgstr "Memvalidasi"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:314
msgid "Validated"
msgstr "Tervalidasi"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:97
msgid "Value"
msgstr "Nilai"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:192
msgid "Verified"
msgstr "Terverifikasi"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:161
msgid "Verify that two-factor authentication has been setup correctly"
msgstr "Verifikasi bahwa autentikasi dua faktor telah diatur dengan benar"

#: apps/client/src/pages/auth/verify-email/page.tsx:43
#: apps/client/src/pages/auth/verify-email/page.tsx:48
msgid "Verify your email address"
msgstr "Verifikasi alamat email Anda"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:26
msgid "Version 4"
msgstr "Versi 4"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:51
msgid "Views"
msgstr "Tampilan"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:87
msgid "Visible"
msgstr "Terlihat"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:70
msgid "Visit <0>Phosphor Icons</0> for a list of available icons"
msgstr "Kunjungi Ikon <0>Fosfor</0> untuk daftar ikon yang tersedia"

#: apps/client/src/pages/auth/verify-email/page.tsx:61
msgid "We verify your email address only to ensure that we can send you a password reset link in case you forget your password."
msgstr "Kami memverifikasi alamat email Anda hanya untuk memastikan bahwa kami dapat mengirimkan tautan pengaturan ulang kata sandi jika Anda lupa kata sandi."

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:115
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:124
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:100
#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:69
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:115
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:95
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:69
msgid "Website"
msgstr "Situs web"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:32
msgid "What's new in the latest version"
msgstr "Apa yang baru dalam versi terbaru"

#: apps/client/src/pages/public/error.tsx:26
msgid "You are not authorized to access this page."
msgstr ""

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:164
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:68
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:164
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:114
msgid "You can add multiple keywords by separating them with a comma or pressing enter."
msgstr "Anda dapat menambahkan beberapa kata kunci dengan memisahkannya dengan koma atau menekan enter."

#: apps/client/src/pages/auth/login/page.tsx:99
msgid "You can also enter your username."
msgstr "Anda juga dapat memasukkan nama pengguna Anda."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:103
msgid "You can also integrate with Ollama simply by setting the API key to `sk-1234567890abcdef` and the Base URL to your Ollama URL, i.e. `http://localhost:11434/v1`. You can also pick and choose models and set the max tokens as per your preference."
msgstr "Anda juga dapat berintegrasi dengan Ollama hanya dengan mengatur kunci API ke `sk-1234567890abcdef` dan URL Dasar ke URL Ollama Anda, yaitu `http://localhost:11434/v1`. Anda juga dapat memilih dan memilih model dan mengatur token maksimum sesuai keinginan Anda."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:81
msgid "You can make use of the OpenAI API to help you generate content, or improve your writing while composing your resume."
msgstr "Anda dapat memanfaatkan API OpenAI untuk membantu Anda menghasilkan konten, atau meningkatkan tulisan Anda saat membuat resume."

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:40
msgid "You can track the number of views your resume has received, or how many people have downloaded the resume by enabling public sharing."
msgstr "Anda dapat melacak jumlah tampilan yang diterima resume Anda, atau berapa banyak orang yang telah mengunduh resume dengan mengaktifkan berbagi publik."

#: apps/client/src/pages/public/error.tsx:20
msgid "You don't have permission to access this page."
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:87
msgid "You have the option to <0>obtain your own OpenAI API key</0>. This key empowers you to leverage the API as you see fit. Alternatively, if you wish to disable the AI features in Reactive Resume altogether, you can simply remove the key from your settings."
msgstr "Anda memiliki opsi untuk <0>mendapatkan kunci API OpenAI Anda sendiri</0>. Kunci ini memberdayakan Anda untuk memanfaatkan API sesuai keinginan Anda. Atau, jika Anda ingin menonaktifkan fitur AI di Reactive Resume, Anda cukup menghapus kunci dari pengaturan Anda."

#: apps/client/src/pages/auth/verify-email/page.tsx:50
msgid "You should have received an email from <0>Reactive Resume</0> with a link to verify your account."
msgstr "Anda seharusnya telah menerima email dari <0>Reactive Resume</0> dengan tautan untuk memverifikasi akun Anda."

#: apps/client/src/pages/auth/forgot-password/page.tsx:49
#: apps/client/src/pages/auth/forgot-password/page.tsx:54
msgid "You've got mail!"
msgstr "Kau punya surat!"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:52
msgid "Your account and all your data has been deleted successfully. Goodbye!"
msgstr "Akun Anda dan semua data Anda telah berhasil dihapus. Selamat tinggal!"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:191
msgid "Your API key is securely stored in the browser's local storage and is only utilized when making requests to OpenAI via their official SDK. Rest assured that your key is not transmitted to any external server except when interacting with OpenAI's services."
msgstr "Kunci API Anda disimpan dengan aman di penyimpanan lokal peramban dan hanya digunakan ketika membuat permintaan ke OpenAI melalui SDK resmi mereka. Yakinlah bahwa kunci Anda tidak dikirimkan ke server eksternal mana pun kecuali saat berinteraksi dengan layanan OpenAI."

#: apps/client/src/pages/auth/verify-email/page.tsx:28
msgid "Your email address has been verified successfully."
msgstr "Alamat email Anda telah berhasil diverifikasi."

#: apps/client/src/services/openai/client.ts:11
msgid "Your OpenAI API Key has not been set yet. Please go to your account settings to enable OpenAI Integration."
msgstr "Kunci API OpenAI Anda belum ditetapkan. Silakan buka pengaturan akun Anda untuk mengaktifkan Integrasi OpenAI."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:56
msgid "Your password has been updated successfully."
msgstr "Kata sandi Anda telah berhasil diperbarui."

#: apps/client/src/pages/builder/_components/toolbar.tsx:112
msgid "Zoom In"
msgstr "Perbesar"

#: apps/client/src/pages/builder/_components/toolbar.tsx:118
msgid "Zoom Out"
msgstr "Perkecil"

