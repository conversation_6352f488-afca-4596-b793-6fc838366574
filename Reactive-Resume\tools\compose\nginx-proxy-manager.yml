version: "3.8"

# In this Docker Compose example, we use Nginx Proxy Manager to manage the reverse proxy and SSL certificates.
# There's very little configuration to be made on the compose file itself, and most of it is done on the Nginx Proxy Manager UI.

services:
  # Database (Postgres)
  postgres:
    image: postgres:16-alpine
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Storage (for image uploads)
  minio:
    image: minio/minio:latest
    restart: unless-stopped
    command: server /data
    volumes:
      - minio_data:/data
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    labels:
      - traefik.enable=true
      - traefik.http.routers.storage.rule=Host(`storage.example.com`)
      - traefik.http.routers.storage.entrypoints=websecure
      - traefik.http.routers.storage.tls.certresolver=letsencrypt
      - traefik.http.services.storage.loadbalancer.server.port=9000

  # Chrome Browser (for printing and previews)
  chrome:
    image: ghcr.io/browserless/chromium:latest
    restart: unless-stopped
    environment:
      HEALTH: "true"
      TOKEN: chrome_token
      PROXY_HOST: "printer.example.com"
      PROXY_PORT: 443
      PROXY_SSL: "true"

  app:
    image: amruthpillai/reactive-resume:latest
    restart: unless-stopped
    depends_on:
      - postgres
      - minio
      - chrome
    environment:
      # -- Environment Variables --
      PORT: 3000
      NODE_ENV: production

      # -- URLs --
      PUBLIC_URL: https://example.com
      STORAGE_URL: https://storage.example.com

      # -- Printer (Chrome) --
      CHROME_TOKEN: chrome_token
      CHROME_URL: wss://printer.example.com

      # -- Database (Postgres) --
      DATABASE_URL: ********************************************/postgres

      # -- Auth --
      ACCESS_TOKEN_SECRET: access_token_secret
      REFRESH_TOKEN_SECRET: refresh_token_secret

      # -- Emails --
      MAIL_FROM: <EMAIL>
      # SMTP_URL: smtp://user:pass@smtp:587 # Optional

      # -- Storage (Minio) --
      STORAGE_ENDPOINT: minio
      STORAGE_PORT: 9000
      STORAGE_REGION: us-east-1 # Optional
      STORAGE_BUCKET: default
      STORAGE_ACCESS_KEY: minioadmin
      STORAGE_SECRET_KEY: minioadmin
      STORAGE_USE_SSL: "false"
      STORAGE_SKIP_BUCKET_CHECK: "false"

      # -- Crowdin (Optional) --
      # CROWDIN_PROJECT_ID:
      # CROWDIN_PERSONAL_TOKEN:

      # -- Feature Flags (Optional) --
      # DISABLE_SIGNUPS: "false"
      # DISABLE_EMAIL_AUTH: "false"

      # -- GitHub (Optional) --
      # GITHUB_CLIENT_ID: github_client_id
      # GITHUB_CLIENT_SECRET: github_client_secret
      # GITHUB_CALLBACK_URL: https://example.com/api/auth/github/callback

      # -- Google (Optional) --
      # GOOGLE_CLIENT_ID: google_client_id
      # GOOGLE_CLIENT_SECRET: google_client_secret
      # GOOGLE_CALLBACK_URL: https://example.com/api/auth/google/callback

      # -- OpenID (Optional) --
      # VITE_OPENID_NAME: OpenID
      # OPENID_AUTHORIZATION_URL:
      # OPENID_CALLBACK_URL: https://example.com/api/auth/openid/callback
      # OPENID_CLIENT_ID:
      # OPENID_CLIENT_SECRET:
      # OPENID_ISSUER:
      # OPENID_SCOPE: openid profile email
      # OPENID_TOKEN_URL:
      # OPENID_USER_INFO_URL:

  nginx:
    image: jc21/nginx-proxy-manager
    restart: always
    ports:
      - "80:80"
      - "443:443"
      - "81:81" # Port 81 is used for Proxy Manager's Web UI
    volumes:
      - nginx_data:/data
      - letsencrypt_data:/etc/letsencrypt
    environment:
      DISABLE_IPV6: "true"

volumes:
  minio_data:
  nginx_data:
  postgres_data:
  letsencrypt_data:
