@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@zkochan+js-yaml@0.0.7\node_modules\@zkochan\js-yaml\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@zkochan+js-yaml@0.0.7\node_modules\@zkochan\js-yaml\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@zkochan+js-yaml@0.0.7\node_modules\@zkochan\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@zkochan+js-yaml@0.0.7\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@zkochan+js-yaml@0.0.7\node_modules\@zkochan\js-yaml\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@zkochan+js-yaml@0.0.7\node_modules\@zkochan\js-yaml\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@zkochan+js-yaml@0.0.7\node_modules\@zkochan\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@zkochan+js-yaml@0.0.7\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\@zkochan+js-yaml@0.0.7\node_modules\@zkochan\js-yaml\bin\js-yaml.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\@zkochan+js-yaml@0.0.7\node_modules\@zkochan\js-yaml\bin\js-yaml.js" %*
)
