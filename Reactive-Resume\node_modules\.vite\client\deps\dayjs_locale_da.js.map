{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/da.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_da=t(e.dayjs)}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var d=t(e),a={name:\"da\",weekdays:\"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag\".split(\"_\"),weekdaysShort:\"søn._man._tirs._ons._tors._fre._lør.\".split(\"_\"),weekdaysMin:\"sø._ma._ti._on._to._fr._lø.\".split(\"_\"),months:\"januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december\".split(\"_\"),monthsShort:\"jan._feb._mar._apr._maj_juni_juli_aug._sept._okt._nov._dec.\".split(\"_\"),weekStart:1,yearStart:4,ordinal:function(e){return e+\".\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY HH:mm\",LLLL:\"dddd [d.] D. MMMM YYYY [kl.] HH:mm\"},relativeTime:{future:\"om %s\",past:\"%s siden\",s:\"få sekunder\",m:\"et minut\",mm:\"%d minutter\",h:\"en time\",hh:\"%d timer\",d:\"en dag\",dd:\"%d dage\",M:\"en måned\",MM:\"%d måneder\",y:\"et år\",yy:\"%d år\"}};return d.default.locale(a,null,!0),a}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,qDAAqD,MAAM,GAAG,GAAE,eAAc,uCAAuC,MAAM,GAAG,GAAE,aAAY,8BAA8B,MAAM,GAAG,GAAE,QAAO,sFAAsF,MAAM,GAAG,GAAE,aAAY,8DAA8D,MAAM,GAAG,GAAE,WAAU,GAAE,WAAU,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA,KAAE;AAAA,MAAG,GAAE,SAAQ,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,gBAAe,KAAI,sBAAqB,MAAK,qCAAoC,GAAE,cAAa,EAAC,QAAO,SAAQ,MAAK,YAAW,GAAE,eAAc,GAAE,YAAW,IAAG,eAAc,GAAE,WAAU,IAAG,YAAW,GAAE,UAAS,IAAG,WAAU,GAAE,YAAW,IAAG,cAAa,GAAE,SAAQ,IAAG,QAAO,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e"]}