{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/he.js"], "sourcesContent": ["!function(Y,M){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=M(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],M):(Y=\"undefined\"!=typeof globalThis?globalThis:Y||self).dayjs_locale_he=M(Y.dayjs)}(this,(function(Y){\"use strict\";function M(Y){return Y&&\"object\"==typeof Y&&\"default\"in Y?Y:{default:Y}}var d=M(Y),e={s:\"מספר שניות\",ss:\"%d שניות\",m:\"דקה\",mm:\"%d דקות\",h:\"שעה\",hh:\"%d שעות\",hh2:\"שעתיים\",d:\"יום\",dd:\"%d ימים\",dd2:\"יומיים\",M:\"חודש\",MM:\"%d חודשים\",MM2:\"חודשיים\",y:\"שנה\",yy:\"%d שנים\",yy2:\"שנתיים\"};function _(Y,M,d){return(e[d+(2===Y?\"2\":\"\")]||e[d]).replace(\"%d\",Y)}var l={name:\"he\",weekdays:\"ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת\".split(\"_\"),weekdaysShort:\"א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳\".split(\"_\"),weekdaysMin:\"א׳_ב׳_ג׳_ד׳_ה׳_ו_ש׳\".split(\"_\"),months:\"ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר\".split(\"_\"),monthsShort:\"ינו_פבר_מרץ_אפר_מאי_יונ_יול_אוג_ספט_אוק_נוב_דצמ\".split(\"_\"),relativeTime:{future:\"בעוד %s\",past:\"לפני %s\",s:_,m:_,mm:_,h:_,hh:_,d:_,dd:_,M:_,MM:_,y:_,yy:_},ordinal:function(Y){return Y},format:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D [ב]MMMM YYYY\",LLL:\"D [ב]MMMM YYYY HH:mm\",LLLL:\"dddd, D [ב]MMMM YYYY HH:mm\",l:\"D/M/YYYY\",ll:\"D MMM YYYY\",lll:\"D MMM YYYY HH:mm\",llll:\"ddd, D MMM YYYY HH:mm\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D [ב]MMMM YYYY\",LLL:\"D [ב]MMMM YYYY HH:mm\",LLLL:\"dddd, D [ב]MMMM YYYY HH:mm\",l:\"D/M/YYYY\",ll:\"D MMM YYYY\",lll:\"D MMM YYYY HH:mm\",llll:\"ddd, D MMM YYYY HH:mm\"}};return d.default.locale(l,null,!0),l}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,GAAE,cAAa,IAAG,YAAW,GAAE,OAAM,IAAG,WAAU,GAAE,OAAM,IAAG,WAAU,KAAI,UAAS,GAAE,OAAM,IAAG,WAAU,KAAI,UAAS,GAAE,QAAO,IAAG,aAAY,KAAI,WAAU,GAAE,OAAM,IAAG,WAAU,KAAI,SAAQ;AAAE,eAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,gBAAO,EAAEA,MAAG,MAAIF,KAAE,MAAI,GAAG,KAAG,EAAEE,EAAC,GAAG,QAAQ,MAAKF,EAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAC,MAAK,MAAK,UAAS,uCAAuC,MAAM,GAAG,GAAE,eAAc,uBAAuB,MAAM,GAAG,GAAE,aAAY,sBAAsB,MAAM,GAAG,GAAE,QAAO,0EAA0E,MAAM,GAAG,GAAE,aAAY,kDAAkD,MAAM,GAAG,GAAE,cAAa,EAAC,QAAO,WAAU,MAAK,WAAU,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,EAAC,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA;AAAA,MAAC,GAAE,QAAO,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,kBAAiB,KAAI,wBAAuB,MAAK,8BAA6B,GAAE,YAAW,IAAG,cAAa,KAAI,oBAAmB,MAAK,wBAAuB,GAAE,SAAQ,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,kBAAiB,KAAI,wBAAuB,MAAK,8BAA6B,GAAE,YAAW,IAAG,cAAa,KAAI,oBAAmB,MAAK,wBAAuB,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["Y", "M", "d"]}