import {
  require_react
} from "./chunk-2O5LBSQE.js";
import {
  __toESM
} from "./chunk-DC5AMYBS.js";

// node_modules/.pnpm/react-zoom-pan-pinch@3.7.0__0999ada38cebf017e083cc6d476f1c06/node_modules/react-zoom-pan-pinch/dist/index.esm.js
var import_react = __toESM(require_react());
var roundNumber = function(num, decimal) {
  return Number(num.toFixed(decimal));
};
var checkIsNumber = function(num, defaultValue) {
  return typeof num === "number" ? num : defaultValue;
};
var handleCallback = function(context, event, callback) {
  if (callback && typeof callback === "function") {
    callback(context, event);
  }
};
var easeOut = function(t) {
  return -Math.cos(t * Math.PI) / 2 + 0.5;
};
var linear = function(t) {
  return t;
};
var easeInQuad = function(t) {
  return t * t;
};
var easeOutQuad = function(t) {
  return t * (2 - t);
};
var easeInOutQuad = function(t) {
  return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
};
var easeInCubic = function(t) {
  return t * t * t;
};
var easeOutCubic = function(t) {
  return --t * t * t + 1;
};
var easeInOutCubic = function(t) {
  return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
};
var easeInQuart = function(t) {
  return t * t * t * t;
};
var easeOutQuart = function(t) {
  return 1 - --t * t * t * t;
};
var easeInOutQuart = function(t) {
  return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t;
};
var easeInQuint = function(t) {
  return t * t * t * t * t;
};
var easeOutQuint = function(t) {
  return 1 + --t * t * t * t * t;
};
var easeInOutQuint = function(t) {
  return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t;
};
var animations = {
  easeOut,
  linear,
  easeInQuad,
  easeOutQuad,
  easeInOutQuad,
  easeInCubic,
  easeOutCubic,
  easeInOutCubic,
  easeInQuart,
  easeOutQuart,
  easeInOutQuart,
  easeInQuint,
  easeOutQuint,
  easeInOutQuint
};
var handleCancelAnimationFrame = function(animation) {
  if (typeof animation === "number") {
    cancelAnimationFrame(animation);
  }
};
var handleCancelAnimation = function(contextInstance) {
  if (!contextInstance.mounted)
    return;
  handleCancelAnimationFrame(contextInstance.animation);
  contextInstance.animate = false;
  contextInstance.animation = null;
  contextInstance.velocity = null;
};
function handleSetupAnimation(contextInstance, animationName, animationTime, callback) {
  if (!contextInstance.mounted)
    return;
  var startTime = (/* @__PURE__ */ new Date()).getTime();
  var lastStep = 1;
  handleCancelAnimation(contextInstance);
  contextInstance.animation = function() {
    if (!contextInstance.mounted) {
      return handleCancelAnimationFrame(contextInstance.animation);
    }
    var frameTime = (/* @__PURE__ */ new Date()).getTime() - startTime;
    var animationProgress = frameTime / animationTime;
    var animationType = animations[animationName];
    var step = animationType(animationProgress);
    if (frameTime >= animationTime) {
      callback(lastStep);
      contextInstance.animation = null;
    } else if (contextInstance.animation) {
      callback(step);
      requestAnimationFrame(contextInstance.animation);
    }
  };
  requestAnimationFrame(contextInstance.animation);
}
function isValidTargetState(targetState) {
  var scale = targetState.scale, positionX = targetState.positionX, positionY = targetState.positionY;
  if (Number.isNaN(scale) || Number.isNaN(positionX) || Number.isNaN(positionY)) {
    return false;
  }
  return true;
}
function animate(contextInstance, targetState, animationTime, animationName) {
  var isValid = isValidTargetState(targetState);
  if (!contextInstance.mounted || !isValid)
    return;
  var setTransformState = contextInstance.setTransformState;
  var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;
  var scaleDiff = targetState.scale - scale;
  var positionXDiff = targetState.positionX - positionX;
  var positionYDiff = targetState.positionY - positionY;
  if (animationTime === 0) {
    setTransformState(targetState.scale, targetState.positionX, targetState.positionY);
  } else {
    handleSetupAnimation(contextInstance, animationName, animationTime, function(step) {
      var newScale = scale + scaleDiff * step;
      var newPositionX = positionX + positionXDiff * step;
      var newPositionY = positionY + positionYDiff * step;
      setTransformState(newScale, newPositionX, newPositionY);
    });
  }
}
function getComponentsSizes(wrapperComponent, contentComponent, newScale) {
  var wrapperWidth = wrapperComponent.offsetWidth;
  var wrapperHeight = wrapperComponent.offsetHeight;
  var contentWidth = contentComponent.offsetWidth;
  var contentHeight = contentComponent.offsetHeight;
  var newContentWidth = contentWidth * newScale;
  var newContentHeight = contentHeight * newScale;
  var newDiffWidth = wrapperWidth - newContentWidth;
  var newDiffHeight = wrapperHeight - newContentHeight;
  return {
    wrapperWidth,
    wrapperHeight,
    newContentWidth,
    newDiffWidth,
    newContentHeight,
    newDiffHeight
  };
}
var getBounds = function(wrapperWidth, newContentWidth, diffWidth, wrapperHeight, newContentHeight, diffHeight, centerZoomedOut) {
  var scaleWidthFactor = wrapperWidth > newContentWidth ? diffWidth * (centerZoomedOut ? 1 : 0.5) : 0;
  var scaleHeightFactor = wrapperHeight > newContentHeight ? diffHeight * (centerZoomedOut ? 1 : 0.5) : 0;
  var minPositionX = wrapperWidth - newContentWidth - scaleWidthFactor;
  var maxPositionX = scaleWidthFactor;
  var minPositionY = wrapperHeight - newContentHeight - scaleHeightFactor;
  var maxPositionY = scaleHeightFactor;
  return { minPositionX, maxPositionX, minPositionY, maxPositionY };
};
var calculateBounds = function(contextInstance, newScale) {
  var wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;
  var centerZoomedOut = contextInstance.setup.centerZoomedOut;
  if (!wrapperComponent || !contentComponent) {
    throw new Error("Components are not mounted");
  }
  var _a = getComponentsSizes(wrapperComponent, contentComponent, newScale), wrapperWidth = _a.wrapperWidth, wrapperHeight = _a.wrapperHeight, newContentWidth = _a.newContentWidth, newDiffWidth = _a.newDiffWidth, newContentHeight = _a.newContentHeight, newDiffHeight = _a.newDiffHeight;
  var bounds = getBounds(wrapperWidth, newContentWidth, newDiffWidth, wrapperHeight, newContentHeight, newDiffHeight, Boolean(centerZoomedOut));
  return bounds;
};
var boundLimiter = function(value, minBound, maxBound, isActive) {
  if (!isActive)
    return roundNumber(value, 2);
  if (value < minBound)
    return roundNumber(minBound, 2);
  if (value > maxBound)
    return roundNumber(maxBound, 2);
  return roundNumber(value, 2);
};
var handleCalculateBounds = function(contextInstance, newScale) {
  var bounds = calculateBounds(contextInstance, newScale);
  contextInstance.bounds = bounds;
  return bounds;
};
function getMouseBoundedPosition(positionX, positionY, bounds, limitToBounds, paddingValueX, paddingValueY, wrapperComponent) {
  var minPositionX = bounds.minPositionX, minPositionY = bounds.minPositionY, maxPositionX = bounds.maxPositionX, maxPositionY = bounds.maxPositionY;
  var paddingX = 0;
  var paddingY = 0;
  if (wrapperComponent) {
    paddingX = paddingValueX;
    paddingY = paddingValueY;
  }
  var x = boundLimiter(positionX, minPositionX - paddingX, maxPositionX + paddingX, limitToBounds);
  var y = boundLimiter(positionY, minPositionY - paddingY, maxPositionY + paddingY, limitToBounds);
  return { x, y };
}
function handleCalculateZoomPositions(contextInstance, mouseX, mouseY, newScale, bounds, limitToBounds) {
  var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;
  var scaleDifference = newScale - scale;
  if (typeof mouseX !== "number" || typeof mouseY !== "number") {
    console.error("Mouse X and Y position were not provided!");
    return { x: positionX, y: positionY };
  }
  var calculatedPositionX = positionX - mouseX * scaleDifference;
  var calculatedPositionY = positionY - mouseY * scaleDifference;
  var newPositions = getMouseBoundedPosition(calculatedPositionX, calculatedPositionY, bounds, limitToBounds, 0, 0, null);
  return newPositions;
}
function checkZoomBounds(zoom, minScale, maxScale, zoomPadding, enablePadding) {
  var scalePadding = enablePadding ? zoomPadding : 0;
  var minScaleWithPadding = minScale - scalePadding;
  if (!Number.isNaN(maxScale) && zoom >= maxScale)
    return maxScale;
  if (!Number.isNaN(minScale) && zoom <= minScaleWithPadding)
    return minScaleWithPadding;
  return zoom;
}
var isPanningStartAllowed = function(contextInstance, event) {
  var excluded = contextInstance.setup.panning.excluded;
  var isInitialized = contextInstance.isInitialized, wrapperComponent = contextInstance.wrapperComponent;
  var target = event.target;
  var targetIsShadowDom = "shadowRoot" in target && "composedPath" in event;
  var isWrapperChild = targetIsShadowDom ? event.composedPath().some(function(el) {
    if (!(el instanceof Element)) {
      return false;
    }
    return wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.contains(el);
  }) : wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.contains(target);
  var isAllowed = isInitialized && target && isWrapperChild;
  if (!isAllowed)
    return false;
  var isExcluded = isExcludedNode(target, excluded);
  if (isExcluded)
    return false;
  return true;
};
var isPanningAllowed = function(contextInstance) {
  var isInitialized = contextInstance.isInitialized, isPanning = contextInstance.isPanning, setup = contextInstance.setup;
  var disabled = setup.panning.disabled;
  var isAllowed = isInitialized && isPanning && !disabled;
  if (!isAllowed)
    return false;
  return true;
};
var handlePanningSetup = function(contextInstance, event) {
  var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY;
  contextInstance.isPanning = true;
  var x = event.clientX;
  var y = event.clientY;
  contextInstance.startCoords = { x: x - positionX, y: y - positionY };
};
var handleTouchPanningSetup = function(contextInstance, event) {
  var touches = event.touches;
  var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY;
  contextInstance.isPanning = true;
  var oneFingerTouch = touches.length === 1;
  if (oneFingerTouch) {
    var x = touches[0].clientX;
    var y = touches[0].clientY;
    contextInstance.startCoords = { x: x - positionX, y: y - positionY };
  }
};
function handlePanToBounds(contextInstance) {
  var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY, scale = _a.scale;
  var _b = contextInstance.setup, disabled = _b.disabled, limitToBounds = _b.limitToBounds, centerZoomedOut = _b.centerZoomedOut;
  var wrapperComponent = contextInstance.wrapperComponent;
  if (disabled || !wrapperComponent || !contextInstance.bounds)
    return;
  var _c = contextInstance.bounds, maxPositionX = _c.maxPositionX, minPositionX = _c.minPositionX, maxPositionY = _c.maxPositionY, minPositionY = _c.minPositionY;
  var xChanged = positionX > maxPositionX || positionX < minPositionX;
  var yChanged = positionY > maxPositionY || positionY < minPositionY;
  var mousePosX = positionX > maxPositionX ? wrapperComponent.offsetWidth : contextInstance.setup.minPositionX || 0;
  var mousePosY = positionY > maxPositionY ? wrapperComponent.offsetHeight : contextInstance.setup.minPositionY || 0;
  var _d = handleCalculateZoomPositions(contextInstance, mousePosX, mousePosY, scale, contextInstance.bounds, limitToBounds || centerZoomedOut), x = _d.x, y = _d.y;
  return {
    scale,
    positionX: xChanged ? x : positionX,
    positionY: yChanged ? y : positionY
  };
}
function handleNewPosition(contextInstance, newPositionX, newPositionY, paddingValueX, paddingValueY) {
  var limitToBounds = contextInstance.setup.limitToBounds;
  var wrapperComponent = contextInstance.wrapperComponent, bounds = contextInstance.bounds;
  var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;
  if (wrapperComponent === null || bounds === null || newPositionX === positionX && newPositionY === positionY) {
    return;
  }
  var _b = getMouseBoundedPosition(newPositionX, newPositionY, bounds, limitToBounds, paddingValueX, paddingValueY, wrapperComponent), x = _b.x, y = _b.y;
  contextInstance.setTransformState(scale, x, y);
}
var getPanningClientPosition = function(contextInstance, clientX, clientY) {
  var startCoords = contextInstance.startCoords, transformState = contextInstance.transformState;
  var panning = contextInstance.setup.panning;
  var lockAxisX = panning.lockAxisX, lockAxisY = panning.lockAxisY;
  var positionX = transformState.positionX, positionY = transformState.positionY;
  if (!startCoords) {
    return { x: positionX, y: positionY };
  }
  var mouseX = clientX - startCoords.x;
  var mouseY = clientY - startCoords.y;
  var newPositionX = lockAxisX ? positionX : mouseX;
  var newPositionY = lockAxisY ? positionY : mouseY;
  return { x: newPositionX, y: newPositionY };
};
var getPaddingValue = function(contextInstance, size) {
  var setup = contextInstance.setup, transformState = contextInstance.transformState;
  var scale = transformState.scale;
  var minScale = setup.minScale, disablePadding = setup.disablePadding;
  if (size > 0 && scale >= minScale && !disablePadding) {
    return size;
  }
  return 0;
};
var isVelocityCalculationAllowed = function(contextInstance) {
  var mounted = contextInstance.mounted;
  var _a = contextInstance.setup, disabled = _a.disabled, velocityAnimation = _a.velocityAnimation;
  var scale = contextInstance.transformState.scale;
  var disabledVelocity = velocityAnimation.disabled;
  var isAllowed = !disabledVelocity || scale > 1 || !disabled || mounted;
  if (!isAllowed)
    return false;
  return true;
};
var isVelocityAllowed = function(contextInstance) {
  var mounted = contextInstance.mounted, velocity = contextInstance.velocity, bounds = contextInstance.bounds;
  var _a = contextInstance.setup, disabled = _a.disabled, velocityAnimation = _a.velocityAnimation;
  var scale = contextInstance.transformState.scale;
  var disabledVelocity = velocityAnimation.disabled;
  var isAllowed = !disabledVelocity || scale > 1 || !disabled || mounted;
  if (!isAllowed)
    return false;
  if (!velocity || !bounds)
    return false;
  return true;
};
function getVelocityMoveTime(contextInstance, velocity) {
  var velocityAnimation = contextInstance.setup.velocityAnimation;
  var equalToMove = velocityAnimation.equalToMove, animationTime = velocityAnimation.animationTime, sensitivity = velocityAnimation.sensitivity;
  if (equalToMove) {
    return animationTime * velocity * sensitivity;
  }
  return animationTime;
}
function getVelocityPosition(newPosition, startPosition, currentPosition, isLocked, limitToBounds, minPosition, maxPosition, minTarget, maxTarget, step) {
  if (limitToBounds) {
    if (startPosition > maxPosition && currentPosition > maxPosition) {
      var calculatedPosition = maxPosition + (newPosition - maxPosition) * step;
      if (calculatedPosition > maxTarget)
        return maxTarget;
      if (calculatedPosition < maxPosition)
        return maxPosition;
      return calculatedPosition;
    }
    if (startPosition < minPosition && currentPosition < minPosition) {
      var calculatedPosition = minPosition + (newPosition - minPosition) * step;
      if (calculatedPosition < minTarget)
        return minTarget;
      if (calculatedPosition > minPosition)
        return minPosition;
      return calculatedPosition;
    }
  }
  if (isLocked)
    return startPosition;
  return boundLimiter(newPosition, minPosition, maxPosition, limitToBounds);
}
function getSizeMultiplier(wrapperComponent, equalToMove) {
  var defaultMultiplier = 1;
  if (equalToMove) {
    return Math.min(defaultMultiplier, wrapperComponent.offsetWidth / window.innerWidth);
  }
  return defaultMultiplier;
}
function handleCalculateVelocity(contextInstance, position) {
  var isAllowed = isVelocityCalculationAllowed(contextInstance);
  if (!isAllowed) {
    return;
  }
  var lastMousePosition = contextInstance.lastMousePosition, velocityTime = contextInstance.velocityTime, setup = contextInstance.setup;
  var wrapperComponent = contextInstance.wrapperComponent;
  var equalToMove = setup.velocityAnimation.equalToMove;
  var now = Date.now();
  if (lastMousePosition && velocityTime && wrapperComponent) {
    var sizeMultiplier = getSizeMultiplier(wrapperComponent, equalToMove);
    var distanceX = position.x - lastMousePosition.x;
    var distanceY = position.y - lastMousePosition.y;
    var velocityX = distanceX / sizeMultiplier;
    var velocityY = distanceY / sizeMultiplier;
    var interval = now - velocityTime;
    var speed = distanceX * distanceX + distanceY * distanceY;
    var velocity = Math.sqrt(speed) / interval;
    contextInstance.velocity = { velocityX, velocityY, total: velocity };
  }
  contextInstance.lastMousePosition = position;
  contextInstance.velocityTime = now;
}
function handleVelocityPanning(contextInstance) {
  var velocity = contextInstance.velocity, bounds = contextInstance.bounds, setup = contextInstance.setup, wrapperComponent = contextInstance.wrapperComponent;
  var isAllowed = isVelocityAllowed(contextInstance);
  if (!isAllowed || !velocity || !bounds || !wrapperComponent) {
    return;
  }
  var velocityX = velocity.velocityX, velocityY = velocity.velocityY, total = velocity.total;
  var maxPositionX = bounds.maxPositionX, minPositionX = bounds.minPositionX, maxPositionY = bounds.maxPositionY, minPositionY = bounds.minPositionY;
  var limitToBounds = setup.limitToBounds, alignmentAnimation = setup.alignmentAnimation;
  var zoomAnimation = setup.zoomAnimation, panning = setup.panning;
  var lockAxisY = panning.lockAxisY, lockAxisX = panning.lockAxisX;
  var animationType = zoomAnimation.animationType;
  var sizeX = alignmentAnimation.sizeX, sizeY = alignmentAnimation.sizeY, velocityAlignmentTime = alignmentAnimation.velocityAlignmentTime;
  var alignAnimationTime = velocityAlignmentTime;
  var moveAnimationTime = getVelocityMoveTime(contextInstance, total);
  var finalAnimationTime = Math.max(moveAnimationTime, alignAnimationTime);
  var paddingValueX = getPaddingValue(contextInstance, sizeX);
  var paddingValueY = getPaddingValue(contextInstance, sizeY);
  var paddingX = paddingValueX * wrapperComponent.offsetWidth / 100;
  var paddingY = paddingValueY * wrapperComponent.offsetHeight / 100;
  var maxTargetX = maxPositionX + paddingX;
  var minTargetX = minPositionX - paddingX;
  var maxTargetY = maxPositionY + paddingY;
  var minTargetY = minPositionY - paddingY;
  var startState = contextInstance.transformState;
  var startTime = (/* @__PURE__ */ new Date()).getTime();
  handleSetupAnimation(contextInstance, animationType, finalAnimationTime, function(step) {
    var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;
    var frameTime = (/* @__PURE__ */ new Date()).getTime() - startTime;
    var animationProgress = frameTime / alignAnimationTime;
    var alignAnimation = animations[alignmentAnimation.animationType];
    var alignStep = 1 - alignAnimation(Math.min(1, animationProgress));
    var customStep = 1 - step;
    var newPositionX = positionX + velocityX * customStep;
    var newPositionY = positionY + velocityY * customStep;
    var currentPositionX = getVelocityPosition(newPositionX, startState.positionX, positionX, lockAxisX, limitToBounds, minPositionX, maxPositionX, minTargetX, maxTargetX, alignStep);
    var currentPositionY = getVelocityPosition(newPositionY, startState.positionY, positionY, lockAxisY, limitToBounds, minPositionY, maxPositionY, minTargetY, maxTargetY, alignStep);
    if (positionX !== newPositionX || positionY !== newPositionY) {
      contextInstance.setTransformState(scale, currentPositionX, currentPositionY);
    }
  });
}
function handlePanningStart(contextInstance, event) {
  var scale = contextInstance.transformState.scale;
  handleCancelAnimation(contextInstance);
  handleCalculateBounds(contextInstance, scale);
  if (window.TouchEvent !== void 0 && event instanceof TouchEvent) {
    handleTouchPanningSetup(contextInstance, event);
  } else {
    handlePanningSetup(contextInstance, event);
  }
}
function handleAlignToBounds(contextInstance, customAnimationTime) {
  var scale = contextInstance.transformState.scale;
  var _a = contextInstance.setup, minScale = _a.minScale, alignmentAnimation = _a.alignmentAnimation;
  var disabled = alignmentAnimation.disabled, sizeX = alignmentAnimation.sizeX, sizeY = alignmentAnimation.sizeY, animationTime = alignmentAnimation.animationTime, animationType = alignmentAnimation.animationType;
  var isDisabled = disabled || scale < minScale || !sizeX && !sizeY;
  if (isDisabled)
    return;
  var targetState = handlePanToBounds(contextInstance);
  if (targetState) {
    animate(contextInstance, targetState, customAnimationTime !== null && customAnimationTime !== void 0 ? customAnimationTime : animationTime, animationType);
  }
}
function handlePanning(contextInstance, clientX, clientY) {
  var startCoords = contextInstance.startCoords, setup = contextInstance.setup;
  var _a = setup.alignmentAnimation, sizeX = _a.sizeX, sizeY = _a.sizeY;
  if (!startCoords)
    return;
  var _b = getPanningClientPosition(contextInstance, clientX, clientY), x = _b.x, y = _b.y;
  var paddingValueX = getPaddingValue(contextInstance, sizeX);
  var paddingValueY = getPaddingValue(contextInstance, sizeY);
  handleCalculateVelocity(contextInstance, { x, y });
  handleNewPosition(contextInstance, x, y, paddingValueX, paddingValueY);
}
function handlePanningEnd(contextInstance) {
  if (contextInstance.isPanning) {
    var velocityDisabled = contextInstance.setup.panning.velocityDisabled;
    var velocity = contextInstance.velocity, wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;
    contextInstance.isPanning = false;
    contextInstance.animate = false;
    contextInstance.animation = null;
    var wrapperRect = wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.getBoundingClientRect();
    var contentRect = contentComponent === null || contentComponent === void 0 ? void 0 : contentComponent.getBoundingClientRect();
    var wrapperWidth = (wrapperRect === null || wrapperRect === void 0 ? void 0 : wrapperRect.width) || 0;
    var wrapperHeight = (wrapperRect === null || wrapperRect === void 0 ? void 0 : wrapperRect.height) || 0;
    var contentWidth = (contentRect === null || contentRect === void 0 ? void 0 : contentRect.width) || 0;
    var contentHeight = (contentRect === null || contentRect === void 0 ? void 0 : contentRect.height) || 0;
    var isZoomed = wrapperWidth < contentWidth || wrapperHeight < contentHeight;
    var shouldAnimate = !velocityDisabled && velocity && (velocity === null || velocity === void 0 ? void 0 : velocity.total) > 0.1 && isZoomed;
    if (shouldAnimate) {
      handleVelocityPanning(contextInstance);
    } else {
      handleAlignToBounds(contextInstance);
    }
  }
}
function handleZoomToPoint(contextInstance, scale, mouseX, mouseY) {
  var _a = contextInstance.setup, minScale = _a.minScale, maxScale = _a.maxScale, limitToBounds = _a.limitToBounds;
  var newScale = checkZoomBounds(roundNumber(scale, 2), minScale, maxScale, 0, false);
  var bounds = handleCalculateBounds(contextInstance, newScale);
  var _b = handleCalculateZoomPositions(contextInstance, mouseX, mouseY, newScale, bounds, limitToBounds), x = _b.x, y = _b.y;
  return { scale: newScale, positionX: x, positionY: y };
}
function handleAlignToScaleBounds(contextInstance, mousePositionX, mousePositionY) {
  var scale = contextInstance.transformState.scale;
  var wrapperComponent = contextInstance.wrapperComponent;
  var _a = contextInstance.setup, minScale = _a.minScale, limitToBounds = _a.limitToBounds, zoomAnimation = _a.zoomAnimation;
  var disabled = zoomAnimation.disabled, animationTime = zoomAnimation.animationTime, animationType = zoomAnimation.animationType;
  var isDisabled = disabled || scale >= minScale;
  if (scale >= 1 || limitToBounds) {
    handleAlignToBounds(contextInstance);
  }
  if (isDisabled || !wrapperComponent || !contextInstance.mounted)
    return;
  var mouseX = mousePositionX || wrapperComponent.offsetWidth / 2;
  var mouseY = mousePositionY || wrapperComponent.offsetHeight / 2;
  var targetState = handleZoomToPoint(contextInstance, minScale, mouseX, mouseY);
  if (targetState) {
    animate(contextInstance, targetState, animationTime, animationType);
  }
}
var __assign = function() {
  __assign = Object.assign || function __assign2(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
    }
    return t;
  };
  return __assign.apply(this, arguments);
};
function __rest(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
    t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
      if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
        t[p[i]] = s[p[i]];
    }
  return t;
}
function __spreadArray(to, from, pack) {
  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
    if (ar || !(i in from)) {
      if (!ar) ar = Array.prototype.slice.call(from, 0, i);
      ar[i] = from[i];
    }
  }
  return to.concat(ar || Array.prototype.slice.call(from));
}
var initialState = {
  previousScale: 1,
  scale: 1,
  positionX: 0,
  positionY: 0
};
var initialSetup = {
  disabled: false,
  minPositionX: null,
  maxPositionX: null,
  minPositionY: null,
  maxPositionY: null,
  minScale: 1,
  maxScale: 8,
  limitToBounds: true,
  centerZoomedOut: false,
  centerOnInit: false,
  disablePadding: false,
  smooth: true,
  wheel: {
    step: 0.2,
    disabled: false,
    smoothStep: 1e-3,
    wheelDisabled: false,
    touchPadDisabled: false,
    activationKeys: [],
    excluded: []
  },
  panning: {
    disabled: false,
    velocityDisabled: false,
    lockAxisX: false,
    lockAxisY: false,
    allowLeftClickPan: true,
    allowMiddleClickPan: true,
    allowRightClickPan: true,
    wheelPanning: false,
    activationKeys: [],
    excluded: []
  },
  pinch: {
    step: 5,
    disabled: false,
    excluded: []
  },
  doubleClick: {
    disabled: false,
    step: 0.7,
    mode: "zoomIn",
    animationType: "easeOut",
    animationTime: 200,
    excluded: []
  },
  zoomAnimation: {
    disabled: false,
    size: 0.4,
    animationTime: 200,
    animationType: "easeOut"
  },
  alignmentAnimation: {
    disabled: false,
    sizeX: 100,
    sizeY: 100,
    animationTime: 200,
    velocityAlignmentTime: 400,
    animationType: "easeOut"
  },
  velocityAnimation: {
    disabled: false,
    sensitivity: 1,
    animationTime: 400,
    animationType: "easeOut",
    equalToMove: true
  }
};
var baseClasses = {
  wrapperClass: "react-transform-wrapper",
  contentClass: "react-transform-component"
};
var createState = function(props) {
  var _a, _b, _c, _d;
  return {
    previousScale: (_a = props.initialScale) !== null && _a !== void 0 ? _a : initialState.scale,
    scale: (_b = props.initialScale) !== null && _b !== void 0 ? _b : initialState.scale,
    positionX: (_c = props.initialPositionX) !== null && _c !== void 0 ? _c : initialState.positionX,
    positionY: (_d = props.initialPositionY) !== null && _d !== void 0 ? _d : initialState.positionY
  };
};
var createSetup = function(props) {
  var newSetup = __assign({}, initialSetup);
  Object.keys(props).forEach(function(key) {
    var validValue = typeof props[key] !== "undefined";
    var validParameter = typeof initialSetup[key] !== "undefined";
    if (validParameter && validValue) {
      var dataType = Object.prototype.toString.call(initialSetup[key]);
      var isObject = dataType === "[object Object]";
      var isArray = dataType === "[object Array]";
      if (isObject) {
        newSetup[key] = __assign(__assign({}, initialSetup[key]), props[key]);
      } else if (isArray) {
        newSetup[key] = __spreadArray(__spreadArray([], initialSetup[key], true), props[key], true);
      } else {
        newSetup[key] = props[key];
      }
    }
  });
  return newSetup;
};
var handleCalculateButtonZoom = function(contextInstance, delta, step) {
  var scale = contextInstance.transformState.scale;
  var wrapperComponent = contextInstance.wrapperComponent, setup = contextInstance.setup;
  var maxScale = setup.maxScale, minScale = setup.minScale, zoomAnimation = setup.zoomAnimation, smooth = setup.smooth;
  var size = zoomAnimation.size;
  if (!wrapperComponent) {
    throw new Error("Wrapper is not mounted");
  }
  var targetScale = smooth ? scale * Math.exp(delta * step) : scale + delta * step;
  var newScale = checkZoomBounds(roundNumber(targetScale, 3), minScale, maxScale, size, false);
  return newScale;
};
function handleZoomToViewCenter(contextInstance, delta, step, animationTime, animationType) {
  var wrapperComponent = contextInstance.wrapperComponent;
  var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;
  if (!wrapperComponent)
    return console.error("No WrapperComponent found");
  var wrapperWidth = wrapperComponent.offsetWidth;
  var wrapperHeight = wrapperComponent.offsetHeight;
  var mouseX = (wrapperWidth / 2 - positionX) / scale;
  var mouseY = (wrapperHeight / 2 - positionY) / scale;
  var newScale = handleCalculateButtonZoom(contextInstance, delta, step);
  var targetState = handleZoomToPoint(contextInstance, newScale, mouseX, mouseY);
  if (!targetState) {
    return console.error("Error during zoom event. New transformation state was not calculated.");
  }
  animate(contextInstance, targetState, animationTime, animationType);
}
function resetTransformations(contextInstance, animationTime, animationType, onResetTransformation) {
  var setup = contextInstance.setup, wrapperComponent = contextInstance.wrapperComponent;
  var limitToBounds = setup.limitToBounds;
  var initialTransformation = createState(contextInstance.props);
  var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;
  if (!wrapperComponent)
    return;
  var newBounds = calculateBounds(contextInstance, initialTransformation.scale);
  var boundedPositions = getMouseBoundedPosition(initialTransformation.positionX, initialTransformation.positionY, newBounds, limitToBounds, 0, 0, wrapperComponent);
  var newState = {
    scale: initialTransformation.scale,
    positionX: boundedPositions.x,
    positionY: boundedPositions.y
  };
  if (scale === initialTransformation.scale && positionX === initialTransformation.positionX && positionY === initialTransformation.positionY) {
    return;
  }
  onResetTransformation === null || onResetTransformation === void 0 ? void 0 : onResetTransformation();
  animate(contextInstance, newState, animationTime, animationType);
}
function getOffset(element, wrapper, content, state) {
  var offset = element.getBoundingClientRect();
  var wrapperOffset = wrapper.getBoundingClientRect();
  var contentOffset = content.getBoundingClientRect();
  var xOff = wrapperOffset.x * state.scale;
  var yOff = wrapperOffset.y * state.scale;
  return {
    x: (offset.x - contentOffset.x + xOff) / state.scale,
    y: (offset.y - contentOffset.y + yOff) / state.scale
  };
}
function calculateZoomToNode(contextInstance, node, customZoom) {
  var wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent, transformState = contextInstance.transformState;
  var _a = contextInstance.setup, limitToBounds = _a.limitToBounds, minScale = _a.minScale, maxScale = _a.maxScale;
  if (!wrapperComponent || !contentComponent)
    return transformState;
  var wrapperRect = wrapperComponent.getBoundingClientRect();
  var nodeRect = node.getBoundingClientRect();
  var nodeOffset = getOffset(node, wrapperComponent, contentComponent, transformState);
  var nodeLeft = nodeOffset.x;
  var nodeTop = nodeOffset.y;
  var nodeWidth = nodeRect.width / transformState.scale;
  var nodeHeight = nodeRect.height / transformState.scale;
  var scaleX = wrapperComponent.offsetWidth / nodeWidth;
  var scaleY = wrapperComponent.offsetHeight / nodeHeight;
  var newScale = checkZoomBounds(customZoom || Math.min(scaleX, scaleY), minScale, maxScale, 0, false);
  var offsetX = (wrapperRect.width - nodeWidth * newScale) / 2;
  var offsetY = (wrapperRect.height - nodeHeight * newScale) / 2;
  var newPositionX = (wrapperRect.left - nodeLeft) * newScale + offsetX;
  var newPositionY = (wrapperRect.top - nodeTop) * newScale + offsetY;
  var bounds = calculateBounds(contextInstance, newScale);
  var _b = getMouseBoundedPosition(newPositionX, newPositionY, bounds, limitToBounds, 0, 0, wrapperComponent), x = _b.x, y = _b.y;
  return { positionX: x, positionY: y, scale: newScale };
}
var zoomIn = function(contextInstance) {
  return function(step, animationTime, animationType) {
    if (step === void 0) {
      step = 0.5;
    }
    if (animationTime === void 0) {
      animationTime = 300;
    }
    if (animationType === void 0) {
      animationType = "easeOut";
    }
    handleZoomToViewCenter(contextInstance, 1, step, animationTime, animationType);
  };
};
var zoomOut = function(contextInstance) {
  return function(step, animationTime, animationType) {
    if (step === void 0) {
      step = 0.5;
    }
    if (animationTime === void 0) {
      animationTime = 300;
    }
    if (animationType === void 0) {
      animationType = "easeOut";
    }
    handleZoomToViewCenter(contextInstance, -1, step, animationTime, animationType);
  };
};
var setTransform = function(contextInstance) {
  return function(newPositionX, newPositionY, newScale, animationTime, animationType) {
    if (animationTime === void 0) {
      animationTime = 300;
    }
    if (animationType === void 0) {
      animationType = "easeOut";
    }
    var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY, scale = _a.scale;
    var wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;
    var disabled = contextInstance.setup.disabled;
    if (disabled || !wrapperComponent || !contentComponent)
      return;
    var targetState = {
      positionX: Number.isNaN(newPositionX) ? positionX : newPositionX,
      positionY: Number.isNaN(newPositionY) ? positionY : newPositionY,
      scale: Number.isNaN(newScale) ? scale : newScale
    };
    animate(contextInstance, targetState, animationTime, animationType);
  };
};
var resetTransform = function(contextInstance) {
  return function(animationTime, animationType) {
    if (animationTime === void 0) {
      animationTime = 200;
    }
    if (animationType === void 0) {
      animationType = "easeOut";
    }
    resetTransformations(contextInstance, animationTime, animationType);
  };
};
var centerView = function(contextInstance) {
  return function(scale, animationTime, animationType) {
    if (animationTime === void 0) {
      animationTime = 200;
    }
    if (animationType === void 0) {
      animationType = "easeOut";
    }
    var transformState = contextInstance.transformState, wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;
    if (wrapperComponent && contentComponent) {
      var targetState = getCenterPosition(scale || transformState.scale, wrapperComponent, contentComponent);
      animate(contextInstance, targetState, animationTime, animationType);
    }
  };
};
var zoomToElement = function(contextInstance) {
  return function(node, scale, animationTime, animationType) {
    if (animationTime === void 0) {
      animationTime = 600;
    }
    if (animationType === void 0) {
      animationType = "easeOut";
    }
    handleCancelAnimation(contextInstance);
    var wrapperComponent = contextInstance.wrapperComponent;
    var target = typeof node === "string" ? document.getElementById(node) : node;
    if (wrapperComponent && target && wrapperComponent.contains(target)) {
      var targetState = calculateZoomToNode(contextInstance, target, scale);
      animate(contextInstance, targetState, animationTime, animationType);
    }
  };
};
var getControls = function(contextInstance) {
  return {
    instance: contextInstance,
    zoomIn: zoomIn(contextInstance),
    zoomOut: zoomOut(contextInstance),
    setTransform: setTransform(contextInstance),
    resetTransform: resetTransform(contextInstance),
    centerView: centerView(contextInstance),
    zoomToElement: zoomToElement(contextInstance)
  };
};
var getState = function(contextInstance) {
  return {
    instance: contextInstance,
    state: contextInstance.transformState
  };
};
var getContext = function(contextInstance) {
  var ref = {};
  Object.assign(ref, getState(contextInstance));
  Object.assign(ref, getControls(contextInstance));
  return ref;
};
var passiveSupported = false;
function makePassiveEventOption() {
  try {
    var options = {
      get passive() {
        passiveSupported = true;
        return false;
      }
    };
    return options;
  } catch (err) {
    passiveSupported = false;
    return passiveSupported;
  }
}
var matchPrefix = ".".concat(baseClasses.wrapperClass);
var isExcludedNode = function(node, excluded) {
  return excluded.some(function(exclude) {
    return node.matches("".concat(matchPrefix, " ").concat(exclude, ", ").concat(matchPrefix, " .").concat(exclude, ", ").concat(matchPrefix, " ").concat(exclude, " *, ").concat(matchPrefix, " .").concat(exclude, " *"));
  });
};
var cancelTimeout = function(timeout) {
  if (timeout) {
    clearTimeout(timeout);
  }
};
var getTransformStyles = function(x, y, scale) {
  return "translate(".concat(x, "px, ").concat(y, "px) scale(").concat(scale, ")");
};
var getMatrixTransformStyles = function(x, y, scale) {
  var a = scale;
  var b = 0;
  var c = 0;
  var d = scale;
  var tx = x;
  var ty = y;
  return "matrix3d(".concat(a, ", ").concat(b, ", 0, 0, ").concat(c, ", ").concat(d, ", 0, 0, 0, 0, 1, 0, ").concat(tx, ", ").concat(ty, ", 0, 1)");
};
var getCenterPosition = function(scale, wrapperComponent, contentComponent) {
  var contentWidth = contentComponent.offsetWidth * scale;
  var contentHeight = contentComponent.offsetHeight * scale;
  var centerPositionX = (wrapperComponent.offsetWidth - contentWidth) / 2;
  var centerPositionY = (wrapperComponent.offsetHeight - contentHeight) / 2;
  return {
    scale,
    positionX: centerPositionX,
    positionY: centerPositionY
  };
};
function mergeRefs(refs) {
  return function(value) {
    refs.forEach(function(ref) {
      if (typeof ref === "function") {
        ref(value);
      } else if (ref != null) {
        ref.current = value;
      }
    });
  };
}
var isWheelAllowed = function(contextInstance, event) {
  var _a = contextInstance.setup.wheel, disabled = _a.disabled, wheelDisabled = _a.wheelDisabled, touchPadDisabled = _a.touchPadDisabled, excluded = _a.excluded;
  var isInitialized = contextInstance.isInitialized, isPanning = contextInstance.isPanning;
  var target = event.target;
  var isAllowed = isInitialized && !isPanning && !disabled && target;
  if (!isAllowed)
    return false;
  if (wheelDisabled && !event.ctrlKey)
    return false;
  if (touchPadDisabled && event.ctrlKey)
    return false;
  var isExcluded = isExcludedNode(target, excluded);
  if (isExcluded)
    return false;
  return true;
};
var getDeltaY = function(event) {
  if (event) {
    return event.deltaY < 0 ? 1 : -1;
  }
  return 0;
};
function getDelta(event, customDelta) {
  var deltaY = getDeltaY(event);
  var delta = checkIsNumber(customDelta, deltaY);
  return delta;
}
function getMousePosition(event, contentComponent, scale) {
  var contentRect = contentComponent.getBoundingClientRect();
  var mouseX = 0;
  var mouseY = 0;
  if ("clientX" in event) {
    mouseX = (event.clientX - contentRect.left) / scale;
    mouseY = (event.clientY - contentRect.top) / scale;
  } else {
    var touch = event.touches[0];
    mouseX = (touch.clientX - contentRect.left) / scale;
    mouseY = (touch.clientY - contentRect.top) / scale;
  }
  if (Number.isNaN(mouseX) || Number.isNaN(mouseY))
    console.error("No mouse or touch offset found");
  return {
    x: mouseX,
    y: mouseY
  };
}
var handleCalculateWheelZoom = function(contextInstance, delta, step, disable, getTarget) {
  var scale = contextInstance.transformState.scale;
  var wrapperComponent = contextInstance.wrapperComponent, setup = contextInstance.setup;
  var maxScale = setup.maxScale, minScale = setup.minScale, zoomAnimation = setup.zoomAnimation, disablePadding = setup.disablePadding;
  var size = zoomAnimation.size, disabled = zoomAnimation.disabled;
  if (!wrapperComponent) {
    throw new Error("Wrapper is not mounted");
  }
  var targetScale = scale + delta * step;
  if (getTarget)
    return targetScale;
  var paddingEnabled = disable ? false : !disabled;
  var newScale = checkZoomBounds(roundNumber(targetScale, 3), minScale, maxScale, size, paddingEnabled && !disablePadding);
  return newScale;
};
var handleWheelZoomStop = function(contextInstance, event) {
  var previousWheelEvent = contextInstance.previousWheelEvent;
  var scale = contextInstance.transformState.scale;
  var _a = contextInstance.setup, maxScale = _a.maxScale, minScale = _a.minScale;
  if (!previousWheelEvent)
    return false;
  if (scale < maxScale || scale > minScale)
    return true;
  if (Math.sign(previousWheelEvent.deltaY) !== Math.sign(event.deltaY))
    return true;
  if (previousWheelEvent.deltaY > 0 && previousWheelEvent.deltaY < event.deltaY)
    return true;
  if (previousWheelEvent.deltaY < 0 && previousWheelEvent.deltaY > event.deltaY)
    return true;
  if (Math.sign(previousWheelEvent.deltaY) !== Math.sign(event.deltaY))
    return true;
  return false;
};
var isPinchStartAllowed = function(contextInstance, event) {
  var _a = contextInstance.setup.pinch, disabled = _a.disabled, excluded = _a.excluded;
  var isInitialized = contextInstance.isInitialized;
  var target = event.target;
  var isAllowed = isInitialized && !disabled && target;
  if (!isAllowed)
    return false;
  var isExcluded = isExcludedNode(target, excluded);
  if (isExcluded)
    return false;
  return true;
};
var isPinchAllowed = function(contextInstance) {
  var disabled = contextInstance.setup.pinch.disabled;
  var isInitialized = contextInstance.isInitialized, pinchStartDistance = contextInstance.pinchStartDistance;
  var isAllowed = isInitialized && !disabled && pinchStartDistance;
  if (!isAllowed)
    return false;
  return true;
};
var calculateTouchMidPoint = function(event, scale, contentComponent) {
  var contentRect = contentComponent.getBoundingClientRect();
  var touches = event.touches;
  var firstPointX = roundNumber(touches[0].clientX - contentRect.left, 5);
  var firstPointY = roundNumber(touches[0].clientY - contentRect.top, 5);
  var secondPointX = roundNumber(touches[1].clientX - contentRect.left, 5);
  var secondPointY = roundNumber(touches[1].clientY - contentRect.top, 5);
  return {
    x: (firstPointX + secondPointX) / 2 / scale,
    y: (firstPointY + secondPointY) / 2 / scale
  };
};
var getTouchDistance = function(event) {
  return Math.sqrt(Math.pow(event.touches[0].pageX - event.touches[1].pageX, 2) + Math.pow(event.touches[0].pageY - event.touches[1].pageY, 2));
};
var calculatePinchZoom = function(contextInstance, currentDistance) {
  var pinchStartScale = contextInstance.pinchStartScale, pinchStartDistance = contextInstance.pinchStartDistance, setup = contextInstance.setup;
  var maxScale = setup.maxScale, minScale = setup.minScale, zoomAnimation = setup.zoomAnimation, disablePadding = setup.disablePadding;
  var size = zoomAnimation.size, disabled = zoomAnimation.disabled;
  if (!pinchStartScale || pinchStartDistance === null || !currentDistance) {
    throw new Error("Pinch touches distance was not provided");
  }
  if (currentDistance < 0) {
    return contextInstance.transformState.scale;
  }
  var touchProportion = currentDistance / pinchStartDistance;
  var scaleDifference = touchProportion * pinchStartScale;
  return checkZoomBounds(roundNumber(scaleDifference, 2), minScale, maxScale, size, !disabled && !disablePadding);
};
var wheelStopEventTime = 160;
var wheelAnimationTime = 100;
var handleWheelStart = function(contextInstance, event) {
  var _a = contextInstance.props, onWheelStart = _a.onWheelStart, onZoomStart = _a.onZoomStart;
  if (!contextInstance.wheelStopEventTimer) {
    handleCancelAnimation(contextInstance);
    handleCallback(getContext(contextInstance), event, onWheelStart);
    handleCallback(getContext(contextInstance), event, onZoomStart);
  }
};
var handleWheelZoom = function(contextInstance, event) {
  var _a = contextInstance.props, onWheel = _a.onWheel, onZoom = _a.onZoom;
  var contentComponent = contextInstance.contentComponent, setup = contextInstance.setup, transformState = contextInstance.transformState;
  var scale = transformState.scale;
  var limitToBounds = setup.limitToBounds, centerZoomedOut = setup.centerZoomedOut, zoomAnimation = setup.zoomAnimation, wheel = setup.wheel, disablePadding = setup.disablePadding, smooth = setup.smooth;
  var size = zoomAnimation.size, disabled = zoomAnimation.disabled;
  var step = wheel.step, smoothStep = wheel.smoothStep;
  if (!contentComponent) {
    throw new Error("Component not mounted");
  }
  event.preventDefault();
  event.stopPropagation();
  var delta = getDelta(event, null);
  var zoomStep = smooth ? smoothStep * Math.abs(event.deltaY) : step;
  var newScale = handleCalculateWheelZoom(contextInstance, delta, zoomStep, !event.ctrlKey);
  if (scale === newScale)
    return;
  var bounds = handleCalculateBounds(contextInstance, newScale);
  var mousePosition = getMousePosition(event, contentComponent, scale);
  var isPaddingDisabled = disabled || size === 0 || centerZoomedOut || disablePadding;
  var isLimitedToBounds = limitToBounds && isPaddingDisabled;
  var _b = handleCalculateZoomPositions(contextInstance, mousePosition.x, mousePosition.y, newScale, bounds, isLimitedToBounds), x = _b.x, y = _b.y;
  contextInstance.previousWheelEvent = event;
  contextInstance.setTransformState(newScale, x, y);
  handleCallback(getContext(contextInstance), event, onWheel);
  handleCallback(getContext(contextInstance), event, onZoom);
};
var handleWheelStop = function(contextInstance, event) {
  var _a = contextInstance.props, onWheelStop = _a.onWheelStop, onZoomStop = _a.onZoomStop;
  cancelTimeout(contextInstance.wheelAnimationTimer);
  contextInstance.wheelAnimationTimer = setTimeout(function() {
    if (!contextInstance.mounted)
      return;
    handleAlignToScaleBounds(contextInstance, event.x, event.y);
    contextInstance.wheelAnimationTimer = null;
  }, wheelAnimationTime);
  var hasStoppedZooming = handleWheelZoomStop(contextInstance, event);
  if (hasStoppedZooming) {
    cancelTimeout(contextInstance.wheelStopEventTimer);
    contextInstance.wheelStopEventTimer = setTimeout(function() {
      if (!contextInstance.mounted)
        return;
      contextInstance.wheelStopEventTimer = null;
      handleCallback(getContext(contextInstance), event, onWheelStop);
      handleCallback(getContext(contextInstance), event, onZoomStop);
    }, wheelStopEventTime);
  }
};
var getTouchCenter = function(event) {
  var totalX = 0;
  var totalY = 0;
  for (var i = 0; i < 2; i += 1) {
    totalX += event.touches[i].clientX;
    totalY += event.touches[i].clientY;
  }
  var x = totalX / 2;
  var y = totalY / 2;
  return { x, y };
};
var handlePinchStart = function(contextInstance, event) {
  var distance = getTouchDistance(event);
  contextInstance.pinchStartDistance = distance;
  contextInstance.lastDistance = distance;
  contextInstance.pinchStartScale = contextInstance.transformState.scale;
  contextInstance.isPanning = false;
  var center = getTouchCenter(event);
  contextInstance.pinchLastCenterX = center.x;
  contextInstance.pinchLastCenterY = center.y;
  handleCancelAnimation(contextInstance);
};
var handlePinchZoom = function(contextInstance, event) {
  var contentComponent = contextInstance.contentComponent, pinchStartDistance = contextInstance.pinchStartDistance, wrapperComponent = contextInstance.wrapperComponent;
  var scale = contextInstance.transformState.scale;
  var _a = contextInstance.setup, limitToBounds = _a.limitToBounds, centerZoomedOut = _a.centerZoomedOut, zoomAnimation = _a.zoomAnimation, alignmentAnimation = _a.alignmentAnimation;
  var disabled = zoomAnimation.disabled, size = zoomAnimation.size;
  if (pinchStartDistance === null || !contentComponent)
    return;
  var midPoint = calculateTouchMidPoint(event, scale, contentComponent);
  if (!Number.isFinite(midPoint.x) || !Number.isFinite(midPoint.y))
    return;
  var currentDistance = getTouchDistance(event);
  var newScale = calculatePinchZoom(contextInstance, currentDistance);
  var center = getTouchCenter(event);
  var panX = center.x - (contextInstance.pinchLastCenterX || 0);
  var panY = center.y - (contextInstance.pinchLastCenterY || 0);
  if (newScale === scale && panX === 0 && panY === 0)
    return;
  contextInstance.pinchLastCenterX = center.x;
  contextInstance.pinchLastCenterY = center.y;
  var bounds = handleCalculateBounds(contextInstance, newScale);
  var isPaddingDisabled = disabled || size === 0 || centerZoomedOut;
  var isLimitedToBounds = limitToBounds && isPaddingDisabled;
  var _b = handleCalculateZoomPositions(contextInstance, midPoint.x, midPoint.y, newScale, bounds, isLimitedToBounds), x = _b.x, y = _b.y;
  contextInstance.pinchMidpoint = midPoint;
  contextInstance.lastDistance = currentDistance;
  var sizeX = alignmentAnimation.sizeX, sizeY = alignmentAnimation.sizeY;
  var paddingValueX = getPaddingValue(contextInstance, sizeX);
  var paddingValueY = getPaddingValue(contextInstance, sizeY);
  var newPositionX = x + panX;
  var newPositionY = y + panY;
  var _c = getMouseBoundedPosition(newPositionX, newPositionY, bounds, limitToBounds, paddingValueX, paddingValueY, wrapperComponent), finalX = _c.x, finalY = _c.y;
  contextInstance.setTransformState(newScale, finalX, finalY);
};
var handlePinchStop = function(contextInstance) {
  var pinchMidpoint = contextInstance.pinchMidpoint;
  contextInstance.velocity = null;
  contextInstance.lastDistance = null;
  contextInstance.pinchMidpoint = null;
  contextInstance.pinchStartScale = null;
  contextInstance.pinchStartDistance = null;
  handleAlignToScaleBounds(contextInstance, pinchMidpoint === null || pinchMidpoint === void 0 ? void 0 : pinchMidpoint.x, pinchMidpoint === null || pinchMidpoint === void 0 ? void 0 : pinchMidpoint.y);
};
var handleDoubleClickStop = function(contextInstance, event) {
  var onZoomStop = contextInstance.props.onZoomStop;
  var animationTime = contextInstance.setup.doubleClick.animationTime;
  cancelTimeout(contextInstance.doubleClickStopEventTimer);
  contextInstance.doubleClickStopEventTimer = setTimeout(function() {
    contextInstance.doubleClickStopEventTimer = null;
    handleCallback(getContext(contextInstance), event, onZoomStop);
  }, animationTime);
};
var handleDoubleClickResetMode = function(contextInstance, event) {
  var _a = contextInstance.props, onZoomStart = _a.onZoomStart, onZoom = _a.onZoom;
  var _b = contextInstance.setup.doubleClick, animationTime = _b.animationTime, animationType = _b.animationType;
  handleCallback(getContext(contextInstance), event, onZoomStart);
  resetTransformations(contextInstance, animationTime, animationType, function() {
    return handleCallback(getContext(contextInstance), event, onZoom);
  });
  handleDoubleClickStop(contextInstance, event);
};
function getDoubleClickScale(mode, scale) {
  if (mode === "toggle") {
    return scale === 1 ? 1 : -1;
  }
  return mode === "zoomOut" ? -1 : 1;
}
function handleDoubleClick(contextInstance, event) {
  var setup = contextInstance.setup, doubleClickStopEventTimer = contextInstance.doubleClickStopEventTimer, transformState = contextInstance.transformState, contentComponent = contextInstance.contentComponent;
  var scale = transformState.scale;
  var _a = contextInstance.props, onZoomStart = _a.onZoomStart, onZoom = _a.onZoom;
  var _b = setup.doubleClick, disabled = _b.disabled, mode = _b.mode, step = _b.step, animationTime = _b.animationTime, animationType = _b.animationType;
  if (disabled)
    return;
  if (doubleClickStopEventTimer)
    return;
  if (mode === "reset") {
    return handleDoubleClickResetMode(contextInstance, event);
  }
  if (!contentComponent)
    return console.error("No ContentComponent found");
  var delta = getDoubleClickScale(mode, contextInstance.transformState.scale);
  var newScale = handleCalculateButtonZoom(contextInstance, delta, step);
  if (scale === newScale)
    return;
  handleCallback(getContext(contextInstance), event, onZoomStart);
  var mousePosition = getMousePosition(event, contentComponent, scale);
  var targetState = handleZoomToPoint(contextInstance, newScale, mousePosition.x, mousePosition.y);
  if (!targetState) {
    return console.error("Error during zoom event. New transformation state was not calculated.");
  }
  handleCallback(getContext(contextInstance), event, onZoom);
  animate(contextInstance, targetState, animationTime, animationType);
  handleDoubleClickStop(contextInstance, event);
}
var isDoubleClickAllowed = function(contextInstance, event) {
  var isInitialized = contextInstance.isInitialized, setup = contextInstance.setup, wrapperComponent = contextInstance.wrapperComponent;
  var _a = setup.doubleClick, disabled = _a.disabled, excluded = _a.excluded;
  var target = event.target;
  var isWrapperChild = wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.contains(target);
  var isAllowed = isInitialized && target && isWrapperChild && !disabled;
  if (!isAllowed)
    return false;
  var isExcluded = isExcludedNode(target, excluded);
  if (isExcluded)
    return false;
  return true;
};
var ZoomPanPinch = (
  /** @class */
  /* @__PURE__ */ function() {
    function ZoomPanPinch2(props) {
      var _this = this;
      this.mounted = true;
      this.pinchLastCenterX = null;
      this.pinchLastCenterY = null;
      this.onChangeCallbacks = /* @__PURE__ */ new Set();
      this.onInitCallbacks = /* @__PURE__ */ new Set();
      this.wrapperComponent = null;
      this.contentComponent = null;
      this.isInitialized = false;
      this.bounds = null;
      this.previousWheelEvent = null;
      this.wheelStopEventTimer = null;
      this.wheelAnimationTimer = null;
      this.isPanning = false;
      this.isWheelPanning = false;
      this.startCoords = null;
      this.lastTouch = null;
      this.distance = null;
      this.lastDistance = null;
      this.pinchStartDistance = null;
      this.pinchStartScale = null;
      this.pinchMidpoint = null;
      this.doubleClickStopEventTimer = null;
      this.velocity = null;
      this.velocityTime = null;
      this.lastMousePosition = null;
      this.animate = false;
      this.animation = null;
      this.maxBounds = null;
      this.pressedKeys = {};
      this.mount = function() {
        _this.initializeWindowEvents();
      };
      this.unmount = function() {
        _this.cleanupWindowEvents();
      };
      this.update = function(newProps) {
        _this.props = newProps;
        handleCalculateBounds(_this, _this.transformState.scale);
        _this.setup = createSetup(newProps);
      };
      this.initializeWindowEvents = function() {
        var _a, _b;
        var passive = makePassiveEventOption();
        var currentDocument = (_a = _this.wrapperComponent) === null || _a === void 0 ? void 0 : _a.ownerDocument;
        var currentWindow = currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.defaultView;
        (_b = _this.wrapperComponent) === null || _b === void 0 ? void 0 : _b.addEventListener("wheel", _this.onWheelPanning, passive);
        currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener("mousedown", _this.onPanningStart, passive);
        currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener("mousemove", _this.onPanning, passive);
        currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener("mouseup", _this.onPanningStop, passive);
        currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.addEventListener("mouseleave", _this.clearPanning, passive);
        currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener("keyup", _this.setKeyUnPressed, passive);
        currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener("keydown", _this.setKeyPressed, passive);
      };
      this.cleanupWindowEvents = function() {
        var _a, _b;
        var passive = makePassiveEventOption();
        var currentDocument = (_a = _this.wrapperComponent) === null || _a === void 0 ? void 0 : _a.ownerDocument;
        var currentWindow = currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.defaultView;
        currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener("mousedown", _this.onPanningStart, passive);
        currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener("mousemove", _this.onPanning, passive);
        currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener("mouseup", _this.onPanningStop, passive);
        currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.removeEventListener("mouseleave", _this.clearPanning, passive);
        currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener("keyup", _this.setKeyUnPressed, passive);
        currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener("keydown", _this.setKeyPressed, passive);
        document.removeEventListener("mouseleave", _this.clearPanning, passive);
        handleCancelAnimation(_this);
        (_b = _this.observer) === null || _b === void 0 ? void 0 : _b.disconnect();
      };
      this.handleInitializeWrapperEvents = function(wrapper) {
        var passive = makePassiveEventOption();
        wrapper.addEventListener("wheel", _this.onWheelZoom, passive);
        wrapper.addEventListener("dblclick", _this.onDoubleClick, passive);
        wrapper.addEventListener("touchstart", _this.onTouchPanningStart, passive);
        wrapper.addEventListener("touchmove", _this.onTouchPanning, passive);
        wrapper.addEventListener("touchend", _this.onTouchPanningStop, passive);
      };
      this.handleInitialize = function(wrapper, contentComponent) {
        var isCentered = false;
        var centerOnInit = _this.setup.centerOnInit;
        var hasTarget = function(entries, target) {
          for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {
            var entry = entries_1[_i];
            if (entry.target === target) {
              return true;
            }
          }
          return false;
        };
        _this.applyTransformation();
        _this.onInitCallbacks.forEach(function(callback) {
          callback(getContext(_this));
        });
        _this.observer = new ResizeObserver(function(entries) {
          if (hasTarget(entries, wrapper) || hasTarget(entries, contentComponent)) {
            if (centerOnInit && !isCentered) {
              var currentWidth = contentComponent.offsetWidth;
              var currentHeight = contentComponent.offsetHeight;
              if (currentWidth > 0 || currentHeight > 0) {
                isCentered = true;
                _this.setCenter();
              }
            } else {
              handleCancelAnimation(_this);
              handleCalculateBounds(_this, _this.transformState.scale);
              handleAlignToBounds(_this, 0);
            }
          }
        });
        _this.observer.observe(wrapper);
        _this.observer.observe(contentComponent);
      };
      this.onWheelZoom = function(event) {
        var disabled = _this.setup.disabled;
        if (disabled)
          return;
        var isAllowed = isWheelAllowed(_this, event);
        if (!isAllowed)
          return;
        var keysPressed = _this.isPressingKeys(_this.setup.wheel.activationKeys);
        if (!keysPressed)
          return;
        handleWheelStart(_this, event);
        handleWheelZoom(_this, event);
        handleWheelStop(_this, event);
      };
      this.onWheelPanning = function(event) {
        var _a = _this.setup, disabled = _a.disabled, wheel = _a.wheel, panning = _a.panning;
        if (!_this.wrapperComponent || !_this.contentComponent || disabled || !wheel.wheelDisabled || panning.disabled || !panning.wheelPanning || event.ctrlKey) {
          return;
        }
        event.preventDefault();
        event.stopPropagation();
        var _b = _this.transformState, positionX = _b.positionX, positionY = _b.positionY;
        var mouseX = positionX - event.deltaX;
        var mouseY = positionY - event.deltaY;
        var newPositionX = panning.lockAxisX ? positionX : mouseX;
        var newPositionY = panning.lockAxisY ? positionY : mouseY;
        var _c = _this.setup.alignmentAnimation, sizeX = _c.sizeX, sizeY = _c.sizeY;
        var paddingValueX = getPaddingValue(_this, sizeX);
        var paddingValueY = getPaddingValue(_this, sizeY);
        if (newPositionX === positionX && newPositionY === positionY)
          return;
        handleNewPosition(_this, newPositionX, newPositionY, paddingValueX, paddingValueY);
      };
      this.onPanningStart = function(event) {
        var disabled = _this.setup.disabled;
        var onPanningStart = _this.props.onPanningStart;
        if (disabled)
          return;
        var isAllowed = isPanningStartAllowed(_this, event);
        if (!isAllowed)
          return;
        var keysPressed = _this.isPressingKeys(_this.setup.panning.activationKeys);
        if (!keysPressed)
          return;
        if (event.button === 0 && !_this.setup.panning.allowLeftClickPan)
          return;
        if (event.button === 1 && !_this.setup.panning.allowMiddleClickPan)
          return;
        if (event.button === 2 && !_this.setup.panning.allowRightClickPan)
          return;
        event.preventDefault();
        event.stopPropagation();
        handleCancelAnimation(_this);
        handlePanningStart(_this, event);
        handleCallback(getContext(_this), event, onPanningStart);
      };
      this.onPanning = function(event) {
        var disabled = _this.setup.disabled;
        var onPanning = _this.props.onPanning;
        if (disabled)
          return;
        var isAllowed = isPanningAllowed(_this);
        if (!isAllowed)
          return;
        var keysPressed = _this.isPressingKeys(_this.setup.panning.activationKeys);
        if (!keysPressed)
          return;
        event.preventDefault();
        event.stopPropagation();
        handlePanning(_this, event.clientX, event.clientY);
        handleCallback(getContext(_this), event, onPanning);
      };
      this.onPanningStop = function(event) {
        var onPanningStop = _this.props.onPanningStop;
        if (_this.isPanning) {
          handlePanningEnd(_this);
          handleCallback(getContext(_this), event, onPanningStop);
        }
      };
      this.onPinchStart = function(event) {
        var disabled = _this.setup.disabled;
        var _a = _this.props, onPinchingStart = _a.onPinchingStart, onZoomStart = _a.onZoomStart;
        if (disabled)
          return;
        var isAllowed = isPinchStartAllowed(_this, event);
        if (!isAllowed)
          return;
        handlePinchStart(_this, event);
        handleCancelAnimation(_this);
        handleCallback(getContext(_this), event, onPinchingStart);
        handleCallback(getContext(_this), event, onZoomStart);
      };
      this.onPinch = function(event) {
        var disabled = _this.setup.disabled;
        var _a = _this.props, onPinching = _a.onPinching, onZoom = _a.onZoom;
        if (disabled)
          return;
        var isAllowed = isPinchAllowed(_this);
        if (!isAllowed)
          return;
        event.preventDefault();
        event.stopPropagation();
        handlePinchZoom(_this, event);
        handleCallback(getContext(_this), event, onPinching);
        handleCallback(getContext(_this), event, onZoom);
      };
      this.onPinchStop = function(event) {
        var _a = _this.props, onPinchingStop = _a.onPinchingStop, onZoomStop = _a.onZoomStop;
        if (_this.pinchStartScale) {
          handlePinchStop(_this);
          handleCallback(getContext(_this), event, onPinchingStop);
          handleCallback(getContext(_this), event, onZoomStop);
        }
      };
      this.onTouchPanningStart = function(event) {
        var disabled = _this.setup.disabled;
        var onPanningStart = _this.props.onPanningStart;
        if (disabled)
          return;
        var isAllowed = isPanningStartAllowed(_this, event);
        if (!isAllowed)
          return;
        var isDoubleTap = _this.lastTouch && +/* @__PURE__ */ new Date() - _this.lastTouch < 200 && event.touches.length === 1;
        if (!isDoubleTap) {
          _this.lastTouch = +/* @__PURE__ */ new Date();
          handleCancelAnimation(_this);
          var touches = event.touches;
          var isPanningAction = touches.length === 1;
          var isPinchAction = touches.length === 2;
          if (isPanningAction) {
            handleCancelAnimation(_this);
            handlePanningStart(_this, event);
            handleCallback(getContext(_this), event, onPanningStart);
          }
          if (isPinchAction) {
            _this.onPinchStart(event);
          }
        }
      };
      this.onTouchPanning = function(event) {
        var disabled = _this.setup.disabled;
        var onPanning = _this.props.onPanning;
        if (_this.isPanning && event.touches.length === 1) {
          if (disabled)
            return;
          var isAllowed = isPanningAllowed(_this);
          if (!isAllowed)
            return;
          event.preventDefault();
          event.stopPropagation();
          var touch = event.touches[0];
          handlePanning(_this, touch.clientX, touch.clientY);
          handleCallback(getContext(_this), event, onPanning);
        } else if (event.touches.length > 1) {
          _this.onPinch(event);
        }
      };
      this.onTouchPanningStop = function(event) {
        _this.onPanningStop(event);
        _this.onPinchStop(event);
      };
      this.onDoubleClick = function(event) {
        var disabled = _this.setup.disabled;
        if (disabled)
          return;
        var isAllowed = isDoubleClickAllowed(_this, event);
        if (!isAllowed)
          return;
        handleDoubleClick(_this, event);
      };
      this.clearPanning = function(event) {
        if (_this.isPanning) {
          _this.onPanningStop(event);
        }
      };
      this.setKeyPressed = function(e) {
        _this.pressedKeys[e.key] = true;
      };
      this.setKeyUnPressed = function(e) {
        _this.pressedKeys[e.key] = false;
      };
      this.isPressingKeys = function(keys) {
        if (!keys.length) {
          return true;
        }
        return Boolean(keys.find(function(key) {
          return _this.pressedKeys[key];
        }));
      };
      this.setTransformState = function(scale, positionX, positionY) {
        var onTransformed = _this.props.onTransformed;
        if (!Number.isNaN(scale) && !Number.isNaN(positionX) && !Number.isNaN(positionY)) {
          if (scale !== _this.transformState.scale) {
            _this.transformState.previousScale = _this.transformState.scale;
            _this.transformState.scale = scale;
          }
          _this.transformState.positionX = positionX;
          _this.transformState.positionY = positionY;
          _this.applyTransformation();
          var ctx_1 = getContext(_this);
          _this.onChangeCallbacks.forEach(function(callback) {
            return callback(ctx_1);
          });
          handleCallback(ctx_1, { scale, positionX, positionY }, onTransformed);
        } else {
          console.error("Detected NaN set state values");
        }
      };
      this.setCenter = function() {
        if (_this.wrapperComponent && _this.contentComponent) {
          var targetState = getCenterPosition(_this.transformState.scale, _this.wrapperComponent, _this.contentComponent);
          _this.setTransformState(targetState.scale, targetState.positionX, targetState.positionY);
        }
      };
      this.handleTransformStyles = function(x, y, scale) {
        if (_this.props.customTransform) {
          return _this.props.customTransform(x, y, scale);
        }
        return getTransformStyles(x, y, scale);
      };
      this.applyTransformation = function() {
        if (!_this.mounted || !_this.contentComponent)
          return;
        var _a = _this.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;
        var transform = _this.handleTransformStyles(positionX, positionY, scale);
        _this.contentComponent.style.transform = transform;
      };
      this.getContext = function() {
        return getContext(_this);
      };
      this.onChange = function(callback) {
        if (!_this.onChangeCallbacks.has(callback)) {
          _this.onChangeCallbacks.add(callback);
        }
        return function() {
          _this.onChangeCallbacks.delete(callback);
        };
      };
      this.onInit = function(callback) {
        if (!_this.onInitCallbacks.has(callback)) {
          _this.onInitCallbacks.add(callback);
        }
        return function() {
          _this.onInitCallbacks.delete(callback);
        };
      };
      this.init = function(wrapperComponent, contentComponent) {
        _this.cleanupWindowEvents();
        _this.wrapperComponent = wrapperComponent;
        _this.contentComponent = contentComponent;
        handleCalculateBounds(_this, _this.transformState.scale);
        _this.handleInitializeWrapperEvents(wrapperComponent);
        _this.handleInitialize(wrapperComponent, contentComponent);
        _this.initializeWindowEvents();
        _this.isInitialized = true;
        var ctx = getContext(_this);
        handleCallback(ctx, void 0, _this.props.onInit);
      };
      this.props = props;
      this.setup = createSetup(this.props);
      this.transformState = createState(this.props);
    }
    return ZoomPanPinch2;
  }()
);
var Context = import_react.default.createContext(null);
var getContent = function(children, ctx) {
  if (typeof children === "function") {
    return children(ctx);
  }
  return children;
};
var TransformWrapper = import_react.default.forwardRef(function(props, ref) {
  var instance = (0, import_react.useRef)(new ZoomPanPinch(props)).current;
  var content = getContent(props.children, getControls(instance));
  (0, import_react.useImperativeHandle)(ref, function() {
    return getControls(instance);
  }, [instance]);
  (0, import_react.useEffect)(function() {
    instance.update(props);
  }, [instance, props]);
  return import_react.default.createElement(Context.Provider, { value: instance }, content);
});
var KeepScale = import_react.default.forwardRef(function(props, ref) {
  var localRef = (0, import_react.useRef)(null);
  var instance = (0, import_react.useContext)(Context);
  (0, import_react.useEffect)(function() {
    return instance.onChange(function(ctx) {
      if (localRef.current) {
        var positionX = 0;
        var positionY = 0;
        localRef.current.style.transform = instance.handleTransformStyles(positionX, positionY, 1 / ctx.instance.transformState.scale);
      }
    });
  }, [instance]);
  return import_react.default.createElement("div", __assign({}, props, { ref: mergeRefs([localRef, ref]) }));
});
var initialElementRect = {
  width: 0,
  height: 0,
  y: 0,
  x: 0,
  top: 0,
  bottom: 0,
  left: 0,
  right: 0
};
var useResize = function(ref, onResize, dependencies) {
  var resizeObserverRef = (0, import_react.useRef)();
  var rectRef = (0, import_react.useRef)(initialElementRect);
  var didUnmount = (0, import_react.useRef)(false);
  (0, import_react.useLayoutEffect)(function() {
    var _a;
    didUnmount.current = false;
    if (!("ResizeObserver" in window)) {
      return;
    }
    if (ref) {
      resizeObserverRef.current = new ResizeObserver(function(entries) {
        var newSize = ref.getBoundingClientRect();
        if (!Array.isArray(entries) || !entries.length || didUnmount.current || newSize.width === rectRef.current.width && newSize.height === rectRef.current.height)
          return;
        onResize(newSize, ref);
        rectRef.current = newSize;
      });
      (_a = resizeObserverRef.current) === null || _a === void 0 ? void 0 : _a.observe(ref);
    }
    return function() {
      var _a2;
      didUnmount.current = true;
      if (ref) {
        (_a2 = resizeObserverRef.current) === null || _a2 === void 0 ? void 0 : _a2.unobserve(ref);
      }
    };
  }, __spreadArray([onResize, ref], dependencies, true));
};
var previewStyles = {
  position: "absolute",
  zIndex: 2,
  top: "0px",
  left: "0px",
  boxSizing: "border-box",
  border: "3px solid red",
  transformOrigin: "0% 0%",
  boxShadow: "rgba(0,0,0,0.2) 0 0 0 10000000px"
};
var MiniMap = function(_a) {
  var _b = _a.width, width = _b === void 0 ? 200 : _b, _c = _a.height, height = _c === void 0 ? 200 : _c, _d = _a.borderColor, borderColor = _d === void 0 ? "red" : _d, children = _a.children, rest = __rest(_a, ["width", "height", "borderColor", "children"]);
  var _e = (0, import_react.useState)(false), initialized = _e[0], setInitialized = _e[1];
  var instance = useTransformContext();
  var miniMapInstance = (0, import_react.useRef)(null);
  var mainRef = (0, import_react.useRef)(null);
  var wrapperRef = (0, import_react.useRef)(null);
  var previewRef = (0, import_react.useRef)(null);
  var getViewportSize = (0, import_react.useCallback)(function() {
    if (instance.wrapperComponent) {
      var rect = instance.wrapperComponent.getBoundingClientRect();
      return {
        width: rect.width,
        height: rect.height
      };
    }
    return {
      width: 0,
      height: 0
    };
  }, [instance.wrapperComponent]);
  var getContentSize = (0, import_react.useCallback)(function() {
    if (instance.contentComponent) {
      var rect = instance.contentComponent.getBoundingClientRect();
      return {
        width: rect.width / instance.transformState.scale,
        height: rect.height / instance.transformState.scale
      };
    }
    return {
      width: 0,
      height: 0
    };
  }, [instance.contentComponent, instance.transformState.scale]);
  var computeMiniMapScale = (0, import_react.useCallback)(function() {
    var contentSize = getContentSize();
    var scaleX = width / contentSize.width;
    var scaleY = height / contentSize.height;
    var scale = scaleY > scaleX ? scaleX : scaleY;
    return scale;
  }, [getContentSize, height, width]);
  var computeMiniMapSize = function() {
    var contentSize = getContentSize();
    var scaleX = width / contentSize.width;
    var scaleY = height / contentSize.height;
    if (scaleY > scaleX) {
      return { width, height: contentSize.height * scaleX };
    }
    return { width: contentSize.width * scaleY, height };
  };
  var computeMiniMapStyle = function() {
    var scale = computeMiniMapScale();
    var style = {
      transform: "scale(".concat(scale || 1, ")"),
      transformOrigin: "0% 0%",
      position: "absolute",
      boxSizing: "border-box",
      zIndex: 1,
      overflow: "hidden"
    };
    Object.keys(style).forEach(function(key) {
      if (wrapperRef.current) {
        wrapperRef.current.style[key] = style[key];
      }
    });
  };
  var transformMiniMap = function() {
    computeMiniMapStyle();
    var miniSize = computeMiniMapSize();
    var wrapSize = getContentSize();
    if (wrapperRef.current) {
      wrapperRef.current.style.width = "".concat(wrapSize.width, "px");
      wrapperRef.current.style.height = "".concat(wrapSize.height, "px");
    }
    if (mainRef.current) {
      mainRef.current.style.width = "".concat(miniSize.width, "px");
      mainRef.current.style.height = "".concat(miniSize.height, "px");
    }
    if (previewRef.current) {
      var size = getViewportSize();
      var scale = computeMiniMapScale();
      var previewScale = scale * (1 / instance.transformState.scale);
      var transform = instance.handleTransformStyles(-instance.transformState.positionX * previewScale, -instance.transformState.positionY * previewScale, 1);
      previewRef.current.style.transform = transform;
      previewRef.current.style.width = "".concat(size.width * previewScale, "px");
      previewRef.current.style.height = "".concat(size.height * previewScale, "px");
    }
  };
  var initialize = function() {
    transformMiniMap();
  };
  useTransformEffect(function() {
    transformMiniMap();
  });
  useTransformInit(function() {
    initialize();
    setInitialized(true);
  });
  useResize(instance.contentComponent, initialize, [initialized]);
  (0, import_react.useEffect)(function() {
    return instance.onChange(function(zpp) {
      var scale = computeMiniMapScale();
      if (miniMapInstance.current) {
        miniMapInstance.current.instance.transformState.scale = zpp.instance.transformState.scale;
        miniMapInstance.current.instance.transformState.positionX = zpp.instance.transformState.positionX * scale;
        miniMapInstance.current.instance.transformState.positionY = zpp.instance.transformState.positionY * scale;
      }
    });
  }, [computeMiniMapScale, instance, miniMapInstance]);
  var wrapperStyle = (0, import_react.useMemo)(function() {
    return {
      position: "relative",
      zIndex: 2,
      overflow: "hidden"
    };
  }, []);
  return import_react.default.createElement(
    "div",
    __assign({}, rest, { ref: mainRef, style: wrapperStyle, className: "rzpp-mini-map ".concat(rest.className || "") }),
    import_react.default.createElement("div", __assign({}, rest, { ref: wrapperRef, className: "rzpp-wrapper" }), children),
    import_react.default.createElement("div", { className: "rzpp-preview", ref: previewRef, style: __assign(__assign({}, previewStyles), { borderColor }) })
  );
};
function styleInject(css, ref) {
  if (ref === void 0) ref = {};
  var insertAt = ref.insertAt;
  if (!css || typeof document === "undefined") {
    return;
  }
  var head = document.head || document.getElementsByTagName("head")[0];
  var style = document.createElement("style");
  style.type = "text/css";
  if (insertAt === "top") {
    if (head.firstChild) {
      head.insertBefore(style, head.firstChild);
    } else {
      head.appendChild(style);
    }
  } else {
    head.appendChild(style);
  }
  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
}
var css_248z = ".transform-component-module_wrapper__SPB86 {\n  position: relative;\n  width: -moz-fit-content;\n  width: fit-content;\n  height: -moz-fit-content;\n  height: fit-content;\n  overflow: hidden;\n  -webkit-touch-callout: none; /* iOS Safari */\n  -webkit-user-select: none; /* Safari */\n  -khtml-user-select: none; /* Konqueror HTML */\n  -moz-user-select: none; /* Firefox */\n  -ms-user-select: none; /* Internet Explorer/Edge */\n  user-select: none;\n  margin: 0;\n  padding: 0;\n  transform: translate3d(0, 0, 0);\n}\n.transform-component-module_content__FBWxo {\n  display: flex;\n  flex-wrap: wrap;\n  width: -moz-fit-content;\n  width: fit-content;\n  height: -moz-fit-content;\n  height: fit-content;\n  margin: 0;\n  padding: 0;\n  transform-origin: 0% 0%;\n}\n.transform-component-module_content__FBWxo img {\n  pointer-events: none;\n}\n";
var styles = { "wrapper": "transform-component-module_wrapper__SPB86", "content": "transform-component-module_content__FBWxo" };
styleInject(css_248z);
var TransformComponent = function(_a) {
  var children = _a.children, _b = _a.wrapperClass, wrapperClass = _b === void 0 ? "" : _b, _c = _a.contentClass, contentClass = _c === void 0 ? "" : _c, wrapperStyle = _a.wrapperStyle, contentStyle = _a.contentStyle, _d = _a.wrapperProps, wrapperProps = _d === void 0 ? {} : _d, _e = _a.contentProps, contentProps = _e === void 0 ? {} : _e;
  var _f = (0, import_react.useContext)(Context), init = _f.init, cleanupWindowEvents = _f.cleanupWindowEvents;
  var wrapperRef = (0, import_react.useRef)(null);
  var contentRef = (0, import_react.useRef)(null);
  (0, import_react.useEffect)(function() {
    var wrapper = wrapperRef.current;
    var content = contentRef.current;
    if (wrapper !== null && content !== null && init) {
      init === null || init === void 0 ? void 0 : init(wrapper, content);
    }
    return function() {
      cleanupWindowEvents === null || cleanupWindowEvents === void 0 ? void 0 : cleanupWindowEvents();
    };
  }, []);
  return import_react.default.createElement(
    "div",
    __assign({}, wrapperProps, { ref: wrapperRef, className: "".concat(baseClasses.wrapperClass, " ").concat(styles.wrapper, " ").concat(wrapperClass), style: wrapperStyle }),
    import_react.default.createElement("div", __assign({}, contentProps, { ref: contentRef, className: "".concat(baseClasses.contentClass, " ").concat(styles.content, " ").concat(contentClass), style: contentStyle }), children)
  );
};
var useTransformContext = function() {
  var libraryContext = (0, import_react.useContext)(Context);
  if (!libraryContext) {
    throw new Error("Transform context must be placed inside TransformWrapper");
  }
  return libraryContext;
};
var useControls = function() {
  var libraryContext = useTransformContext();
  return getControls(libraryContext);
};
var useTransformInit = function(callback) {
  var libraryContext = useTransformContext();
  (0, import_react.useEffect)(function() {
    var unmountCallback;
    var unmount;
    if (libraryContext.contentComponent && libraryContext.wrapperComponent) {
      unmountCallback = callback(getState(libraryContext));
    } else {
      unmount = libraryContext.onInit(function(ref) {
        unmountCallback = callback(getState(ref.instance));
      });
    }
    return function() {
      unmount === null || unmount === void 0 ? void 0 : unmount();
      unmountCallback === null || unmountCallback === void 0 ? void 0 : unmountCallback();
    };
  }, []);
};
var useTransformEffect = function(callback) {
  var libraryContext = useTransformContext();
  (0, import_react.useEffect)(function() {
    var unmountCallback;
    var unmount = libraryContext.onChange(function(ref) {
      unmountCallback = callback(getState(ref.instance));
    });
    return function() {
      unmount();
      unmountCallback === null || unmountCallback === void 0 ? void 0 : unmountCallback();
    };
  }, [callback, libraryContext]);
};
function useTransformComponent(callback) {
  var libraryContext = useTransformContext();
  var _a = (0, import_react.useState)(callback(getState(libraryContext))), transformRender = _a[0], setTransformRender = _a[1];
  (0, import_react.useEffect)(function() {
    var mounted = true;
    var unmount = libraryContext.onChange(function(ref) {
      if (mounted) {
        setTransformRender(callback(getState(ref.instance)));
      }
    });
    return function() {
      unmount();
      mounted = false;
    };
  }, [callback, libraryContext]);
  return transformRender;
}
export {
  Context,
  KeepScale,
  MiniMap,
  TransformComponent,
  TransformWrapper,
  getCenterPosition,
  getMatrixTransformStyles,
  getTransformStyles,
  useControls,
  useTransformComponent,
  useTransformContext,
  useTransformEffect,
  useTransformInit
};
//# sourceMappingURL=react-zoom-pan-pinch.js.map
