{"Language": "De<PERSON>ch", "Translation": {"Login Required": "<PERSON><PERSON><PERSON><PERSON>", "Please login to access settings": "Bitte melden Sie sich an, um auf die Einstellungen zuzugreifen", "Username": "<PERSON><PERSON><PERSON><PERSON>", "Password": "Passwort", "Login": "Anmelden", "Login Error": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Incorrect username or password": "Falscher Benutzername oder Passwort", "Please enter your username and password": "Bitte geben Sie Ihren Benutzernamen und Ihr Passwort ein", "Video Script Settings": "**Drehbuch / Topic des Videos**", "Video Subject": "Worum soll es in dem Video gehen? (Geben Sie ein Keyword an, :red[Dank KI wird automatisch ein Drehbuch generieren])", "Script Language": "Welche Sprache soll zum Generieren von Drehbüchern  verwendet werden? :red[KI generiert anhand dieses Begriffs das Drehbuch]", "Generate Video Script and Keywords": "<PERSON><PERSON><PERSON> hier, um mithilfe von K<PERSON> ein [Video Drehbuch] und [Video Keywords] basierend auf dem **Keyword** zu generieren.", "Auto Detect": "Automatisch erkennen", "Video Script": "Drehbuch (Storybook) (:blue[① Optional, KI generiert  ② Die richtige Zeichensetzung hilft bei der Erstellung von Untertiteln])", "Generate Video Keywords": "<PERSON><PERSON><PERSON>, um KI zum Generieren zu verwenden [Video Keywords] basierend auf dem **Drehbuch**", "Please Enter the Video Subject": "Bitte geben Sie zu<PERSON>t das Drehbuch an", "Generating Video Script and Keywords": "KI generiert ein Drehbuch und Schlüsselwörter...", "Generating Video Keywords": "KI generiert Video-Schlüsselwörter...", "Video Keywords": "Video Schlüsselwörter (:blue[① Optional, KI generiert ② Verwende **, (Kommas)** zur Trennung der Wörter, in englischer Sprache])", "Video Settings": "**Video Einstellungen**", "Video Concat Mode": "Videoverkettungsmodus", "Random": "Zufällige Verkettung (empfohlen)", "Sequential": "Sequentielle Verkettung", "Video Transition Mode": "Video Übergangsmodus", "None": "<PERSON><PERSON>", "Shuffle": "Zufällige Übergänge", "FadeIn": "FadeIn", "FadeOut": "FadeOut", "SlideIn": "SlideIn", "SlideOut": "SlideOut", "Video Ratio": "Video-Seitenverhältnis", "Portrait": "Portrait 9:16", "Landscape": "Landschaft 16:9", "Clip Duration": "Maximale Dauer einzelner Videoclips in sekunden", "Number of Videos Generated Simultaneously": "<PERSON><PERSON><PERSON> der parallel generierten Videos", "Audio Settings": "**Audio Einstellungen**", "Speech Synthesis": "Sprachausgabe", "Speech Region": "Region(:red[<PERSON>rf<PERSON><PERSON>lich，[Region abrufen](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Key": "API-Schlüssel(:red[<PERSON><PERSON><PERSON><PERSON><PERSON>，[API-Schlüssel abrufen](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Volume": "Lautstärke der Sprachausgabe", "Speech Rate": "Lesegeschwindigkeit (1,0 bedeutet 1x)", "Male": "<PERSON><PERSON><PERSON><PERSON>", "Female": "<PERSON><PERSON><PERSON>", "Background Music": "Hintergrundmusik", "No Background Music": "<PERSON>ne Hintergrundmusik", "Random Background Music": "Zufällig erzeugte Hintergrundmusik", "Custom Background Music": "Benutzerdefinierte Hintergrundmusik", "Custom Background Music File": "Bitte gib den Pfad zur Musikdatei an:", "Background Music Volume": "Lautstärke: (0.2 entspricht 20%, sollte nicht zu laut sein)", "Subtitle Settings": "**Untertitel-Einstellungen**", "Enable Subtitles": "Untertitel aktivieren (Wenn diese Option deaktiviert ist, werden die Einstellungen nicht genutzt)", "Font": "Schriftart des Untertitels", "Position": "Ausrichtung des Untertitels", "Top": "<PERSON><PERSON>", "Center": "<PERSON><PERSON><PERSON>", "Bottom": "<PERSON>ten (empfohlen)", "Custom": "Benutzerdefinierte Position (70, was 70% von oben bedeutet)", "Font Size": "Schriftgröße für Untertitel", "Font Color": "Schriftfarbe", "Stroke Color": "<PERSON><PERSON><PERSON>", "Stroke Width": "Breite der Untertitelkontur", "Generate Video": "Generiere Videos durch KI", "Video Script and Subject Cannot Both Be Empty": "Das Video-Thema und Drehbuch dürfen nicht beide leer sein", "Generating Video": "Video wird erstellt, bitte warten...", "Start Generating Video": "Beginne mit der Generierung", "Video Generation Completed": "Video erfolgreich generiert", "Video Generation Failed": "Video Generierung fehlgeschlagen", "You can download the generated video from the following links": "<PERSON>e können das generierte Video über die folgenden Links herunterladen", "Basic Settings": "**Grundeinstellungen** (:blue[Klicken zum Erweitern])", "Language": "<PERSON><PERSON><PERSON>", "Pexels API Key": "Pexels API-Schlüssel ([API-Schlüssel abrufen](https://www.pexels.com/api/))", "Pixabay API Key": "Pixabay API-Schlüssel ([API-Schlüssel abrufen](https://pixabay.com/api/docs/#api_search_videos))", "LLM Provider": "KI-Modellanbieter", "API Key": "API-Schlüssel (:red[<PERSON><PERSON><PERSON><PERSON><PERSON>])", "Base Url": "Basis-URL", "Account ID": "Konto-ID (Aus dem Cloudflare-Dashboard)", "Model Name": "<PERSON><PERSON><PERSON>", "Please Enter the LLM API Key": "Bitte geben Si<PERSON> den **KI-Modell API-Schlüssel** ein", "Please Enter the Pexels API Key": "Bitte geben Si<PERSON> den **Pexels API-Schlüssel** ein", "Please Enter the Pixabay API Key": "Bitte geben Si<PERSON> den **Pixabay API-Schlüssel** ein", "Get Help": "<PERSON><PERSON> <PERSON> Hilfe benötigen oder Fragen haben, können Si<PERSON> dem Discord beitreten: https://harryai.cc", "Video Source": "<PERSON><PERSON><PERSON>", "TikTok": "TikTok (TikTok-Unterstützung kommt bald)", "Bilibili": "Bilibili (Bilibili-Unterstützung kommt bald)", "Xiaohongshu": "<PERSON><PERSON><PERSON> (Xiaohongshu-Unterstützung kommt bald)", "Local file": "Lokale Datei", "Play Voice": "Sprachausgabe abspielen", "Voice Example": "Dies ist ein Beispieltext zum Testen der Sprachsynthese", "Synthesizing Voice": "Sprachsynthese l<PERSON>t, bitte warten...", "TTS Provider": "Sprachsynthese-Anbieter auswählen", "TTS Servers": "TTS-Server", "No voices available for the selected TTS server. Please select another server.": "<PERSON>ine Stimmen für den ausgewählten TTS-Server verfügbar. Bitte wählen Sie einen anderen Server.", "SiliconFlow API Key": "SiliconFlow API-Schlüssel", "SiliconFlow TTS Settings": "SiliconFlow TTS-Einstellungen", "Speed: Range [0.25, 4.0], default is 1.0": "Geschwindigkeit: Bereich [0.25, 4.0], Standardwert ist 1.0", "Volume: Uses Speech Volume setting, default 1.0 maps to gain 0": "Lautstärke: Verwendet die Sprachlautstärke-Einstellung, Standardwert 1.0 entspricht Verstärkung 0", "Hide Log": "Protokoll ausblenden", "Hide Basic Settings": "Basis-Einstellungen ausblenden\n\nWenn diese Option deaktiviert ist, wird die Basis-Einstellungen-Leiste nicht auf der Seite angezeigt.\n\nWenn Sie sie erneut anzeigen möchten, setzen <PERSON>e `hide_config = false` in `config.toml`", "LLM Settings": "**LLM-Einstellungen**", "Video Source Settings": "**Videoquellen-Einstellungen**"}}