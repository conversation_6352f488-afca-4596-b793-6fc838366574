name: Lint, Test & Build

concurrency:
  group: "${{ github.workflow }}-${{ github.ref }}"
  cancel-in-progress: true

on:
  workflow_dispatch:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  main:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4.2.2
        with:
          fetch-depth: 2

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.0.0
        with:
          version: latest

      - name: Setup Node.js
        uses: actions/setup-node@v4.1.0
        with:
          cache: "pnpm"
          node-version: 22

      - name: Install Dependencies
        run: pnpm install --frozen-lockfile

      - name: Lint
        run: pnpm run lint

      - name: Format
        run: pnpm run format

      - name: Test
        run: pnpm run test

      - name: Build
        run: pnpm run build
