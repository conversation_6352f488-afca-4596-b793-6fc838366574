{"extends": "../../tsconfig.base.json", "compilerOptions": {"module": "commonjs", "forceConsistentCasingInFileNames": true, "strict": true, "esModuleInterop": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}