{"version": 3, "file": "DecompressionTable.js", "sourceRoot": "", "sources": ["../../src/util/DecompressionTable.ts"], "names": [], "mappings": ";;;AAAA,4DAAuD;AAEvD,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;AAE3C,MAAa,kBAAkB;IAA/B;QACqB,UAAK,GAAc,EAAE,CAAC;IAmF3C,CAAC;IAjFQ,WAAW,CAAC,QAAmB;QACpC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,IAAI,CAAC,MAAM;YAAE,OAAO;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC1B,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,IAAI,GAAmB,KAAK,CAAC;YACjC,IAAI,KAAc,CAAC;YACnB,OAAO,CAAC,GAAG,MAAM,EAAE,CAAC;gBAClB,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzB,IAAI,GAAG,IAAI,GAAW,KAAK,CAAC;oBAC5B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACjB,CAAC,EAAE,CAAC;gBACN,CAAC;qBAAM,CAAC;oBACN,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,CAAC,GAAG,MAAM;YAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IAEM,UAAU,CAAC,KAAa;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAEzB,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC;IACtB,CAAC;IAEM,UAAU,CAAC,KAAc;QAC9B,QAAQ,OAAO,KAAK,EAAE,CAAC;YACrB,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC;YACD,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,IAAI,CAAC,KAAK;oBAAE,OAAO,IAAI,CAAC;gBACxB,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;gBACtC,QAAQ,WAAW,EAAE,CAAC;oBACpB,KAAK,MAAM,CAAC,CAAC,CAAC;wBACZ,MAAM,GAAG,GAAG,KAAgC,CAAC;wBAC7C,MAAM,MAAM,GAA4B,EAAE,CAAC;wBAC3C,KAAK,MAAM,GAAG,IAAI,GAAG;4BAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;wBAChG,OAAO,MAAM,CAAC;oBAChB,CAAC;oBACD,KAAK,KAAK,CAAC,CAAC,CAAC;wBACX,MAAM,GAAG,GAAG,KAAkB,CAAC;wBAC/B,MAAM,MAAM,GAAc,EAAE,CAAC;wBAC7B,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;wBACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;4BAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnE,OAAO,MAAM,CAAC;oBAChB,CAAC;oBACD,KAAK,GAAG,CAAC,CAAC,CAAC;wBACT,MAAM,GAAG,GAAG,KAA8B,CAAC;wBAC3C,MAAM,MAAM,GAAG,IAAI,GAAG,EAAoB,CAAC;wBAC3C,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;4BACzB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;wBAC3D,CAAC,CAAC,CAAC;wBACH,OAAO,MAAM,CAAC;oBAChB,CAAC;oBACD,KAAK,GAAG,CAAC,CAAC,CAAC;wBACT,MAAM,GAAG,GAAG,KAAqB,CAAC;wBAClC,MAAM,MAAM,GAAG,IAAI,GAAG,EAAW,CAAC;wBAClC,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;4BACpB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;wBACrC,CAAC,CAAC,CAAC;wBACH,MAAM;oBACR,CAAC;oBACD,KAAK,qCAAiB,CAAC,CAAC,CAAC;wBACvB,MAAM,GAAG,GAAG,KAA0B,CAAC;wBACvC,MAAM,MAAM,GAAG,IAAI,qCAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;wBACjG,OAAO,MAAM,CAAC;oBAChB,CAAC;gBACH,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;IACH,CAAC;CACF;AApFD,gDAoFC"}