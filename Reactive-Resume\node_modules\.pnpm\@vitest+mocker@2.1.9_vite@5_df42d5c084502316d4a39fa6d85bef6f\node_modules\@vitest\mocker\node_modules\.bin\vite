#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d/node_modules/vite/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d/node_modules/vite/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../../../vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d/node_modules/vite/bin/vite.js" "$@"
fi
