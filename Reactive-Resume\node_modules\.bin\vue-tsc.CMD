@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vue-tsc@2.0.29_typescript@5.7.3\node_modules\vue-tsc\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vue-tsc@2.0.29_typescript@5.7.3\node_modules\vue-tsc\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vue-tsc@2.0.29_typescript@5.7.3\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vue-tsc@2.0.29_typescript@5.7.3\node_modules\vue-tsc\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vue-tsc@2.0.29_typescript@5.7.3\node_modules\vue-tsc\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vue-tsc@2.0.29_typescript@5.7.3\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\vue-tsc@2.0.29_typescript@5.7.3\node_modules\vue-tsc\bin\vue-tsc.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\vue-tsc@2.0.29_typescript@5.7.3\node_modules\vue-tsc\bin\vue-tsc.js" %*
)
