{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/hi.js"], "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_hi=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"hi\",weekdays:\"रविवार_सोमवार_मंगलवार_बुधवार_गुरूवार_शुक्रवार_शनिवार\".split(\"_\"),months:\"जनवरी_फ़रवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितम्बर_अक्टूबर_नवम्बर_दिसम्बर\".split(\"_\"),weekdaysShort:\"रवि_सोम_मंगल_बुध_गुरू_शुक्र_शनि\".split(\"_\"),monthsShort:\"जन._फ़र._मार्च_अप्रै._मई_जून_जुल._अग._सित._अक्टू._नव._दिस.\".split(\"_\"),weekdaysMin:\"र_सो_मं_बु_गु_शु_श\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"A h:mm बजे\",LTS:\"A h:mm:ss बजे\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, A h:mm बजे\",LLLL:\"dddd, D MMMM YYYY, A h:mm बजे\"},relativeTime:{future:\"%s में\",past:\"%s पहले\",s:\"कुछ ही क्षण\",m:\"एक मिनट\",mm:\"%d मिनट\",h:\"एक घंटा\",hh:\"%d घंटे\",d:\"एक दिन\",dd:\"%d दिन\",M:\"एक महीने\",MM:\"%d महीने\",y:\"एक वर्ष\",yy:\"%d वर्ष\"}};return t.default.locale(d,null,!0),d}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,uDAAuD,MAAM,GAAG,GAAE,QAAO,8EAA8E,MAAM,GAAG,GAAE,eAAc,kCAAkC,MAAM,GAAG,GAAE,aAAY,6DAA6D,MAAM,GAAG,GAAE,aAAY,qBAAqB,MAAM,GAAG,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA;AAAA,MAAC,GAAE,SAAQ,EAAC,IAAG,cAAa,KAAI,iBAAgB,GAAE,cAAa,IAAG,eAAc,KAAI,2BAA0B,MAAK,gCAA+B,GAAE,cAAa,EAAC,QAAO,UAAS,MAAK,WAAU,GAAE,eAAc,GAAE,WAAU,IAAG,WAAU,GAAE,WAAU,IAAG,WAAU,GAAE,UAAS,IAAG,UAAS,GAAE,YAAW,IAAG,YAAW,GAAE,WAAU,IAAG,UAAS,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["_"]}