name: docker

on:
  push:
  release:
    types: [published]

jobs:
  build_publish_image:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
      attestations: write
      id-token: write
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Compute Docker tags based on tag/branch
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            browseruse/browseruse
            ghcr.io/browser-use/browser-use
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=pep440,pattern={{version}}
            type=pep440,pattern={{major}}.{{minor}}
            type=sha

      - name: Build and push Docker image
        id: push
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64,linux/arm64
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=registry,ref=browseruse/browseruse:buildcache
          cache-to: type=registry,ref=browseruse/browseruse:buildcache,mode=max
