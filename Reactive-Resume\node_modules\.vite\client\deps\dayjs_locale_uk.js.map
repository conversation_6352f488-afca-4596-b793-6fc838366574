{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/uk.js"], "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_uk=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),s=\"січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня\".split(\"_\"),n=\"січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень\".split(\"_\"),o=/D[oD]?(\\[[^[\\]]*\\]|\\s)+MMMM?/;function d(_,e,t){var s,n;return\"m\"===t?e?\"хвилина\":\"хвилину\":\"h\"===t?e?\"година\":\"годину\":_+\" \"+(s=+_,n={ss:e?\"секунда_секунди_секунд\":\"секунду_секунди_секунд\",mm:e?\"хвилина_хвилини_хвилин\":\"хвилину_хвилини_хвилин\",hh:e?\"година_години_годин\":\"годину_години_годин\",dd:\"день_дні_днів\",MM:\"місяць_місяці_місяців\",yy:\"рік_роки_років\"}[t].split(\"_\"),s%10==1&&s%100!=11?n[0]:s%10>=2&&s%10<=4&&(s%100<10||s%100>=20)?n[1]:n[2])}var i=function(_,e){return o.test(e)?s[_.month()]:n[_.month()]};i.s=n,i.f=s;var r={name:\"uk\",weekdays:\"неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота\".split(\"_\"),weekdaysShort:\"ндл_пнд_втр_срд_чтв_птн_сбт\".split(\"_\"),weekdaysMin:\"нд_пн_вт_ср_чт_пт_сб\".split(\"_\"),months:i,monthsShort:\"січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд\".split(\"_\"),weekStart:1,relativeTime:{future:\"за %s\",past:\"%s тому\",s:\"декілька секунд\",m:d,mm:d,h:d,hh:d,d:\"день\",dd:d,M:\"місяць\",MM:d,y:\"рік\",yy:d},ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY р.\",LLL:\"D MMMM YYYY р., HH:mm\",LLLL:\"dddd, D MMMM YYYY р., HH:mm\"}};return t.default.locale(r,null,!0),r}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,yFAAyF,MAAM,GAAG,GAAE,IAAE,iGAAiG,MAAM,GAAG,GAAE,IAAE;AAA+B,eAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,YAAIC,IAAEC;AAAE,eAAM,QAAMF,KAAED,KAAE,YAAU,YAAU,QAAMC,KAAED,KAAE,WAAS,WAASD,KAAE,OAAKG,KAAE,CAACH,IAAEI,KAAE,EAAC,IAAGH,KAAE,2BAAyB,0BAAyB,IAAGA,KAAE,2BAAyB,0BAAyB,IAAGA,KAAE,wBAAsB,uBAAsB,IAAG,iBAAgB,IAAG,yBAAwB,IAAG,iBAAgB,EAAEC,EAAC,EAAE,MAAM,GAAG,GAAEC,KAAE,MAAI,KAAGA,KAAE,OAAK,KAAGC,GAAE,CAAC,IAAED,KAAE,MAAI,KAAGA,KAAE,MAAI,MAAIA,KAAE,MAAI,MAAIA,KAAE,OAAK,MAAIC,GAAE,CAAC,IAAEA,GAAE,CAAC;AAAA,MAAE;AAAC,UAAI,IAAE,SAASJ,IAAEC,IAAE;AAAC,eAAO,EAAE,KAAKA,EAAC,IAAE,EAAED,GAAE,MAAM,CAAC,IAAE,EAAEA,GAAE,MAAM,CAAC;AAAA,MAAC;AAAE,QAAE,IAAE,GAAE,EAAE,IAAE;AAAE,UAAI,IAAE,EAAC,MAAK,MAAK,UAAS,0DAA0D,MAAM,GAAG,GAAE,eAAc,8BAA8B,MAAM,GAAG,GAAE,aAAY,uBAAuB,MAAM,GAAG,GAAE,QAAO,GAAE,aAAY,yDAAyD,MAAM,GAAG,GAAE,WAAU,GAAE,cAAa,EAAC,QAAO,SAAQ,MAAK,WAAU,GAAE,mBAAkB,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,QAAO,IAAG,GAAE,GAAE,UAAS,IAAG,GAAE,GAAE,OAAM,IAAG,EAAC,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA;AAAA,MAAC,GAAE,SAAQ,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,kBAAiB,KAAI,yBAAwB,MAAK,8BAA6B,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["_", "e", "t", "s", "n"]}