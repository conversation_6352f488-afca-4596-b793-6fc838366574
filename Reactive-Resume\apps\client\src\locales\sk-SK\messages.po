msgid ""
msgstr ""
"POT-Creation-Date: 2023-11-10 13:15+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: sk\n"
"Project-Id-Version: reactive-resume\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-02-03 09:13\n"
"Last-Translator: \n"
"Language-Team: Slovak\n"
"Plural-Forms: nplurals=4; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 3;\n"
"X-Crowdin-Project: reactive-resume\n"
"X-Crowdin-Project-ID: 503410\n"
"X-Crowdin-Language: sk\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 494\n"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:171
msgid "You have enabled two-factor authentication successfully."
msgstr "Úspešne ste povolili dvoj<PERSON><PERSON><PERSON><PERSON><PERSON> overovanie."

#: apps/client/src/pages/home/<USER>/features/index.tsx:57
msgid "{templatesCount} resume templates to choose from"
msgstr "{templatesCount} šablóny životopisov na výber"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:142
msgid "{value, plural, one {Column} other {Columns}}"
msgstr "{value, plural, one {Stĺpec} other {Stĺpce}}"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:20
msgid "<0>I built Reactive Resume mostly by myself during my spare time, with a lot of help from other great open-source contributors.</0><1>If you like the app and want to support keeping it free forever, please donate whatever you can afford to give.</1>"
msgstr "<0>Reactive Resume som vytvoril väčšinou sám vo svojom voľnom čase s veľkou pomocou ďalších skvelých prispievateľov do open-source.</0><1>Ak sa vám aplikácia páči a chcete ju podporiť, aby zostala navždy bezplatná, darujte, čo si môžete dovoliť.</1>"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:51
msgid "<0>I'm sure the app is not perfect, but I'd like for it to be.</0><1>If you faced any issues while creating your resume, or have an idea that would help you and other users in creating your resume more easily, drop an issue on the repository or send me an email about it.</1>"
msgstr "<0>Som si istý, že aplikácia nie je dokonalá, ale chcel by som, aby bola.</0><1>Ak ste sa pri vytváraní životopisu stretli s nejakými problémami alebo máte nápad, ktorý by vám a ostatným používateľom pomohol pri jednoduchšom vytváraní životopisu, napíšte o tom do úložiska alebo mi pošlite e-mail.</1>"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:201
msgid "<0>Note: </0>By utilizing the OpenAI API, you acknowledge and accept the <1>terms of use</1> and <2>privacy policy</2> outlined by OpenAI. Please note that Reactive Resume bears no responsibility for any improper or unauthorized utilization of the service, and any resulting repercussions or liabilities solely rest on the user."
msgstr "<0>Poznámka: </0>Používaním rozhrania API OpenAI beriete na vedomie a akceptujete <1>podmienky používania</1> a <2>zásady ochrany osobných údajov</2> uvedené spoločnosťou OpenAI. Upozorňujeme, že spoločnosť Reactive Resume nenesie žiadnu zodpovednosť za akékoľvek nesprávne alebo neoprávnené používanie služby a akékoľvek následky alebo zodpovednosť za ne nesie výlučne používateľ."

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:85
msgid "<0>The community has spent a lot of time writing the documentation for Reactive Resume, and I'm sure it will help you get started with the app.</0><1>There are also a lot of examples to help you get started, and features that you might not know about which could help you build your perfect resume.</1>"
msgstr "<0>Komunita strávila veľa času písaním dokumentácie k aplikácii Reactive Resume a som si istý, že vám pomôže začať pracovať s aplikáciou.</0><1>Je tu aj veľa príkladov, ktoré vám pomôžu začať, a funkcií, o ktorých možno neviete a ktoré by vám mohli pomôcť vytvoriť dokonalý životopis.</1>"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:140
msgid "<0>Two-factor authentication is currently disabled.</0> You can enable it by adding an authenticator app to your account."
msgstr "<0>Dvojfaktorové overovanie je v súčasnosti vypnuté.</0> Môžete ho povoliť pridaním aplikácie autentifikátora do svojho konta."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:133
msgid "<0>Two-factor authentication is enabled.</0> You will be asked to enter a code every time you sign in."
msgstr "<0>Dvojfaktorové overovanie je povolené.</0> Pri každom prihlásení budete požiadaní o zadanie kódu."

#: apps/client/src/pages/home/<USER>
#: apps/client/src/pages/home/<USER>/hero/index.tsx:40
msgid "A free and open-source resume builder"
msgstr "Bezplatný nástroj na tvorbu životopisov s otvoreným zdrojovým kódom"

#: apps/client/src/pages/home/<USER>/footer.tsx:21
#: apps/client/src/pages/home/<USER>/hero/index.tsx:45
msgid "A free and open-source resume builder that simplifies the process of creating, updating, and sharing your resume."
msgstr "Bezplatný nástroj na tvorbu životopisov s otvoreným zdrojovým kódom, ktorý zjednodušuje proces vytvárania, aktualizácie a zdieľania životopisu."

#: apps/client/src/pages/builder/_components/toolbar.tsx:59
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:29
msgid "A link has been copied to your clipboard."
msgstr "Odkaz bol skopírovaný do schránky."

#: apps/client/src/components/copyright.tsx:29
msgid "A passion project by <0>Amruth Pillai</0>"
msgstr "Vášnivý projekt <0>Amruth Pillai</0>"

#: apps/client/src/pages/auth/forgot-password/page.tsx:57
msgid "A password reset link should have been sent to your inbox, if an account existed with the email you provided."
msgstr "Do vašej e-mailovej schránky by malo byť odoslané prepojenie na obnovenie hesla, ak existuje konto s e-mailom, ktorý ste zadali."

#: apps/client/src/services/errors/translate-error.ts:43
msgid "A resume with this slug already exists, please pick a different unique identifier."
msgstr "Životopis s týmto príponou už existuje, vyberte iný jedinečný identifikátor."

#: apps/client/src/services/errors/translate-error.ts:10
msgid "A user with this email address and/or username already exists."
msgstr "Používateľ s touto e-mailovou adresou a/alebo používateľským menom už existuje."

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:43
msgid "A4"
msgstr "A4"

#. Helper text to let the user know what filetypes are accepted. {accept} can be .pdf or .json.
#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:270
msgid "Accepts only {accept} files"
msgstr "Prijíma iba súbory {accept}"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:105
msgid "Account"
msgstr "Účet"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:175
msgid "Add a custom field"
msgstr "Pridanie vlastného poľa"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-base.tsx:119
#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-base.tsx:170
msgctxt "For example, add a new work experience, or add a new profile."
msgid "Add a new item"
msgstr "Pridanie novej položky"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:91
msgid "Add a new item"
msgstr "Pridanie novej položky"

#: apps/client/src/pages/builder/sidebars/left/index.tsx:146
#: apps/client/src/pages/builder/sidebars/left/index.tsx:263
msgid "Add a new section"
msgstr "Pridanie novej sekcie"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:261
msgid "Add New Page"
msgstr "Pridať novú stránku"

#: apps/client/src/components/ai-actions.tsx:79
msgid "AI"
msgstr "AI"

#: apps/client/src/pages/auth/register/page.tsx:71
msgid "Already have an account?"
msgstr "Už máte účet?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:144
msgid "An error occurred while validating the file."
msgstr "Pri overovaní súboru došlo k chybe."

#: apps/client/src/pages/public/error.tsx:23
msgid "An internal server error occurred."
msgstr ""

#: apps/client/src/pages/public/error.tsx:32
msgid "An unexpected error occurred."
msgstr ""

#: apps/client/src/pages/home/<USER>/features/index.tsx:134
msgid "and many more..."
msgstr "a mnoho ďalších..."

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:57
msgid "Anyone with the link can view and download the resume."
msgstr "Každý, kto má k dispozícii toto prepojenie, si môže životopis pozrieť a stiahnuť."

#: apps/client/src/pages/builder/_components/toolbar.tsx:60
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:30
msgid "Anyone with this link can view and download the resume. Share it on your profile or with recruiters."
msgstr "Životopis si môže pozrieť a stiahnuť každý, kto má k dispozícii toto prepojenie. Zdieľajte ho na svojom profile alebo s náborovými pracovníkmi."

#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:41
msgid "Apply Custom CSS"
msgstr "Použitie vlastného CSS"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:128
msgid "Are you sure you want to delete this item?"
msgstr "Ste si istí, že chcete túto položku odstrániť?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:149
msgid "Are you sure you want to delete your resume?"
msgstr "Ste si istí, že chcete vymazať svoj životopis?"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:125
msgid "Are you sure you want to disable two-factor authentication?"
msgstr "Ste si istí, že chcete vypnúť dvojfaktorové overovanie?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:38
msgid "Are you sure you want to lock this resume?"
msgstr "Ste si istí, že chcete tento životopis uzamknúť?"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:39
msgid "Are you sure you want to unlock this resume?"
msgstr "Ste si istí, že chcete odomknúť tento životopis?"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:94
msgid "Are you sure?"
msgstr "Ste si istý?"

#. For example, Computer Science or Business Administration
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:73
msgid "Area of Study"
msgstr "Oblasť štúdia"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:79
msgid "Aspect Ratio"
msgstr "Pomer strán"

#: apps/client/src/pages/home/<USER>/features/index.tsx:51
msgid "Available in {languagesCount} languages"
msgstr "K dispozícii v {languagesCount} jazykoch"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:53
msgid "Awarder"
msgstr "Udeľovateľ"

#: apps/client/src/pages/auth/backup-otp/page.tsx:99
#: apps/client/src/pages/auth/forgot-password/page.tsx:100
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:243
msgid "Back"
msgstr "Späť"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:73
msgid "Background Color"
msgstr "Farba pozadia"

#: apps/client/src/pages/auth/backup-otp/page.tsx:75
msgid "Backup Code"
msgstr "Záložný kód"

#: apps/client/src/pages/auth/backup-otp/page.tsx:81
msgid "Backup Codes may contain only lowercase letters or numbers, and must be exactly 10 characters."
msgstr "Záložné kódy môžu obsahovať len malé písmená alebo číslice a musia mať presne 10 znakov."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:132
msgid "Base URL"
msgstr "Základná adresa URL"

#: apps/client/src/pages/builder/sidebars/left/index.tsx:55
msgctxt "The basics section of a resume consists of User's Picture, Full Name, Location etc."
msgid "Basics"
msgstr "Základy"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:21
msgid "Basics"
msgstr "Základy"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:191
msgid "Border"
msgstr "Hranica"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:124
msgid "Border Radius"
msgstr "Polomer hranice"

#: apps/client/src/pages/public/page.tsx:93
msgid "Built with"
msgstr "Postavené s"

#: apps/client/src/components/copyright.tsx:27
#: apps/client/src/pages/home/<USER>/contributors/index.tsx:20
msgid "By the community, for the community."
msgstr "Od komunity, pre komunitu."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:135
#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:49
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:156
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:137
msgid "Cancel"
msgstr "Zrušiť"

#: apps/client/src/components/ai-actions.tsx:103
#: apps/client/src/components/ai-actions.tsx:106
msgid "Casual"
msgstr "Príležitostné"

#: apps/client/src/pages/builder/_components/toolbar.tsx:130
msgid "Center Artboard"
msgstr "Stredová umelecká doska"

#: apps/client/src/pages/auth/reset-password/page.tsx:99
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:115
msgid "Change Password"
msgstr "Zmena hesla"

#: apps/client/src/components/ai-actions.tsx:97
msgid "Change Tone"
msgstr "Zmena tónu"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:186
msgid "Changed your mind about the name? Give it a new one."
msgstr "Zmenili ste názor na názov? Dajte mu nový."

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:70
msgid "Check your email for the confirmation link to update your email address."
msgstr "Skontrolujte, či sa vám v e-mailovej správe nenachádza potvrdzujúce prepojenie na aktualizáciu vašej e-mailovej adresy."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:144
msgid "Circle"
msgstr "Kruh"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:249
msgid "Close"
msgstr "Zatvoriť"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:201
msgid "Code"
msgstr "Kód"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:52
msgid "Code must be exactly 6 digits long."
msgstr "Kód musí mať presne 6 číslic."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:136
msgid "Columns"
msgstr "Stĺpce"

#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:39
msgid "Company"
msgstr "Spoločnosť"

#: apps/client/src/components/ai-actions.tsx:115
#: apps/client/src/components/ai-actions.tsx:118
msgid "Confident"
msgstr "Sebavedomé"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:234
#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:246
msgid "Continue"
msgstr "Pokračovať"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:96
msgid "Copy"
msgstr "Kopírovať"

#: apps/client/src/pages/builder/_components/toolbar.tsx:164
msgid "Copy Link to Resume"
msgstr "Kopírovať prepojenie na životopis"

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:78
msgid "Copy to Clipboard"
msgstr "Kopírovanie do schránky"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:179
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:244
msgid "Create"
msgstr "Vytvoriť stránku"

#: apps/client/src/pages/auth/register/page.tsx:64
#: apps/client/src/pages/auth/register/page.tsx:69
msgid "Create a new account"
msgstr "Vytvorenie nového účtu"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:163
msgid "Create a new item"
msgstr "Vytvorenie novej položky"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:178
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/create-card.tsx:28
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/create-item.tsx:18
msgid "Create a new resume"
msgstr "Vytvorenie nového životopisu"

#: apps/client/src/pages/auth/login/page.tsx:65
msgctxt "This is a link to create a new account"
msgid "Create one now"
msgstr "Vytvorte si ho teraz"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:259
msgid "Create Sample Resume"
msgstr "Vytvorenie vzorového životopisu"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:82
msgid "Current Password"
msgstr ""

#: apps/client/src/pages/builder/sidebars/right/index.tsx:93
#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:27
#: apps/client/src/pages/builder/sidebars/right/sections/css.tsx:28
msgid "Custom CSS"
msgstr "Vlastné CSS"

#: apps/client/src/pages/home/<USER>/features/index.tsx:62
msgid "Custom resume sections"
msgstr "Vlastné časti životopisu"

#: apps/client/src/stores/resume.ts:47
msgid "Custom Section"
msgstr "Vlastná sekcia"

#: apps/client/src/pages/home/<USER>/features/index.tsx:60
msgid "Customisable colour palettes"
msgstr "Prispôsobiteľné palety farieb"

#: apps/client/src/pages/home/<USER>/features/index.tsx:61
msgid "Customisable layouts"
msgstr "Prispôsobiteľné rozloženia"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:62
msgid "Danger Zone"
msgstr "Nebezpečná zóna"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:87
msgid "Dark"
msgstr "Dark"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:67
msgid "Date"
msgstr "Dátum"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:110
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:72
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:67
msgid "Date or Date Range"
msgstr "Dátum alebo rozsah dátumov"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:137
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:158
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:121
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:127
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:172
msgid "Delete"
msgstr "Odstrániť"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:79
#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:94
msgid "Delete Account"
msgstr "Odstrániť účet"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:73
#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:50
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:53
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:63
msgid "Description"
msgstr "Popis"

#: apps/client/src/pages/home/<USER>/features/index.tsx:58
msgid "Design single/multi page resumes"
msgstr "Navrhovanie jednostranných/ viacstranových životopisov"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:139
msgid "Disable"
msgstr "Zakázať"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:154
msgid "Disable 2FA"
msgstr "Zakázanie funkcie 2FA"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:302
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:220
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:134
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:118
msgid "Discard"
msgstr "Vyradiť"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:105
msgid "Documentation"
msgstr "Dokumentácia"

#: apps/client/src/pages/auth/login/page.tsx:62
msgid "Don't have an account?"
msgstr "Nemáte účet?"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:83
msgid "Don't know where to begin? Hit the docs!"
msgstr "Neviete, kde začať? Prejdite na dokumenty!"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:107
msgid "Don't see your language? <0>Help translate the app.</0>"
msgstr "Nevidíte svoj jazyk? <0>Pomôžte preložiť aplikáciu.</0>"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:40
msgid "Donate to Reactive Resume"
msgstr "Darujte Reaktívny životopis"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:56
msgid "Download a JSON snapshot of your resume. This file can be used to import your resume in the future, or can even be shared with others to collaborate."
msgstr "Stiahnite si snímku JSON svojho životopisu. Tento súbor môžete použiť na importovanie svojho životopisu v budúcnosti alebo ho môžete dokonca zdieľať s ostatnými na spoluprácu."

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:74
msgid "Download a PDF of your resume. This file can be used to print your resume, send it to recruiters, or upload on job portals."
msgstr "Stiahnite si svoj životopis vo formáte PDF. Tento súbor môžete použiť na vytlačenie svojho životopisu, poslať ho náborovým pracovníkom alebo nahrať na pracovné portály."

#: apps/client/src/pages/builder/_components/toolbar.tsx:176
msgid "Download PDF"
msgstr "Stiahnuť PDF"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:58
msgid "Downloads"
msgstr "Na stiahnutie"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:181
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:246
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:105
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:95
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:156
msgid "Duplicate"
msgstr "Duplikát"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:165
msgid "Duplicate an existing item"
msgstr "Duplikovať existujúcu položku"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:180
msgid "Duplicate an existing resume"
msgstr "Duplikovať existujúci životopis"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:92
msgid "Edit"
msgstr "Upraviť"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:169
msgid "Effects"
msgstr "Účinky"

#: apps/client/src/pages/auth/forgot-password/page.tsx:82
#: apps/client/src/pages/auth/login/page.tsx:90
#: apps/client/src/pages/auth/register/page.tsx:141
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:54
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:181
msgid "Email"
msgstr "E-mail"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:163
msgid "Enable 2FA"
msgstr "Povolenie 2FA"

#: apps/client/src/pages/auth/reset-password/page.tsx:67
msgid "Enter a new password below, and make sure it's secure."
msgstr "Nižšie zadajte nové heslo a uistite sa, že je bezpečné."

#: apps/client/src/pages/auth/backup-otp/page.tsx:59
msgid "Enter one of the 10 backup codes you saved when you enabled two-factor authentication."
msgstr "Zadajte jeden z 10 záložných kódov, ktoré ste uložili pri zapnutí dvojfaktorového overovania."

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:63
msgid "Enter Phosphor Icon"
msgstr "Zadajte ikonu fosforu"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:170
msgid "Enter the 6-digit code from your authenticator app to verify that 2FA has been setup correctly."
msgstr "Zadajte 6-miestny kód z aplikácie autentifikátora a overte, či bolo 2FA nastavené správne."

#: apps/client/src/pages/auth/verify-otp/page.tsx:60
msgid "Enter the one-time password provided by your authenticator app below."
msgstr "Nižšie zadajte jednorazové heslo, ktoré vám poskytla aplikácia autentifikátora."

#: apps/client/src/pages/auth/forgot-password/page.tsx:70
msgid "Enter your email address and we will send you a link to reset your password if the account exists."
msgstr "Zadajte svoju e-mailovú adresu a my vám pošleme odkaz na obnovenie hesla, ak konto existuje."

#: apps/client/src/pages/public/error.tsx:46
msgid "Error {statusCode}"
msgstr ""

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:283
msgid "Errors"
msgstr "Chyby"

#: apps/client/src/pages/home/<USER>/support/index.tsx:78
msgid "Even if you're not in a position to contribute financially, you can still make a difference by giving the GitHub repository a star, spreading the word to your friends, or dropping a quick message to let me know how Reactive Resume has helped you. Your feedback and support are always welcome and much appreciated!"
msgstr "Aj keď nie ste v pozícii, aby ste mohli finančne prispieť, stále môžete prispieť tým, že dáte úložisku GitHub hviezdičku, rozšírite túto informáciu medzi svojich priateľov alebo mi napíšete krátku správu, aby som vedel, ako vám Reactive Resume pomohol. Vaša spätná väzba a podpora sú vždy vítané a veľmi si ich cením!"

#: apps/client/src/pages/home/<USER>/templates/index.tsx:12
msgid "Explore the templates available in Reactive Resume and view the resumes crafted with them. They could also serve as examples to help guide the creation of your next resume."
msgstr "Preskúmajte šablóny dostupné v Reactive Resume a prezrite si životopisy, ktoré boli pomocou nich vytvorené. Môžu vám poslúžiť aj ako príklady, ktoré vám pomôžu pri tvorbe vášho ďalšieho životopisu."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:121
#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:39
#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:40
msgid "Export"
msgstr "Export"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:255
msgid "File"
msgstr "Súbor"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:221
msgid "Filetype"
msgstr "Typ súboru"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:38
msgid "Finally,"
msgstr "Nakoniec,"

#: apps/client/src/components/ai-actions.tsx:90
msgid "Fix Spelling & Grammar"
msgstr "Oprava pravopisu a gramatiky"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:107
msgid "Font Family"
msgstr "Rodina písiem"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:148
msgid "Font Size"
msgstr "Veľkosť písma"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:122
msgid "Font Subset"
msgstr "Podskupina písiem"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:134
msgid "Font Variants"
msgstr "Varianty písma"

#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:35
msgid "For example, information regarding which companies you sent this resume to or the links to the job descriptions can be noted down here."
msgstr "Tu si môžete zapísať napríklad informácie o tom, ktorým spoločnostiam ste poslali tento životopis, alebo odkazy na opisy pracovných miest."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:182
msgid "Forget"
msgstr "Zabudnite na"

#: apps/client/src/pages/auth/login/page.tsx:131
msgid "Forgot Password?"
msgstr "Zabudli ste heslo?"

#: apps/client/src/pages/auth/forgot-password/page.tsx:68
msgid "Forgot your password?"
msgstr "Zabudli ste heslo?"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:32
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:40
msgid "Format"
msgstr "Formát"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:49
msgid "Found a bug, or have an idea for a new feature?"
msgstr "Našli ste chybu alebo máte nápad na novú funkciu?"

#: apps/client/src/pages/home/<USER>/features/index.tsx:46
msgid "Free, forever"
msgstr "Zadarmo, navždy"

#: apps/client/src/components/ai-actions.tsx:121
#: apps/client/src/components/ai-actions.tsx:124
msgid "Friendly"
msgstr "Friendly"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:31
msgid "Full Name"
msgstr "Celé meno"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:202
msgid "Generate a random title for your resume"
msgstr "Vygenerujte náhodný názov pre svoj životopis"

#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:32
msgid "Get Started"
msgstr "Začnite"

#: apps/client/src/pages/auth/_components/social-auth.tsx:18
msgid "GitHub"
msgstr "GitHub"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:12
msgid "GitHub Stars"
msgstr "Hviezdy GitHub"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:187
msgid "Give your old resume a new name."
msgstr "Dajte svojmu starému životopisu nové meno."

#: apps/client/src/pages/auth/verify-email/page.tsx:67
#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:18
msgid "Go to Dashboard"
msgstr "Prejsť na prístrojový panel"

#: apps/client/src/pages/public/error.tsx:55
msgid "Go to home"
msgstr ""

#: apps/client/src/pages/auth/_components/social-auth.tsx:31
msgid "Google"
msgstr "Google"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:202
msgid "Grayscale"
msgstr "Odtiene sivej"

#: apps/client/src/pages/dashboard/resumes/page.tsx:43
msgid "Grid"
msgstr "Sieť"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:43
msgid "Headline"
msgstr "Titulok"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:107
msgid "Here, you can update your account information such as your profile picture, name and username."
msgstr "Tu môžete aktualizovať informácie o svojom konte, napríklad profilový obrázok, meno a používateľské meno."

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:68
msgid "Here, you can update your profile to customize and personalize your experience."
msgstr "Tu môžete aktualizovať svoj profil a prispôsobiť si ho."

#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:80
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:94
#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:180
msgid "Hidden"
msgstr "Skryté"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:106
msgid "Hide"
msgstr "Skryť"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:192
msgid "Hide Icons"
msgstr "Skryť ikony"

#: apps/client/src/pages/auth/login/page.tsx:115
#: apps/client/src/pages/auth/register/page.tsx:168
#: apps/client/src/pages/auth/reset-password/page.tsx:88
msgid "Hold <0>Ctrl</0> to display your password temporarily."
msgstr "Podržaním <0>klávesu Ctrl</0> dočasne zobrazíte heslo."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:93
msgid "Horizontal"
msgstr "Horizontálne"

#: apps/client/src/pages/home/<USER>/features/index.tsx:67
msgid "Host your resume publicly"
msgstr "Zverejnite svoj životopis"

#: apps/client/src/pages/home/<USER>/testimonials/index.tsx:70
msgid "I always love to hear from the users of Reactive Resume with feedback or support. Here are some of the messages I've received. If you have any feedback, feel free to drop me an email at <0>{email}</0>."
msgstr "Vždy sa rád dozviem spätnú väzbu alebo podporu od používateľov aplikácie Reactive Resume. Tu sú niektoré zo správ, ktoré som dostal. Ak máte nejakú spätnú väzbu, neváhajte mi napísať na e-mailovú adresu <0>{email}</0>."

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:83
#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:53
msgid "Icon"
msgstr "Ikona"

#: apps/client/src/pages/home/<USER>/logo-cloud/index.tsx:47
msgid "If this app has helped you with your job hunt, let me know by reaching out through <0>this contact form</0>."
msgstr "Ak vám táto aplikácia pomohla pri hľadaní práce, dajte mi vedieť prostredníctvom <0>tohto kontaktného formulára</0>."

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:128
msgid "If you disable two-factor authentication, you will no longer be required to enter a verification code when logging in."
msgstr "Ak vypnete dvojfaktorové overovanie, pri prihlasovaní sa už nebude vyžadovať zadanie overovacieho kódu."

#: apps/client/src/pages/home/<USER>/support/index.tsx:59
msgid "If you're multilingual, we'd love your help in bringing the app to more languages and communities. Don't worry if you don't see your language on the list - just give me a shout-out on GitHub, and I'll make sure to include it. Ready to get started? Jump into translation over at Crowdin by clicking the link below."
msgstr "Ak hovoríte viacerými jazykmi, budeme radi, ak nám pomôžete rozšíriť aplikáciu do viacerých jazykov a komunít. Ak svoj jazyk na zozname nevidíte, nebojte sa - stačí, ak mi dáte vedieť na GitHube, a ja sa postarám o jeho zaradenie. Ste pripravení začať? Vstúpte do prekladu na stránke Crowdin kliknutím na odkaz nižšie."

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:309
msgid "Import"
msgstr "Import"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:208
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/import-card.tsx:28
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/import-item.tsx:17
msgid "Import an existing resume"
msgstr "Importovanie existujúceho životopisu"

#: apps/client/src/components/ai-actions.tsx:85
msgid "Improve Writing"
msgstr "Zlepšiť písanie"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:188
msgid "In case you are unable to scan this QR Code, you can also copy-paste this link into your authenticator app."
msgstr "Ak sa vám nepodarí naskenovať tento kód QR, môžete toto prepojenie skopírovať a vložiť do aplikácie autentifikátora."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:67
msgid "In this section, you can change your password and enable/disable two-factor authentication."
msgstr "V tejto časti môžete zmeniť heslo a povoliť/povoliť dvojfaktorové overovanie."

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:64
msgid "In this section, you can delete your account and all the data associated to your user, but please keep in mind that <0>this action is irreversible</0>."
msgstr "V tejto časti môžete vymazať svoje konto a všetky údaje priradené k používateľovi, ale nezabudnite, že <0>táto akcia je nezvratná</0>."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:135
#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:116
#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:117
msgid "Information"
msgstr "Informácie"

#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:39
msgid "Institution"
msgstr "Inštitúcia"

#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:53
msgid "Issuer"
msgstr "Emitent"

#: apps/client/src/services/errors/translate-error.ts:7
msgid "It doesn't look like a user exists with the credentials you provided."
msgstr "Nevyzerá to, že existuje používateľ s prihlasovacími údajmi, ktoré ste zadali."

#: apps/client/src/services/errors/translate-error.ts:37
msgid "It looks like the backup code you provided is invalid or used. Please try again."
msgstr "Vyzerá to, že záložný kód, ktorý ste poskytli, je neplatný alebo použitý. Skúste to prosím znova."

#: apps/client/src/services/errors/translate-error.ts:19
msgid "It looks like the reset token you provided is invalid. Please try restarting the password reset process again."
msgstr "Vyzerá to, že vami zadaný resetovací token je neplatný. Prosím, skúste znovu spustiť proces obnovenia hesla."

#: apps/client/src/services/errors/translate-error.ts:46
msgid "It looks like the resume you're looking for doesn't exist."
msgstr "Vyzerá to tak, že životopis, ktorý hľadáte, neexistuje."

#: apps/client/src/services/errors/translate-error.ts:34
msgid "It looks like the two-factor authentication code you provided is invalid. Please try again."
msgstr "Vyzerá to tak, že vami zadaný kód dvojfaktorového overovania je neplatný. Skúste to prosím znova."

#: apps/client/src/services/errors/translate-error.ts:22
msgid "It looks like the verification token you provided is invalid. Please try restarting the verification process again."
msgstr "Vyzerá to, že vami zadaný overovací token je neplatný. Skúste prosím znovu spustiť proces overovania."

#: apps/client/src/services/errors/translate-error.ts:25
msgid "It looks like your email address has already been verified."
msgstr "Vyzerá to, že vaša e-mailová adresa už bola overená."

#: apps/client/src/pages/auth/register/page.tsx:101
msgctxt "Localized version of a placeholder name. For example, Max Mustermann in German or Jan Kowalski in Polish."
msgid "John Doe"
msgstr "John Doe"

#: apps/client/src/pages/auth/register/page.tsx:123
msgctxt "Localized version of a placeholder username. For example, max.mustermann in German or jan.kowalski in Polish."
msgid "john.doe"
msgstr "john.doe"

#: apps/client/src/pages/auth/register/page.tsx:145
msgctxt "Localized version of a placeholder email. For example, <EMAIL> in <NAME_EMAIL> in Polish."
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:54
msgid "JSON"
msgstr "JSON"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:159
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:63
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:159
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:109
msgid "Keywords"
msgstr "Kľúčové slová"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:42
#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:52
msgid "Label"
msgstr "Štítok"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:101
msgid "Language"
msgstr "Jazyk"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:83
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:139
msgid "Last updated {lastUpdated}"
msgstr "Posledná aktualizácia {lastUpdated}"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:72
#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:197
#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:198
msgid "Layout"
msgstr "Rozloženie"

#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:38
msgid "Learn more"
msgstr "Zistite viac"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:44
msgid "Letter"
msgstr "List"

#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:64
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:77
msgid "Level"
msgstr "Úroveň"

#: apps/client/src/components/copyright.tsx:16
msgid "Licensed under <0>MIT</0>"
msgstr "Licencia <0>MIT</0>"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:86
msgid "Light"
msgstr "Svetlo"

#: apps/client/src/pages/home/<USER>/features/index.tsx:69
msgid "Light or dark theme"
msgstr "Svetlá alebo tmavá téma"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:165
msgid "Line Height"
msgstr "Výška riadku"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/import-card.tsx:33
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/import-item.tsx:22
msgid "LinkedIn, JSON Resume, etc."
msgstr "LinkedIn, JSON Resume atď."

#: apps/client/src/pages/dashboard/resumes/page.tsx:47
msgid "List"
msgstr "Zoznam"

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:86
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:81
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:93
msgid "Location"
msgstr "Umiestnenie"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:51
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:115
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:115
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:166
msgid "Lock"
msgstr "Zámok"

#: apps/client/src/pages/home/<USER>/features/index.tsx:64
msgid "Lock a resume to prevent editing"
msgstr "Uzamknutie životopisu, aby sa zabránilo jeho úpravám"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:43
msgid "Locking a resume will prevent any further changes to it. This is useful when you have already shared your resume with someone and you don't want to accidentally make any changes to it."
msgstr "Uzamknutie životopisu zabráni jeho ďalším zmenám. Je to užitočné, ak ste už svoj životopis s niekým zdieľali a nechcete v ňom náhodne vykonať žiadne zmeny."

#: apps/client/src/components/user-options.tsx:38
#: apps/client/src/pages/home/<USER>/hero/call-to-action.tsx:23
msgid "Logout"
msgstr "Odhlásenie"

#: apps/client/src/pages/auth/verify-otp/page.tsx:64
msgid "Lost your device?"
msgstr "Stratili ste zariadenie?"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:247
msgid "Main"
msgstr "Hlavná stránka"

#: apps/client/src/pages/home/<USER>/features/index.tsx:59
msgid "Manage multiple resumes"
msgstr "Správa viacerých životopisov"

#. The month and year should be uniform across all languages.
#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:71
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:69
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:69
msgid "March 2023"
msgstr "marec 2023"

#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:112
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:74
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:103
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:69
msgid "March 2023 - Present"
msgstr "marec 2023 - súčasnosť"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:50
msgid "Margin"
msgstr "Marža"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:158
msgid "Max Tokens"
msgstr "Maximálny počet žetónov"

#: apps/client/src/pages/home/<USER>/features/index.tsx:48
msgid "MIT License"
msgstr "Licencia MIT"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:145
msgid "Model"
msgstr "Model"

#: apps/client/src/pages/auth/register/page.tsx:98
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:59
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:48
#: apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx:36
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:73
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:39
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:39
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:49
#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:88
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:153
msgid "Name"
msgstr "Názov"

#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:39
msgctxt "Name of the Certification"
msgid "Name"
msgstr "Názov"

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:40
msgid "Network"
msgstr "Sieť"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:96
msgid "New Password"
msgstr "Nové heslo"

#: apps/client/src/components/locale-combobox.tsx:45
msgid "No results found"
msgstr "Nenašli sa žiadne výsledky"

#: apps/client/src/pages/home/<USER>/features/index.tsx:49
msgid "No user tracking or advertising"
msgstr "Žiadne sledovanie používateľov ani reklama"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:133
msgid "Note: This will make your account less secure."
msgstr "Poznámka: Tým sa vaše konto stane menej bezpečným."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:128
#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:16
#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:17
msgid "Notes"
msgstr "Poznámky"

#: apps/client/src/pages/auth/verify-otp/page.tsx:82
msgid "One-Time Password"
msgstr "Jednorazové heslo"

#: apps/client/src/components/ai-actions.tsx:56
#: apps/client/src/libs/axios.ts:30
#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:188
#: apps/client/src/services/resume/print.tsx:26
msgid "Oops, the server returned an error."
msgstr "Ups, server vrátil chybu."

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:97
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:77
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:148
msgid "Open"
msgstr "Otvorte stránku"

#: apps/client/src/pages/home/<USER>/features/index.tsx:47
msgid "Open Source"
msgstr "Otvorený zdrojový kód"

#: apps/client/src/services/openai/change-tone.ts:35
#: apps/client/src/services/openai/fix-grammar.ts:33
#: apps/client/src/services/openai/improve-writing.ts:33
msgid "OpenAI did not return any choices for your text."
msgstr "Aplikácia OpenAI nevrátila žiadne možnosti pre váš text."

#: apps/client/src/pages/home/<USER>/features/index.tsx:52
msgid "OpenAI Integration"
msgstr "Integrácia OpenAI"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:119
msgid "OpenAI/Ollama API Key"
msgstr "Kľúč API OpenAI/Ollama"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:79
msgid "OpenAI/Ollama Integration"
msgstr "Integrácia OpenAI/Ollama"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:67
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:182
msgid "Options"
msgstr "Možnosti"

#: apps/client/src/pages/auth/layout.tsx:47
msgctxt "The user can either login with email/password, or continue with GitHub or Google."
msgid "or continue with"
msgstr "alebo pokračovať s"

#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:39
msgid "Organization"
msgstr "Organizácia"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:100
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:25
#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:26
msgid "Page"
msgstr "Strana"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:228
msgid "Page {pageNumber}"
msgstr "Strana {pageNumber}"

#: apps/client/src/pages/auth/login/page.tsx:110
#: apps/client/src/pages/auth/register/page.tsx:163
#: apps/client/src/pages/auth/reset-password/page.tsx:83
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:73
msgid "Password"
msgstr "Heslo"

#: apps/client/src/pages/builder/sidebars/right/sections/export.tsx:72
msgid "PDF"
msgstr "PDF"

#: apps/client/src/pages/home/<USER>/features/index.tsx:63
msgid "Personal notes for each resume"
msgstr "Osobné poznámky ku každému životopisu"

#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:81
msgid "Phone"
msgstr "Telefón"

#: apps/client/src/pages/auth/layout.tsx:76
msgid "Photograph by Patrick Tomasso"
msgstr "Fotografia: Patrick Tomasso"

#: apps/client/src/pages/home/<USER>/features/index.tsx:66
msgid "Pick any font from Google Fonts"
msgstr "Vyberte si ľubovoľné písmo zo služby Google Fonts"

#: apps/client/src/pages/builder/sidebars/left/sections/picture/section.tsx:69
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:121
msgid "Picture"
msgstr "Obrázok"

#: apps/client/src/pages/auth/verify-email/page.tsx:59
msgid "Please note that this step is completely optional."
msgstr "Upozorňujeme, že tento krok je úplne voliteľný."

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:225
msgid "Please select a file type"
msgstr "Vyberte typ súboru"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:228
msgid "Please store your backup codes in a secure location. You can use one of these one-time use codes to login in case you lose access to your authenticator app."
msgstr "Záložné kódy uložte na bezpečnom mieste. Jeden z týchto jednorazových kódov môžete použiť na prihlásenie v prípade straty prístupu k aplikácii autentifikátora."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:99
msgid "Portrait"
msgstr "Portrét"

#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:54
msgctxt "Position held at a company, for example, Software Engineer"
msgid "Position"
msgstr "Pozícia"

#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:53
msgid "Position"
msgstr "Pozícia"

#: apps/client/src/pages/home/<USER>/features/index.tsx:96
msgid "Powered by"
msgstr "Powered by"

#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:94
msgid "Powered by <0>Simple Icons</0>"
msgstr "Poháňané <0>jednoduchými ikonami</0>"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:43
msgid "Primary Color"
msgstr "Primárna farba"

#: apps/client/src/pages/home/<USER>/footer.tsx:50
msgid "Privacy Policy"
msgstr "Zásady ochrany osobných údajov"

#: apps/client/src/components/ai-actions.tsx:109
#: apps/client/src/components/ai-actions.tsx:112
msgid "Professional"
msgstr "Profesionálne"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:66
msgid "Profile"
msgstr "Profil"

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:55
msgid "Public"
msgstr "Verejnosť"

#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:53
msgid "Publisher"
msgstr "Vydavateľ"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:69
msgid "Raise an issue"
msgstr "Upozorniť na problém"

#: apps/client/src/components/copyright.tsx:35
#: apps/client/src/pages/auth/backup-otp/page.tsx:52
#: apps/client/src/pages/auth/forgot-password/page.tsx:49
#: apps/client/src/pages/auth/login/page.tsx:55
#: apps/client/src/pages/auth/register/page.tsx:64
#: apps/client/src/pages/auth/reset-password/page.tsx:60
#: apps/client/src/pages/auth/verify-email/page.tsx:43
#: apps/client/src/pages/auth/verify-otp/page.tsx:52
#: apps/client/src/pages/builder/page.tsx:60
#: apps/client/src/pages/dashboard/resumes/page.tsx:20
#: apps/client/src/pages/dashboard/settings/page.tsx:16
#: apps/client/src/pages/home/<USER>/footer.tsx:18
#: apps/client/src/pages/home/<USER>
#: apps/client/src/pages/public/page.tsx:74
#: apps/client/src/pages/public/page.tsx:95
msgid "Reactive Resume"
msgstr "Reaktívny životopis"

#: apps/client/src/pages/home/<USER>/logo-cloud/index.tsx:39
msgid "Reactive Resume has helped people land jobs at these great companies:"
msgstr "Reactive Resume pomohol ľuďom získať prácu v týchto skvelých spoločnostiach:"

#: apps/client/src/pages/home/<USER>/support/index.tsx:12
msgid "Reactive Resume is a free and open-source project crafted mostly by me, and your support would be greatly appreciated. If you're inclined to contribute, and only if you can afford to, consider making a donation through any of the listed platforms. Additionally, donations to Reactive Resume through Open Collective are tax-exempt, as the project is fiscally hosted by Open Collective Europe."
msgstr "Reactive Resume je bezplatný projekt s otvoreným zdrojovým kódom, ktorý som vytvoril najmä ja, a veľmi si cením vašu podporu. Ak máte chuť prispieť, a to len vtedy, ak si to môžete dovoliť, zvážte možnosť prispieť prostredníctvom niektorej z uvedených platforiem. Okrem toho sú dary pre Reactive Resume prostredníctvom Open Collective oslobodené od daní, keďže projekt fiškálne zastrešuje Open Collective Europe."

#: apps/client/src/pages/home/<USER>/features/index.tsx:107
msgid "Reactive Resume is a passion project of over 3 years of hard work, and with that comes a number of re-iterated ideas and features that have been built to (near) perfection."
msgstr "Reactive Resume je vášnivý projekt, na ktorom sa usilovne pracovalo viac ako 3 roky, a s tým súvisí aj množstvo opakovaných nápadov a funkcií, ktoré boli vybudované (takmer) dokonale."

#: apps/client/src/pages/home/<USER>/contributors/index.tsx:22
msgid "Reactive Resume thrives thanks to its vibrant community. This project owes its progress to numerous individuals who've dedicated their time and skills. Below, we celebrate the coders who've enhanced its features on GitHub and the linguists whose translations on Crowdin have made it accessible to a broader audience."
msgstr "Reactive Resume sa darí vďaka živej komunite. Tento projekt vďačí za svoj pokrok mnohým jednotlivcom, ktorí mu venovali svoj čas a zručnosti. Nižšie oslavujeme programátorov, ktorí vylepšili jeho funkcie na GitHube, a lingvistov, ktorých preklady na Crowdin ho sprístupnili širšiemu publiku."

#: apps/client/src/pages/builder/_components/toolbar.tsx:89
msgid "Redo"
msgstr "Redo"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:100
#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:157
msgid "Remove"
msgstr "Odstránenie stránky"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:231
msgid "Remove Page"
msgstr "Odstrániť stránku"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:111
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:101
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:86
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:152
msgid "Rename"
msgstr "Premenovať stránku"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:199
msgid "Resend email confirmation link"
msgstr "Opätovné odoslanie prepojenia na potvrdenie e-mailu"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:152
msgid "Reset"
msgstr "Obnovenie"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:201
msgid "Reset Layout"
msgstr "Obnovenie rozloženia"

#: apps/client/src/pages/auth/reset-password/page.tsx:60
#: apps/client/src/pages/auth/reset-password/page.tsx:65
msgid "Reset your password"
msgstr "Obnovenie hesla"

#: apps/client/src/pages/builder/_components/toolbar.tsx:124
msgid "Reset Zoom"
msgstr "Obnovenie priblíženia"

#: apps/client/src/pages/dashboard/_components/sidebar.tsx:86
#: apps/client/src/pages/dashboard/resumes/page.tsx:20
#: apps/client/src/pages/dashboard/resumes/page.tsx:37
msgid "Resumes"
msgstr "Životopisy"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:14
msgid "Resumes Generated"
msgstr "Vytvorené životopisy"

#: apps/client/src/pages/home/<USER>/features/index.tsx:105
msgid "Rich in features, not in pricing."
msgstr "Bohaté funkcie, nie však ceny."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:138
msgid "Rounded"
msgstr "Zaoblené"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:180
#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:245
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:217
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:131
msgid "Save Changes"
msgstr "Uloženie zmien"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:176
msgid "Save Locally"
msgstr "Uložiť lokálne"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:176
msgid "Saved"
msgstr "Uložené"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:168
msgid "Scan the QR code below with your authenticator app to setup 2FA on your account."
msgstr "Naskenujte nižšie uvedený kód QR pomocou aplikácie autentifikátora a nastavte 2FA na svojom konte."

#. Score or honors for the degree, for example, CGPA or magna cum laude
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:92
msgid "Score"
msgstr "Skóre"

#: apps/client/src/pages/builder/_components/toolbar.tsx:104
msgid "Scroll to Pan"
msgstr "Posuňte sa na Pan"

#: apps/client/src/pages/builder/_components/toolbar.tsx:104
msgid "Scroll to Zoom"
msgstr "Posúvanie na zväčšenie"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:111
msgid "Search for a font family"
msgstr "Vyhľadanie rodiny písiem"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:126
msgid "Search for a font subset"
msgstr "Vyhľadanie podmnožiny písma"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:139
msgid "Search for a font variant"
msgstr "Vyhľadanie variantu písma"

#: apps/client/src/components/locale-combobox.tsx:41
msgid "Search for a language"
msgstr "Vyhľadávanie jazyka"

#: apps/client/src/pages/home/<USER>/features/index.tsx:56
msgid "Secure with two-factor authentication"
msgstr "Zabezpečenie pomocou dvojfaktorového overovania"

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:65
msgid "Security"
msgstr "Zabezpečenie"

#: apps/client/src/pages/home/<USER>/features/index.tsx:50
msgid "Self-host with Docker"
msgstr "Vlastný hostiteľ pomocou nástroja Docker"

#: apps/client/src/pages/auth/forgot-password/page.tsx:104
msgid "Send Email"
msgstr "Odoslať e-mail"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:74
msgid "Send me a message"
msgstr "Pošlite mi správu"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:97
msgid "Separate Links"
msgstr "Samostatné odkazy"

#: apps/client/src/components/user-options.tsx:32
#: apps/client/src/pages/dashboard/_components/sidebar.tsx:92
#: apps/client/src/pages/dashboard/settings/page.tsx:16
#: apps/client/src/pages/dashboard/settings/page.tsx:26
msgid "Settings"
msgstr "Nastavenia"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:159
msgid "Setup two-factor authentication on your account"
msgstr "Nastavenie dvojfaktorového overovania na svojom konte"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:107
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:38
#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:39
msgid "Sharing"
msgstr "Zdieľanie"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx:106
msgid "Show"
msgstr "Zobraziť"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:78
msgid "Show Break Line"
msgstr "Zobraziť čiaru prestávky"

#: apps/client/src/pages/builder/sidebars/right/sections/page.tsx:91
msgid "Show Page Numbers"
msgstr "Zobraziť čísla strán"

#: apps/client/src/pages/builder/sidebars/right/sections/layout.tsx:248
msgid "Sidebar"
msgstr "Bočný panel"

#: apps/client/src/pages/auth/backup-otp/page.tsx:103
#: apps/client/src/pages/auth/login/page.tsx:127
#: apps/client/src/pages/auth/verify-otp/page.tsx:92
msgid "Sign in"
msgstr "Prihlásiť sa"

#: apps/client/src/pages/auth/register/page.tsx:74
msgid "Sign in now"
msgstr "Prihláste sa teraz"

#: apps/client/src/pages/auth/login/page.tsx:55
#: apps/client/src/pages/auth/login/page.tsx:60
msgid "Sign in to your account"
msgstr "Prihláste sa do svojho konta"

#: apps/client/src/pages/home/<USER>/features/index.tsx:55
msgid "Sign in with Email"
msgstr "Prihláste sa pomocou e-mailu"

#: apps/client/src/pages/home/<USER>/features/index.tsx:53
msgid "Sign in with GitHub"
msgstr "Prihláste sa pomocou služby GitHub"

#: apps/client/src/pages/home/<USER>/features/index.tsx:54
msgid "Sign in with Google"
msgstr "Prihlásenie pomocou služby Google"

#: apps/client/src/pages/auth/register/page.tsx:179
msgid "Sign up"
msgstr "Zaregistrujte sa"

#: apps/client/src/pages/auth/login/page.tsx:74
msgid "Signing in via email is currently disabled by the administrator."
msgstr "Prihlasovanie prostredníctvom e-mailu je v súčasnosti správcom zakázané."

#: apps/client/src/pages/auth/register/page.tsx:82
msgid "Signups are currently disabled by the administrator."
msgstr "Prihlasovanie je v súčasnosti zakázané správcom."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:65
msgid "Size (in px)"
msgstr "Veľkosť (v px)"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:228
msgid "Slug"
msgstr "Slimák"

#: apps/client/src/services/errors/translate-error.ts:55
msgid "Something went wrong while grabbing a preview your resume. Please try again later or raise an issue on GitHub."
msgstr "Pri prezeraní vášho životopisu sa niečo pokazilo. Prosím, skúste to neskôr alebo nahláste problém na GitHub."

#: apps/client/src/services/errors/translate-error.ts:52
msgid "Something went wrong while printing your resume. Please try again later or raise an issue on GitHub."
msgstr "Pri tlači vášho životopisu sa niečo pokazilo. Skúste to prosím neskôr alebo nahláste problém na GitHube."

#: apps/client/src/services/errors/translate-error.ts:58
msgid "Something went wrong while processing your request. Please try again later or raise an issue on GitHub."
msgstr "Pri spracovaní vašej žiadosti sa niečo pokazilo. Prosím, skúste to neskôr alebo zadajte problém na GitHube."

#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:87
#: apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx:132
msgid "Square"
msgstr "Štvorec"

#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/create-card.tsx:33
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/create-item.tsx:23
msgid "Start building from scratch"
msgstr "Začnite stavať od nuly"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:185
msgid "Start building your resume by giving it a name."
msgstr "Svoj životopis začnite tvoriť tak, že mu dáte meno."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:114
#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:22
#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:23
msgid "Statistics"
msgstr "Štatistika"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:38
msgid "Statistics are available only for public resumes."
msgstr "Štatistiky sú k dispozícii len pre verejné životopisy."

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:162
msgid "Store your backup codes securely"
msgstr "Bezpečné ukladanie záložných kódov"

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:101
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:95
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:129
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:138
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:114
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:129
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:95
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:109
msgid "Summary"
msgstr "Zhrnutie"

#: apps/client/src/pages/builder/sidebars/right/sections/information.tsx:18
msgid "Support the app by donating what you can!"
msgstr "Podporte aplikáciu darovaním toho, čo môžete!"

#: apps/client/src/pages/home/<USER>/support/index.tsx:9
msgid "Supporting Reactive Resume"
msgstr "Podporný reaktívny životopis"

#: apps/client/src/pages/home/<USER>/features/index.tsx:65
msgid "Supports A4/Letter page formats"
msgstr "Podporuje formáty strán A4/Letter"

#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:85
msgid "System"
msgstr "Systém"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:65
#: apps/client/src/pages/builder/sidebars/right/sections/template.tsx:18
#: apps/client/src/pages/builder/sidebars/right/sections/template.tsx:19
msgid "Template"
msgstr "Šablóna"

#: apps/client/src/pages/home/<USER>/templates/index.tsx:9
msgid "Templates"
msgstr "Šablóny"

#: apps/client/src/pages/home/<USER>/testimonials/index.tsx:68
msgid "Testimonials"
msgstr "Skúsenosti"

#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:103
msgid "Text Color"
msgstr "Farba textu"

#: apps/client/src/pages/public/error.tsx:17
msgid "The page you're looking for doesn't exist."
msgstr ""

#: apps/client/src/pages/public/error.tsx:29
msgid "The request was invalid."
msgstr ""

#: apps/client/src/services/errors/translate-error.ts:49
msgid "The resume you want to update is locked, please unlock if you wish to make any changes to it."
msgstr "Životopis, ktorý chcete aktualizovať, je uzamknutý, ak v ňom chcete vykonať akékoľvek zmeny, odomknite ho."

#: apps/client/src/pages/builder/sidebars/right/index.tsx:86
#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:19
#: apps/client/src/pages/builder/sidebars/right/sections/theme.tsx:20
#: apps/client/src/pages/dashboard/settings/_sections/profile.tsx:79
msgid "Theme"
msgstr "Téma"

#: apps/client/src/services/errors/translate-error.ts:40
msgid "There was an error connecting to the browser. Please make sure 'chrome' is running and reachable."
msgstr "Pri pripájaní k prehliadaču došlo k chybe. Uistite sa, že je prehliadač 'chrome' spustený a dostupný."

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:130
msgid "This action can be reverted by clicking on the undo button in the floating toolbar."
msgstr "Túto akciu môžete vrátiť kliknutím na tlačidlo Zrušiť v plávajúcom paneli nástrojov."

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:151
msgid "This action cannot be undone. This will permanently delete your resume and cannot be recovered."
msgstr "Túto akciu nemožno vrátiť späť. Tým sa váš životopis natrvalo vymaže a nie je možné ho obnoviť."

#: apps/client/src/services/errors/translate-error.ts:16
msgid "This email address is associated with an OAuth account. Please sign in with your OAuth provider."
msgstr "Táto e-mailová adresa je priradená k účtu OAuth. Prihláste sa pomocou svojho poskytovateľa OAuth."

#: apps/client/src/pages/builder/_components/header.tsx:57
msgid "This resume is locked, please unlock to make further changes."
msgstr "Tento životopis je uzamknutý, ak chcete vykonať ďalšie zmeny, odomknite ho."

#: apps/client/src/pages/builder/sidebars/right/sections/notes.tsx:23
msgid "This section is reserved for your personal notes specific to this resume. The content here remains private and is not shared with anyone else."
msgstr "Táto časť je vyhradená pre vaše osobné poznámky týkajúce sa tohto životopisu. Obsah tu zostáva súkromný a nie je poskytovaný nikomu inému."

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:216
msgid "Tip: You can name the resume referring to the position you are applying for."
msgstr "Tip: Životopis môžete pomenovať podľa pozície, o ktorú sa uchádzate."

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:39
msgctxt "Name of the Award"
msgid "Title"
msgstr "Názov"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:196
msgid "Title"
msgstr "Názov"

#: apps/client/src/pages/builder/_components/toolbar.tsx:138
msgid "Toggle Page Break Line"
msgstr "Prepínanie riadku prelomu stránky"

#: apps/client/src/pages/builder/_components/toolbar.tsx:150
msgid "Toggle Page Numbers"
msgstr "Prepínanie čísiel strán"

#: apps/client/src/pages/home/<USER>/features/index.tsx:68
msgid "Track views and downloads"
msgstr "Sledovanie zobrazení a stiahnutí"

#: apps/client/src/pages/auth/verify-otp/page.tsx:52
#: apps/client/src/pages/auth/verify-otp/page.tsx:57
#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:129
msgid "Two-Factor Authentication"
msgstr "Dvojfaktorové overovanie"

#: apps/client/src/services/errors/translate-error.ts:31
msgid "Two-factor authentication is already enabled for this account."
msgstr "Pre toto konto je už povolené dvojfaktorové overovanie."

#: apps/client/src/services/errors/translate-error.ts:28
msgid "Two-factor authentication is not enabled for this account."
msgstr "Pre toto konto nie je povolené dvojfaktorové overovanie."

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:84
msgid "Type <0>delete</0> to confirm deleting your account."
msgstr "Zadaním príkazu <0>delete</0> potvrďte vymazanie svojho konta."

#. For example, Bachelor's Degree or Master's Degree
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:54
msgid "Type of Study"
msgstr "Typ štúdie"

#: apps/client/src/pages/builder/sidebars/right/index.tsx:79
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:76
#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:77
msgid "Typography"
msgstr "Typografia"

#: apps/client/src/pages/builder/sidebars/right/sections/typography.tsx:203
msgid "Underline Links"
msgstr "Podčiarknite odkazy"

#: apps/client/src/pages/builder/_components/toolbar.tsx:76
msgid "Undo"
msgstr "Zrušiť"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:52
#: apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx:110
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:105
#: apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx:161
msgid "Unlock"
msgstr "Odomknutie stránky"

#: apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx:44
msgid "Unlocking a resume will allow you to make changes to it again."
msgstr "Odomknutie životopisu vám umožní znovu v ňom vykonať zmeny."

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:192
msgid "Unverified"
msgstr "Neoverené"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx:164
msgid "Update an existing item"
msgstr "Aktualizácia existujúcej položky"

#: apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx:179
msgid "Update an existing resume"
msgstr "Aktualizácia existujúceho životopisu"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:212
msgid "Upload a file from one of the accepted sources to parse existing data and import it into Reactive Resume for easier editing."
msgstr "Nahrajte súbor z jedného z akceptovaných zdrojov, aby ste mohli analyzovať existujúce údaje a importovať ich do aplikácie Reactive Resume na jednoduchšie úpravy."

#: apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx:73
msgid "URL"
msgstr "ADRESA URL"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx:61
msgid "URL must start with https://"
msgstr "Adresa URL musí začínať https://"

#: apps/client/src/pages/auth/backup-otp/page.tsx:52
#: apps/client/src/pages/auth/backup-otp/page.tsx:57
msgid "Use your backup code"
msgstr "Použite svoj záložný kód"

#: apps/client/src/services/errors/translate-error.ts:13
msgid "User does not have an associated 'secrets' record. Please report this issue on GitHub."
msgstr "Používateľ nemá priradený záznam \"secrets\". Nahláste tento problém na GitHub."

#: apps/client/src/pages/auth/register/page.tsx:119
#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:55
#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:167
msgid "Username"
msgstr "Používateľské meno"

#: apps/client/src/pages/home/<USER>/statistics/index.tsx:13
msgid "Users Signed Up"
msgstr "Zaregistrovaní používatelia"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:296
msgid "Validate"
msgstr "Overenie"

#: apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx:314
msgid "Validated"
msgstr "Overené"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:97
msgid "Value"
msgstr "Hodnota"

#: apps/client/src/pages/dashboard/settings/_sections/account.tsx:192
msgid "Verified"
msgstr "Overené"

#: apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx:161
msgid "Verify that two-factor authentication has been setup correctly"
msgstr "Overenie správneho nastavenia dvojfaktorového overovania"

#: apps/client/src/pages/auth/verify-email/page.tsx:43
#: apps/client/src/pages/auth/verify-email/page.tsx:48
msgid "Verify your email address"
msgstr "Overenie vašej e-mailovej adresy"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:26
msgid "Version 4"
msgstr "Verzia 4"

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:51
msgid "Views"
msgstr "Zobrazenia"

#: apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx:87
msgid "Visible"
msgstr "Viditeľné"

#: apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx:70
msgid "Visit <0>Phosphor Icons</0> for a list of available icons"
msgstr "Zoznam dostupných ikon nájdete na stránke <0>Ikony fosforu</0>"

#: apps/client/src/pages/auth/verify-email/page.tsx:61
msgid "We verify your email address only to ensure that we can send you a password reset link in case you forget your password."
msgstr "Vašu e-mailovú adresu overujeme len preto, aby sme vám mohli poslať odkaz na obnovenie hesla v prípade, že ho zabudnete."

#: apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx:87
#: apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:115
#: apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx:124
#: apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx:100
#: apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx:69
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:115
#: apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx:81
#: apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx:67
#: apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx:95
#: apps/client/src/pages/builder/sidebars/left/sections/basics.tsx:69
msgid "Website"
msgstr "Webová lokalita"

#: apps/client/src/pages/home/<USER>/hero/index.tsx:32
msgid "What's new in the latest version"
msgstr "Novinky v najnovšej verzii"

#: apps/client/src/pages/public/error.tsx:26
msgid "You are not authorized to access this page."
msgstr ""

#: apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx:164
#: apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx:68
#: apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx:164
#: apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx:114
msgid "You can add multiple keywords by separating them with a comma or pressing enter."
msgstr "Môžete pridať viacero kľúčových slov tak, že ich oddelíte čiarkou alebo stlačíte kláves enter."

#: apps/client/src/pages/auth/login/page.tsx:99
msgid "You can also enter your username."
msgstr "Môžete tiež zadať svoje používateľské meno."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:103
msgid "You can also integrate with Ollama simply by setting the API key to `sk-1234567890abcdef` and the Base URL to your Ollama URL, i.e. `http://localhost:11434/v1`. You can also pick and choose models and set the max tokens as per your preference."
msgstr "Môžete sa tiež integrovať s Ollama jednoducho nastavením kľúča API na `sk-1234567890abcdef` a základnej adresy URL na vašu adresu URL Ollama, t. j. `http://localhost:11434/v1`. Môžete si tiež vybrať modely a nastaviť maximálne tokeny podľa svojich preferencií."

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:81
msgid "You can make use of the OpenAI API to help you generate content, or improve your writing while composing your resume."
msgstr "Rozhranie API OpenAI môžete využiť na pomoc pri vytváraní obsahu alebo na zlepšenie písania pri písaní životopisu."

#: apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx:40
msgid "You can track the number of views your resume has received, or how many people have downloaded the resume by enabling public sharing."
msgstr "Ak povolíte verejné zdieľanie, môžete sledovať počet zobrazení vášho životopisu alebo počet ľudí, ktorí si ho stiahli."

#: apps/client/src/pages/public/error.tsx:20
msgid "You don't have permission to access this page."
msgstr ""

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:87
msgid "You have the option to <0>obtain your own OpenAI API key</0>. This key empowers you to leverage the API as you see fit. Alternatively, if you wish to disable the AI features in Reactive Resume altogether, you can simply remove the key from your settings."
msgstr "Máte možnosť <0>získať vlastný kľúč API od OpenAI</0>. Tento kľúč vám umožňuje využívať API podľa vlastného uváženia. Prípadne, ak chcete funkcie umelej inteligencie v Reactive Resume úplne vypnúť, môžete jednoducho odstrániť kľúč z nastavení."

#: apps/client/src/pages/auth/verify-email/page.tsx:50
msgid "You should have received an email from <0>Reactive Resume</0> with a link to verify your account."
msgstr "Od spoločnosti <0>Reactive Resume</0> by ste mali dostať e-mail s odkazom na overenie vášho konta."

#: apps/client/src/pages/auth/forgot-password/page.tsx:49
#: apps/client/src/pages/auth/forgot-password/page.tsx:54
msgid "You've got mail!"
msgstr "Máte poštu!"

#: apps/client/src/pages/dashboard/settings/_sections/danger.tsx:52
msgid "Your account and all your data has been deleted successfully. Goodbye!"
msgstr "Vaše konto a všetky vaše údaje boli úspešne odstránené. Zbohom!"

#: apps/client/src/pages/dashboard/settings/_sections/openai.tsx:191
msgid "Your API key is securely stored in the browser's local storage and is only utilized when making requests to OpenAI via their official SDK. Rest assured that your key is not transmitted to any external server except when interacting with OpenAI's services."
msgstr "Váš kľúč API je bezpečne uložený v miestnom úložisku prehliadača a používa sa len pri zadávaní požiadaviek na OpenAI prostredníctvom ich oficiálneho SDK. Môžete si byť istí, že váš kľúč sa neprenáša na žiadny externý server okrem prípadov, keď komunikujete so službami OpenAI."

#: apps/client/src/pages/auth/verify-email/page.tsx:28
msgid "Your email address has been verified successfully."
msgstr "Vaša e-mailová adresa bola úspešne overená."

#: apps/client/src/services/openai/client.ts:11
msgid "Your OpenAI API Key has not been set yet. Please go to your account settings to enable OpenAI Integration."
msgstr "Váš kľúč API OpenAI ešte nebol nastavený. Prejdite do nastavení svojho účtu a povoľte integráciu OpenAI."

#: apps/client/src/pages/dashboard/settings/_sections/security.tsx:56
msgid "Your password has been updated successfully."
msgstr "Vaše heslo bolo úspešne aktualizované."

#: apps/client/src/pages/builder/_components/toolbar.tsx:112
msgid "Zoom In"
msgstr "Priblíženie"

#: apps/client/src/pages/builder/_components/toolbar.tsx:118
msgid "Zoom Out"
msgstr "Zväčšenie a zmenšenie"

