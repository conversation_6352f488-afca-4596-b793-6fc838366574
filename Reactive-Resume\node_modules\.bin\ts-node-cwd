#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46/node_modules/ts-node/dist/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46/node_modules/ts-node/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46/node_modules/ts-node/dist/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46/node_modules/ts-node/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46/node_modules/ts-node/dist/bin-cwd.js" "$@"
else
  exec node  "$basedir/../.pnpm/ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46/node_modules/ts-node/dist/bin-cwd.js" "$@"
fi
