{"version": 3, "sources": ["../../../.pnpm/react-parallax-tilt@1.7.277_6a2a4df8cdbff48fe8a90171fbcb3cfd/node_modules/react-parallax-tilt/dist/modern/index.js"], "sourcesContent": ["import{jsx as t}from\"react/jsx-runtime\";import{PureComponent as e}from\"react\";const i=(t,e,i,n)=>{t.style.transition=`${e} ${i}ms ${n}`},n=(t,e,i)=>Math.min(Math.max(t,e),i);class s{constructor(t,e){this.glareAngle=0,this.glareOpacity=0,this.calculateGlareSize=t=>{const{width:e,height:i}=t,n=Math.sqrt(e**2+i**2);return{width:n,height:n}},this.setSize=t=>{const e=this.calculateGlareSize(t);this.glareEl.style.width=`${e.width}px`,this.glareEl.style.height=`${e.height}px`},this.update=(t,e,i,n)=>{this.updateAngle(t,e.glareReverse),this.updateOpacity(t,e,i,n)},this.updateAngle=(t,e)=>{const{xPercentage:i,yPercentage:n}=t,s=180/Math.PI,r=i?Math.atan2(n,-i)*s:0;this.glareAngle=r-(e?180:0)},this.updateOpacity=(t,e,i,s)=>{const{xPercentage:r,yPercentage:l}=t,{glarePosition:a,glareReverse:o,glareMaxOpacity:h}=e,p=i?-1:1,c=s?-1:1,g=o?-1:1;let d=0;switch(a){case\"top\":d=-r*p*g;break;case\"right\":d=l*c*g;break;case\"bottom\":case void 0:d=r*p*g;break;case\"left\":d=-l*c*g;break;case\"all\":d=Math.hypot(r,l)}const u=n(d,0,100);this.glareOpacity=u*h/100},this.render=t=>{const{glareColor:e}=t;this.glareEl.style.transform=`rotate(${this.glareAngle}deg) translate(-50%, -50%)`,this.glareEl.style.opacity=this.glareOpacity.toString(),this.glareEl.style.background=`linear-gradient(0deg, rgba(255,255,255,0) 0%, ${e} 100%)`},this.glareWrapperEl=document.createElement(\"div\"),this.glareEl=document.createElement(\"div\"),this.glareWrapperEl.appendChild(this.glareEl),this.glareWrapperEl.className=\"glare-wrapper\",this.glareEl.className=\"glare\";const i={position:\"absolute\",top:\"0\",left:\"0\",width:\"100%\",height:\"100%\",overflow:\"hidden\",borderRadius:e,WebkitMaskImage:\"-webkit-radial-gradient(white, black)\",pointerEvents:\"none\"},s=this.calculateGlareSize(t),r={position:\"absolute\",top:\"50%\",left:\"50%\",transformOrigin:\"0% 0%\",pointerEvents:\"none\",width:`${s.width}px`,height:`${s.height}px`};Object.assign(this.glareWrapperEl.style,i),Object.assign(this.glareEl.style,r)}}class r{constructor(){this.glareAngle=0,this.glareOpacity=0,this.tiltAngleX=0,this.tiltAngleY=0,this.tiltAngleXPercentage=0,this.tiltAngleYPercentage=0,this.update=(t,e)=>{this.updateTilt(t,e),this.updateTiltManualInput(t,e),this.updateTiltReverse(e),this.updateTiltLimits(e)},this.updateTilt=(t,e)=>{const{xPercentage:i,yPercentage:n}=t,{tiltMaxAngleX:s,tiltMaxAngleY:r}=e;this.tiltAngleX=i*s/100,this.tiltAngleY=n*r/100*-1},this.updateTiltManualInput=(t,e)=>{const{tiltAngleXManual:i,tiltAngleYManual:n,tiltMaxAngleX:s,tiltMaxAngleY:r}=e;(null!==i||null!==n)&&(this.tiltAngleX=null!==i?i:0,this.tiltAngleY=null!==n?n:0,t.xPercentage=100*this.tiltAngleX/s,t.yPercentage=100*this.tiltAngleY/r)},this.updateTiltReverse=t=>{const e=t.tiltReverse?-1:1;this.tiltAngleX=e*this.tiltAngleX,this.tiltAngleY=e*this.tiltAngleY},this.updateTiltLimits=t=>{const{tiltAxis:e}=t;this.tiltAngleX=n(this.tiltAngleX,-90,90),this.tiltAngleY=n(this.tiltAngleY,-90,90);e&&(this.tiltAngleX=\"x\"===e?this.tiltAngleX:0,this.tiltAngleY=\"y\"===e?this.tiltAngleY:0)},this.updateTiltAnglesPercentage=t=>{const{tiltMaxAngleX:e,tiltMaxAngleY:i}=t;this.tiltAngleXPercentage=this.tiltAngleX/e*100,this.tiltAngleYPercentage=this.tiltAngleY/i*100},this.render=t=>{t.style.transform+=`rotateX(${this.tiltAngleX}deg) rotateY(${this.tiltAngleY}deg) `}}}const l={scale:1,perspective:1e3,flipVertically:!1,flipHorizontally:!1,reset:!0,transitionEasing:\"cubic-bezier(.03,.98,.52,.99)\",transitionSpeed:400,trackOnWindow:!1,gyroscope:!1,...{tiltEnable:!0,tiltReverse:!1,tiltAngleXInitial:0,tiltAngleYInitial:0,tiltMaxAngleX:20,tiltMaxAngleY:20,tiltAxis:void 0,tiltAngleXManual:null,tiltAngleYManual:null},glareEnable:!1,glareMaxOpacity:.7,glareColor:\"#ffffff\",glarePosition:\"bottom\",glareReverse:!1,glareBorderRadius:\"0\"};class a extends e{constructor(){super(...arguments),this.wrapperEl={node:null,size:{width:0,height:0,left:0,top:0},clientPosition:{x:null,y:null,xPercentage:0,yPercentage:0},updateAnimationId:null,scale:1},this.tilt=null,this.glare=null,this.addDeviceOrientationEventListener=async()=>{if(!window.DeviceOrientationEvent)return;const t=DeviceOrientationEvent.requestPermission;if(\"function\"==typeof t){\"granted\"===await t()&&window.addEventListener(\"deviceorientation\",this.onMove)}else window.addEventListener(\"deviceorientation\",this.onMove)},this.setSize=()=>{this.setWrapperElSize(),this.glare&&this.glare.setSize(this.wrapperEl.size)},this.mainLoop=t=>{null!==this.wrapperEl.updateAnimationId&&cancelAnimationFrame(this.wrapperEl.updateAnimationId),this.processInput(t),this.update(t.type),this.wrapperEl.updateAnimationId=requestAnimationFrame(this.renderFrame)},this.onEnter=t=>{const{onEnter:e}=this.props;this.setSize(),this.wrapperEl.node.style.willChange=\"transform\",this.setTransitions(),e&&e({event:t})},this.onMove=t=>{this.mainLoop(t),this.emitOnMove(t)},this.onLeave=t=>{const{onLeave:e}=this.props;if(this.setTransitions(),e&&e({event:t}),this.props.reset){const t=new CustomEvent(\"autoreset\");this.onMove(t)}},this.processInput=t=>{const{scale:e}=this.props;switch(t.type){case\"mousemove\":this.wrapperEl.clientPosition.x=t.pageX,this.wrapperEl.clientPosition.y=t.pageY,this.wrapperEl.scale=e;break;case\"touchmove\":this.wrapperEl.clientPosition.x=t.touches[0].pageX,this.wrapperEl.clientPosition.y=t.touches[0].pageY,this.wrapperEl.scale=e;break;case\"deviceorientation\":this.processInputDeviceOrientation(t),this.wrapperEl.scale=e;break;case\"autoreset\":{const{tiltAngleXInitial:t,tiltAngleYInitial:e,tiltMaxAngleX:i,tiltMaxAngleY:s}=this.props,r=e/s*100;this.wrapperEl.clientPosition.xPercentage=n(t/i*100,-100,100),this.wrapperEl.clientPosition.yPercentage=n(r,-100,100),this.wrapperEl.scale=1;break}}},this.processInputDeviceOrientation=t=>{if(!t.gamma||!t.beta||!this.props.gyroscope)return;const{tiltMaxAngleX:e,tiltMaxAngleY:i}=this.props,s=t.gamma;this.wrapperEl.clientPosition.xPercentage=t.beta/e*100,this.wrapperEl.clientPosition.yPercentage=s/i*100,this.wrapperEl.clientPosition.xPercentage=n(this.wrapperEl.clientPosition.xPercentage,-100,100),this.wrapperEl.clientPosition.yPercentage=n(this.wrapperEl.clientPosition.yPercentage,-100,100)},this.update=t=>{const{tiltEnable:e,flipVertically:i,flipHorizontally:n}=this.props;\"autoreset\"!==t&&\"deviceorientation\"!==t&&\"propChange\"!==t&&this.updateClientInput(),e&&this.tilt.update(this.wrapperEl.clientPosition,this.props),this.updateFlip(),this.tilt.updateTiltAnglesPercentage(this.props),this.glare&&this.glare.update(this.wrapperEl.clientPosition,this.props,i,n)},this.updateClientInput=()=>{const{trackOnWindow:t}=this.props;let e,i;if(t){const{x:t,y:n}=this.wrapperEl.clientPosition;e=n/window.innerHeight*200-100,i=t/window.innerWidth*200-100}else{const{size:{width:t,height:n,left:s,top:r},clientPosition:{x:l,y:a}}=this.wrapperEl;e=(a-r)/n*200-100,i=(l-s)/t*200-100}this.wrapperEl.clientPosition.xPercentage=n(e,-100,100),this.wrapperEl.clientPosition.yPercentage=n(i,-100,100)},this.updateFlip=()=>{const{flipVertically:t,flipHorizontally:e}=this.props;t&&(this.tilt.tiltAngleX+=180,this.tilt.tiltAngleY*=-1),e&&(this.tilt.tiltAngleY+=180)},this.renderFrame=()=>{this.resetWrapperElTransform(),this.renderPerspective(),this.tilt.render(this.wrapperEl.node),this.renderScale(),this.glare&&this.glare.render(this.props)}}componentDidMount(){if(this.tilt=new r,this.initGlare(),this.setSize(),this.addEventListeners(),\"undefined\"==typeof CustomEvent)return;const t=new CustomEvent(\"autoreset\");this.mainLoop(t);const e=new CustomEvent(\"initial\");this.emitOnMove(e)}componentWillUnmount(){null!==this.wrapperEl.updateAnimationId&&cancelAnimationFrame(this.wrapperEl.updateAnimationId),this.removeEventListeners()}componentDidUpdate(){const t=new CustomEvent(\"propChange\");this.mainLoop(t),this.emitOnMove(t)}addEventListeners(){const{trackOnWindow:t,gyroscope:e}=this.props;window.addEventListener(\"resize\",this.setSize),t&&(window.addEventListener(\"mouseenter\",this.onEnter),window.addEventListener(\"mousemove\",this.onMove),window.addEventListener(\"mouseout\",this.onLeave),window.addEventListener(\"touchstart\",this.onEnter),window.addEventListener(\"touchmove\",this.onMove),window.addEventListener(\"touchend\",this.onLeave)),e&&this.addDeviceOrientationEventListener()}removeEventListeners(){const{trackOnWindow:t,gyroscope:e}=this.props;window.removeEventListener(\"resize\",this.setSize),t&&(window.removeEventListener(\"mouseenter\",this.onEnter),window.removeEventListener(\"mousemove\",this.onMove),window.removeEventListener(\"mouseout\",this.onLeave),window.removeEventListener(\"touchstart\",this.onEnter),window.removeEventListener(\"touchmove\",this.onMove),window.removeEventListener(\"touchend\",this.onLeave)),e&&window.DeviceOrientationEvent&&window.removeEventListener(\"deviceorientation\",this.onMove)}setWrapperElSize(){const t=this.wrapperEl.node.getBoundingClientRect();this.wrapperEl.size.width=this.wrapperEl.node.offsetWidth,this.wrapperEl.size.height=this.wrapperEl.node.offsetHeight,this.wrapperEl.size.left=t.left+window.scrollX,this.wrapperEl.size.top=t.top+window.scrollY}initGlare(){const{glareEnable:t,glareBorderRadius:e}=this.props;t&&(this.glare=new s(this.wrapperEl.size,e),this.wrapperEl.node.appendChild(this.glare.glareWrapperEl))}emitOnMove(t){const{onMove:e}=this.props;if(!e)return;let i=0,n=0;this.glare&&(i=this.glare.glareAngle,n=this.glare.glareOpacity),e({tiltAngleX:this.tilt.tiltAngleX,tiltAngleY:this.tilt.tiltAngleY,tiltAngleXPercentage:this.tilt.tiltAngleXPercentage,tiltAngleYPercentage:this.tilt.tiltAngleYPercentage,glareAngle:i,glareOpacity:n,event:t})}resetWrapperElTransform(){this.wrapperEl.node.style.transform=\"\"}renderPerspective(){const{perspective:t}=this.props;this.wrapperEl.node.style.transform+=`perspective(${t}px) `}renderScale(){const{scale:t}=this.wrapperEl;this.wrapperEl.node.style.transform+=`scale3d(${t},${t},${t})`}setTransitions(){const{transitionSpeed:t,transitionEasing:e}=this.props;i(this.wrapperEl.node,\"all\",t,e),this.glare&&i(this.glare.glareEl,\"opacity\",t,e)}render(){const{children:e,className:i,style:n}=this.props;return t(\"div\",{ref:t=>{this.wrapperEl.node=t},onMouseEnter:this.onEnter,onMouseMove:this.onMove,onMouseLeave:this.onLeave,onTouchStart:this.onEnter,onTouchMove:this.onMove,onTouchEnd:this.onLeave,className:i,style:n,children:e})}}a.defaultProps=l;export{a as default};\n"], "mappings": ";;;;;;;;;;;AAAA,yBAAoB;AAAoB,mBAA8B;AAAQ,IAAM,IAAE,CAACA,IAAEC,IAAEC,IAAEC,OAAI;AAAC,EAAAH,GAAE,MAAM,aAAW,GAAGC,EAAC,IAAIC,EAAC,MAAMC,EAAC;AAAE;AAAzD,IAA2D,IAAE,CAACH,IAAEC,IAAEC,OAAI,KAAK,IAAI,KAAK,IAAIF,IAAEC,EAAC,GAAEC,EAAC;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYF,IAAEC,IAAE;AAAC,SAAK,aAAW,GAAE,KAAK,eAAa,GAAE,KAAK,qBAAmB,CAAAD,OAAG;AAAC,YAAK,EAAC,OAAMC,IAAE,QAAOC,GAAC,IAAEF,IAAEG,KAAE,KAAK,KAAKF,MAAG,IAAEC,MAAG,CAAC;AAAE,aAAM,EAAC,OAAMC,IAAE,QAAOA,GAAC;AAAA,IAAC,GAAE,KAAK,UAAQ,CAAAH,OAAG;AAAC,YAAMC,KAAE,KAAK,mBAAmBD,EAAC;AAAE,WAAK,QAAQ,MAAM,QAAM,GAAGC,GAAE,KAAK,MAAK,KAAK,QAAQ,MAAM,SAAO,GAAGA,GAAE,MAAM;AAAA,IAAI,GAAE,KAAK,SAAO,CAACD,IAAEC,IAAEC,IAAEC,OAAI;AAAC,WAAK,YAAYH,IAAEC,GAAE,YAAY,GAAE,KAAK,cAAcD,IAAEC,IAAEC,IAAEC,EAAC;AAAA,IAAC,GAAE,KAAK,cAAY,CAACH,IAAEC,OAAI;AAAC,YAAK,EAAC,aAAYC,IAAE,aAAYC,GAAC,IAAEH,IAAEI,KAAE,MAAI,KAAK,IAAGC,KAAEH,KAAE,KAAK,MAAMC,IAAE,CAACD,EAAC,IAAEE,KAAE;AAAE,WAAK,aAAWC,MAAGJ,KAAE,MAAI;AAAA,IAAE,GAAE,KAAK,gBAAc,CAACD,IAAEC,IAAEC,IAAEE,OAAI;AAAC,YAAK,EAAC,aAAYC,IAAE,aAAYC,GAAC,IAAEN,IAAE,EAAC,eAAcO,IAAE,cAAa,GAAE,iBAAgB,EAAC,IAAEN,IAAE,IAAEC,KAAE,KAAG,GAAE,IAAEE,KAAE,KAAG,GAAE,IAAE,IAAE,KAAG;AAAE,UAAI,IAAE;AAAE,cAAOG,IAAE;AAAA,QAAC,KAAI;AAAM,cAAE,CAACF,KAAE,IAAE;AAAE;AAAA,QAAM,KAAI;AAAQ,cAAEC,KAAE,IAAE;AAAE;AAAA,QAAM,KAAI;AAAA,QAAS,KAAK;AAAO,cAAED,KAAE,IAAE;AAAE;AAAA,QAAM,KAAI;AAAO,cAAE,CAACC,KAAE,IAAE;AAAE;AAAA,QAAM,KAAI;AAAM,cAAE,KAAK,MAAMD,IAAEC,EAAC;AAAA,MAAC;AAAC,YAAM,IAAE,EAAE,GAAE,GAAE,GAAG;AAAE,WAAK,eAAa,IAAE,IAAE;AAAA,IAAG,GAAE,KAAK,SAAO,CAAAN,OAAG;AAAC,YAAK,EAAC,YAAWC,GAAC,IAAED;AAAE,WAAK,QAAQ,MAAM,YAAU,UAAU,KAAK,UAAU,8BAA6B,KAAK,QAAQ,MAAM,UAAQ,KAAK,aAAa,SAAS,GAAE,KAAK,QAAQ,MAAM,aAAW,iDAAiDC,EAAC;AAAA,IAAQ,GAAE,KAAK,iBAAe,SAAS,cAAc,KAAK,GAAE,KAAK,UAAQ,SAAS,cAAc,KAAK,GAAE,KAAK,eAAe,YAAY,KAAK,OAAO,GAAE,KAAK,eAAe,YAAU,iBAAgB,KAAK,QAAQ,YAAU;AAAQ,UAAMC,KAAE,EAAC,UAAS,YAAW,KAAI,KAAI,MAAK,KAAI,OAAM,QAAO,QAAO,QAAO,UAAS,UAAS,cAAaD,IAAE,iBAAgB,yCAAwC,eAAc,OAAM,GAAEG,KAAE,KAAK,mBAAmBJ,EAAC,GAAEK,KAAE,EAAC,UAAS,YAAW,KAAI,OAAM,MAAK,OAAM,iBAAgB,SAAQ,eAAc,QAAO,OAAM,GAAGD,GAAE,KAAK,MAAK,QAAO,GAAGA,GAAE,MAAM,KAAI;AAAE,WAAO,OAAO,KAAK,eAAe,OAAMF,EAAC,GAAE,OAAO,OAAO,KAAK,QAAQ,OAAMG,EAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,aAAW,GAAE,KAAK,eAAa,GAAE,KAAK,aAAW,GAAE,KAAK,aAAW,GAAE,KAAK,uBAAqB,GAAE,KAAK,uBAAqB,GAAE,KAAK,SAAO,CAACL,IAAEC,OAAI;AAAC,WAAK,WAAWD,IAAEC,EAAC,GAAE,KAAK,sBAAsBD,IAAEC,EAAC,GAAE,KAAK,kBAAkBA,EAAC,GAAE,KAAK,iBAAiBA,EAAC;AAAA,IAAC,GAAE,KAAK,aAAW,CAACD,IAAEC,OAAI;AAAC,YAAK,EAAC,aAAYC,IAAE,aAAYC,GAAC,IAAEH,IAAE,EAAC,eAAcI,IAAE,eAAcC,GAAC,IAAEJ;AAAE,WAAK,aAAWC,KAAEE,KAAE,KAAI,KAAK,aAAWD,KAAEE,KAAE,MAAI;AAAA,IAAE,GAAE,KAAK,wBAAsB,CAACL,IAAEC,OAAI;AAAC,YAAK,EAAC,kBAAiBC,IAAE,kBAAiBC,IAAE,eAAcC,IAAE,eAAcC,GAAC,IAAEJ;AAAE,OAAC,SAAOC,MAAG,SAAOC,QAAK,KAAK,aAAW,SAAOD,KAAEA,KAAE,GAAE,KAAK,aAAW,SAAOC,KAAEA,KAAE,GAAEH,GAAE,cAAY,MAAI,KAAK,aAAWI,IAAEJ,GAAE,cAAY,MAAI,KAAK,aAAWK;AAAA,IAAE,GAAE,KAAK,oBAAkB,CAAAL,OAAG;AAAC,YAAMC,KAAED,GAAE,cAAY,KAAG;AAAE,WAAK,aAAWC,KAAE,KAAK,YAAW,KAAK,aAAWA,KAAE,KAAK;AAAA,IAAU,GAAE,KAAK,mBAAiB,CAAAD,OAAG;AAAC,YAAK,EAAC,UAASC,GAAC,IAAED;AAAE,WAAK,aAAW,EAAE,KAAK,YAAW,KAAI,EAAE,GAAE,KAAK,aAAW,EAAE,KAAK,YAAW,KAAI,EAAE;AAAE,MAAAC,OAAI,KAAK,aAAW,QAAMA,KAAE,KAAK,aAAW,GAAE,KAAK,aAAW,QAAMA,KAAE,KAAK,aAAW;AAAA,IAAE,GAAE,KAAK,6BAA2B,CAAAD,OAAG;AAAC,YAAK,EAAC,eAAcC,IAAE,eAAcC,GAAC,IAAEF;AAAE,WAAK,uBAAqB,KAAK,aAAWC,KAAE,KAAI,KAAK,uBAAqB,KAAK,aAAWC,KAAE;AAAA,IAAG,GAAE,KAAK,SAAO,CAAAF,OAAG;AAAC,MAAAA,GAAE,MAAM,aAAW,WAAW,KAAK,UAAU,gBAAgB,KAAK,UAAU;AAAA,IAAO;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,EAAC,OAAM,GAAE,aAAY,KAAI,gBAAe,OAAG,kBAAiB,OAAG,OAAM,MAAG,kBAAiB,iCAAgC,iBAAgB,KAAI,eAAc,OAAG,WAAU,OAAG,GAAG,EAAC,YAAW,MAAG,aAAY,OAAG,mBAAkB,GAAE,mBAAkB,GAAE,eAAc,IAAG,eAAc,IAAG,UAAS,QAAO,kBAAiB,MAAK,kBAAiB,KAAI,GAAE,aAAY,OAAG,iBAAgB,KAAG,YAAW,WAAU,eAAc,UAAS,cAAa,OAAG,mBAAkB,IAAG;AAAE,IAAM,IAAN,cAAgB,aAAAC,cAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,YAAU,EAAC,MAAK,MAAK,MAAK,EAAC,OAAM,GAAE,QAAO,GAAE,MAAK,GAAE,KAAI,EAAC,GAAE,gBAAe,EAAC,GAAE,MAAK,GAAE,MAAK,aAAY,GAAE,aAAY,EAAC,GAAE,mBAAkB,MAAK,OAAM,EAAC,GAAE,KAAK,OAAK,MAAK,KAAK,QAAM,MAAK,KAAK,oCAAkC,YAAS;AAAC,UAAG,CAAC,OAAO,uBAAuB;AAAO,YAAMD,KAAE,uBAAuB;AAAkB,UAAG,cAAY,OAAOA,IAAE;AAAC,sBAAY,MAAMA,GAAE,KAAG,OAAO,iBAAiB,qBAAoB,KAAK,MAAM;AAAA,MAAC,MAAM,QAAO,iBAAiB,qBAAoB,KAAK,MAAM;AAAA,IAAC,GAAE,KAAK,UAAQ,MAAI;AAAC,WAAK,iBAAiB,GAAE,KAAK,SAAO,KAAK,MAAM,QAAQ,KAAK,UAAU,IAAI;AAAA,IAAC,GAAE,KAAK,WAAS,CAAAA,OAAG;AAAC,eAAO,KAAK,UAAU,qBAAmB,qBAAqB,KAAK,UAAU,iBAAiB,GAAE,KAAK,aAAaA,EAAC,GAAE,KAAK,OAAOA,GAAE,IAAI,GAAE,KAAK,UAAU,oBAAkB,sBAAsB,KAAK,WAAW;AAAA,IAAC,GAAE,KAAK,UAAQ,CAAAA,OAAG;AAAC,YAAK,EAAC,SAAQC,GAAC,IAAE,KAAK;AAAM,WAAK,QAAQ,GAAE,KAAK,UAAU,KAAK,MAAM,aAAW,aAAY,KAAK,eAAe,GAAEA,MAAGA,GAAE,EAAC,OAAMD,GAAC,CAAC;AAAA,IAAC,GAAE,KAAK,SAAO,CAAAA,OAAG;AAAC,WAAK,SAASA,EAAC,GAAE,KAAK,WAAWA,EAAC;AAAA,IAAC,GAAE,KAAK,UAAQ,CAAAA,OAAG;AAAC,YAAK,EAAC,SAAQC,GAAC,IAAE,KAAK;AAAM,UAAG,KAAK,eAAe,GAAEA,MAAGA,GAAE,EAAC,OAAMD,GAAC,CAAC,GAAE,KAAK,MAAM,OAAM;AAAC,cAAMA,KAAE,IAAI,YAAY,WAAW;AAAE,aAAK,OAAOA,EAAC;AAAA,MAAC;AAAA,IAAC,GAAE,KAAK,eAAa,CAAAA,OAAG;AAAC,YAAK,EAAC,OAAMC,GAAC,IAAE,KAAK;AAAM,cAAOD,GAAE,MAAK;AAAA,QAAC,KAAI;AAAY,eAAK,UAAU,eAAe,IAAEA,GAAE,OAAM,KAAK,UAAU,eAAe,IAAEA,GAAE,OAAM,KAAK,UAAU,QAAMC;AAAE;AAAA,QAAM,KAAI;AAAY,eAAK,UAAU,eAAe,IAAED,GAAE,QAAQ,CAAC,EAAE,OAAM,KAAK,UAAU,eAAe,IAAEA,GAAE,QAAQ,CAAC,EAAE,OAAM,KAAK,UAAU,QAAMC;AAAE;AAAA,QAAM,KAAI;AAAoB,eAAK,8BAA8BD,EAAC,GAAE,KAAK,UAAU,QAAMC;AAAE;AAAA,QAAM,KAAI,aAAY;AAAC,gBAAK,EAAC,mBAAkBD,IAAE,mBAAkBC,IAAE,eAAcC,IAAE,eAAcE,GAAC,IAAE,KAAK,OAAMC,KAAEJ,KAAEG,KAAE;AAAI,eAAK,UAAU,eAAe,cAAY,EAAEJ,KAAEE,KAAE,KAAI,MAAK,GAAG,GAAE,KAAK,UAAU,eAAe,cAAY,EAAEG,IAAE,MAAK,GAAG,GAAE,KAAK,UAAU,QAAM;AAAE;AAAA,QAAK;AAAA,MAAC;AAAA,IAAC,GAAE,KAAK,gCAA8B,CAAAL,OAAG;AAAC,UAAG,CAACA,GAAE,SAAO,CAACA,GAAE,QAAM,CAAC,KAAK,MAAM,UAAU;AAAO,YAAK,EAAC,eAAcC,IAAE,eAAcC,GAAC,IAAE,KAAK,OAAME,KAAEJ,GAAE;AAAM,WAAK,UAAU,eAAe,cAAYA,GAAE,OAAKC,KAAE,KAAI,KAAK,UAAU,eAAe,cAAYG,KAAEF,KAAE,KAAI,KAAK,UAAU,eAAe,cAAY,EAAE,KAAK,UAAU,eAAe,aAAY,MAAK,GAAG,GAAE,KAAK,UAAU,eAAe,cAAY,EAAE,KAAK,UAAU,eAAe,aAAY,MAAK,GAAG;AAAA,IAAC,GAAE,KAAK,SAAO,CAAAF,OAAG;AAAC,YAAK,EAAC,YAAWC,IAAE,gBAAeC,IAAE,kBAAiBC,GAAC,IAAE,KAAK;AAAM,sBAAcH,MAAG,wBAAsBA,MAAG,iBAAeA,MAAG,KAAK,kBAAkB,GAAEC,MAAG,KAAK,KAAK,OAAO,KAAK,UAAU,gBAAe,KAAK,KAAK,GAAE,KAAK,WAAW,GAAE,KAAK,KAAK,2BAA2B,KAAK,KAAK,GAAE,KAAK,SAAO,KAAK,MAAM,OAAO,KAAK,UAAU,gBAAe,KAAK,OAAMC,IAAEC,EAAC;AAAA,IAAC,GAAE,KAAK,oBAAkB,MAAI;AAAC,YAAK,EAAC,eAAcH,GAAC,IAAE,KAAK;AAAM,UAAIC,IAAEC;AAAE,UAAGF,IAAE;AAAC,cAAK,EAAC,GAAEA,IAAE,GAAEG,GAAC,IAAE,KAAK,UAAU;AAAe,QAAAF,KAAEE,KAAE,OAAO,cAAY,MAAI,KAAID,KAAEF,KAAE,OAAO,aAAW,MAAI;AAAA,MAAG,OAAK;AAAC,cAAK,EAAC,MAAK,EAAC,OAAMA,IAAE,QAAOG,IAAE,MAAKC,IAAE,KAAIC,GAAC,GAAE,gBAAe,EAAC,GAAEC,IAAE,GAAEC,GAAC,EAAC,IAAE,KAAK;AAAU,QAAAN,MAAGM,KAAEF,MAAGF,KAAE,MAAI,KAAID,MAAGI,KAAEF,MAAGJ,KAAE,MAAI;AAAA,MAAG;AAAC,WAAK,UAAU,eAAe,cAAY,EAAEC,IAAE,MAAK,GAAG,GAAE,KAAK,UAAU,eAAe,cAAY,EAAEC,IAAE,MAAK,GAAG;AAAA,IAAC,GAAE,KAAK,aAAW,MAAI;AAAC,YAAK,EAAC,gBAAeF,IAAE,kBAAiBC,GAAC,IAAE,KAAK;AAAM,MAAAD,OAAI,KAAK,KAAK,cAAY,KAAI,KAAK,KAAK,cAAY,KAAIC,OAAI,KAAK,KAAK,cAAY;AAAA,IAAI,GAAE,KAAK,cAAY,MAAI;AAAC,WAAK,wBAAwB,GAAE,KAAK,kBAAkB,GAAE,KAAK,KAAK,OAAO,KAAK,UAAU,IAAI,GAAE,KAAK,YAAY,GAAE,KAAK,SAAO,KAAK,MAAM,OAAO,KAAK,KAAK;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,QAAG,KAAK,OAAK,IAAI,KAAE,KAAK,UAAU,GAAE,KAAK,QAAQ,GAAE,KAAK,kBAAkB,GAAE,eAAa,OAAO,YAAY;AAAO,UAAMD,KAAE,IAAI,YAAY,WAAW;AAAE,SAAK,SAASA,EAAC;AAAE,UAAMC,KAAE,IAAI,YAAY,SAAS;AAAE,SAAK,WAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,aAAO,KAAK,UAAU,qBAAmB,qBAAqB,KAAK,UAAU,iBAAiB,GAAE,KAAK,qBAAqB;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAMD,KAAE,IAAI,YAAY,YAAY;AAAE,SAAK,SAASA,EAAC,GAAE,KAAK,WAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,UAAK,EAAC,eAAcA,IAAE,WAAUC,GAAC,IAAE,KAAK;AAAM,WAAO,iBAAiB,UAAS,KAAK,OAAO,GAAED,OAAI,OAAO,iBAAiB,cAAa,KAAK,OAAO,GAAE,OAAO,iBAAiB,aAAY,KAAK,MAAM,GAAE,OAAO,iBAAiB,YAAW,KAAK,OAAO,GAAE,OAAO,iBAAiB,cAAa,KAAK,OAAO,GAAE,OAAO,iBAAiB,aAAY,KAAK,MAAM,GAAE,OAAO,iBAAiB,YAAW,KAAK,OAAO,IAAGC,MAAG,KAAK,kCAAkC;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,UAAK,EAAC,eAAcD,IAAE,WAAUC,GAAC,IAAE,KAAK;AAAM,WAAO,oBAAoB,UAAS,KAAK,OAAO,GAAED,OAAI,OAAO,oBAAoB,cAAa,KAAK,OAAO,GAAE,OAAO,oBAAoB,aAAY,KAAK,MAAM,GAAE,OAAO,oBAAoB,YAAW,KAAK,OAAO,GAAE,OAAO,oBAAoB,cAAa,KAAK,OAAO,GAAE,OAAO,oBAAoB,aAAY,KAAK,MAAM,GAAE,OAAO,oBAAoB,YAAW,KAAK,OAAO,IAAGC,MAAG,OAAO,0BAAwB,OAAO,oBAAoB,qBAAoB,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,UAAMD,KAAE,KAAK,UAAU,KAAK,sBAAsB;AAAE,SAAK,UAAU,KAAK,QAAM,KAAK,UAAU,KAAK,aAAY,KAAK,UAAU,KAAK,SAAO,KAAK,UAAU,KAAK,cAAa,KAAK,UAAU,KAAK,OAAKA,GAAE,OAAK,OAAO,SAAQ,KAAK,UAAU,KAAK,MAAIA,GAAE,MAAI,OAAO;AAAA,EAAO;AAAA,EAAC,YAAW;AAAC,UAAK,EAAC,aAAYA,IAAE,mBAAkBC,GAAC,IAAE,KAAK;AAAM,IAAAD,OAAI,KAAK,QAAM,IAAI,EAAE,KAAK,UAAU,MAAKC,EAAC,GAAE,KAAK,UAAU,KAAK,YAAY,KAAK,MAAM,cAAc;AAAA,EAAE;AAAA,EAAC,WAAWD,IAAE;AAAC,UAAK,EAAC,QAAOC,GAAC,IAAE,KAAK;AAAM,QAAG,CAACA,GAAE;AAAO,QAAIC,KAAE,GAAEC,KAAE;AAAE,SAAK,UAAQD,KAAE,KAAK,MAAM,YAAWC,KAAE,KAAK,MAAM,eAAcF,GAAE,EAAC,YAAW,KAAK,KAAK,YAAW,YAAW,KAAK,KAAK,YAAW,sBAAqB,KAAK,KAAK,sBAAqB,sBAAqB,KAAK,KAAK,sBAAqB,YAAWC,IAAE,cAAaC,IAAE,OAAMH,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,SAAK,UAAU,KAAK,MAAM,YAAU;AAAA,EAAE;AAAA,EAAC,oBAAmB;AAAC,UAAK,EAAC,aAAYA,GAAC,IAAE,KAAK;AAAM,SAAK,UAAU,KAAK,MAAM,aAAW,eAAeA,EAAC;AAAA,EAAM;AAAA,EAAC,cAAa;AAAC,UAAK,EAAC,OAAMA,GAAC,IAAE,KAAK;AAAU,SAAK,UAAU,KAAK,MAAM,aAAW,WAAWA,EAAC,IAAIA,EAAC,IAAIA,EAAC;AAAA,EAAG;AAAA,EAAC,iBAAgB;AAAC,UAAK,EAAC,iBAAgBA,IAAE,kBAAiBC,GAAC,IAAE,KAAK;AAAM,MAAE,KAAK,UAAU,MAAK,OAAMD,IAAEC,EAAC,GAAE,KAAK,SAAO,EAAE,KAAK,MAAM,SAAQ,WAAUD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,UAAK,EAAC,UAASA,IAAE,WAAUC,IAAE,OAAMC,GAAC,IAAE,KAAK;AAAM,eAAO,mBAAAH,KAAE,OAAM,EAAC,KAAI,CAAAA,OAAG;AAAC,WAAK,UAAU,OAAKA;AAAA,IAAC,GAAE,cAAa,KAAK,SAAQ,aAAY,KAAK,QAAO,cAAa,KAAK,SAAQ,cAAa,KAAK,SAAQ,aAAY,KAAK,QAAO,YAAW,KAAK,SAAQ,WAAUE,IAAE,OAAMC,IAAE,UAASF,GAAC,CAAC;AAAA,EAAC;AAAC;AAAC,EAAE,eAAa;", "names": ["t", "e", "i", "n", "s", "r", "l", "a"]}