# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [5.0.1](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v5.0.0...v5.0.1) (2023-06-13)


### Bug Fixes

* improve perf ([#235](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/235)) ([959eb89](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/959eb89aa1e37df88d2decbe6f40c096f10a3745))

## [5.0.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v4.2.2...v5.0.0) (2023-03-27)


### ⚠ BREAKING CHANGES

* update `cssnano` to v6 (migration guide - https://github.com/cssnano/cssnano/releases/tag/cssnano%406.0.0) (#224)

### Features

* update `cssnano` to v6 ([#224](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/224)) ([cfcae0c](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/cfcae0c9e2b7afd740774104a8e5eb675fe984db))

### [4.2.2](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v4.2.1...v4.2.2) (2022-10-13)


### Bug Fixes

* handle `swc` errors ([#202](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/202)) ([b1ce195](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/b1ce195a0de508217ea1a91d7f8e2683e77b495a))

### [4.2.1](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v4.2.0...v4.2.1) (2022-10-06)


### Bug Fixes

* crash ([#200](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/200)) ([c50b2b2](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/c50b2b25348a78a6bfaff248e0fb356a70a4241c))

## [4.2.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v4.1.0...v4.2.0) (2022-09-29)


### Features

* added `swc` minimizer ([#197](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/197)) ([5461421](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/546142104cb30e4711ba800170961822de7d0097))

## [4.1.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v4.0.0...v4.1.0) (2022-09-09)


### Features

* added `lightningcss` support ([#192](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/192)) ([04a3347](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/04a3347610a0044116bfebfd3f3be50d3ada4e04))

## [4.0.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.4.1...v4.0.0) (2022-05-18)


### ⚠ BREAKING CHANGES

* minimum supported `Node.js` version is `14.15.0`

### [3.4.1](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.4.0...v3.4.1) (2022-01-18)


### Bug Fixes

* types ([9c8b0f3](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/9c8b0f35cc6fc8816c387c48c535d71891d8aa65))

## [3.4.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.3.1...v3.4.0) (2022-01-18)


### Features

* added `@parcel/css` support ([#154](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/154)) ([5e5fe51](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/5e5fe512c7209f0ad4c7f9c55f47cf9763adfc74))

### [3.3.1](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.3.0...v3.3.1) (2021-12-21)


### Bug Fixes

* cssnano types ([#147](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/147)) ([a51026e](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/a51026e3b948fa14d2334d823b3bbb6ea482c57b))

## [3.3.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.2.0...v3.3.0) (2021-12-16)


### Features

* removed cjs wrapper and generated types in commonjs format (`export =` and `namespaces` used in types), now you can directly use exported types ([3262a9a](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/3262a9aee2c89e6aed00c4f76443e0d1e07489f0))

## [3.2.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.1.4...v3.2.0) (2021-11-23)


### Features

* added types ([#142](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/142)) ([fb91610](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/fb91610730166b8fb4fb09cee5da781f0ec98e70))

### [3.1.4](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.1.3...v3.1.4) (2021-11-17)


### Chore

* update `schema-utils` package to `4.0.0` version

### [3.1.3](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.1.2...v3.1.3) (2021-11-10)


### Bug Fixes

* source map generation for `cssnano` and `clean-css` ([#135](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/135)) ([a9dd43e](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/a9dd43e1a7284bb713e19eac6fa3f3724fb9d48b))

### [3.1.2](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.1.1...v3.1.2) (2021-11-08)


### Bug Fixes

* handle `esbuild` warnings ([f427f41](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/f427f41d319b340262c9b5061cf5427bdf5a5f78))

### [3.1.1](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.1.0...v3.1.1) (2021-10-05)


### Bug Fixes

* minified result can be empty ([#127](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/127)) ([0a8ad71](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/0a8ad71fa0da86c5f76381f13ddf013db02a60ea))

## [3.1.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.0.2...v3.1.0) (2021-10-04)


### Features

* added `esbuild` minimizer ([#122](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/122)) ([987d454](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/987d45402af81d88fbd1489021f8d14e5ff26a74))
* allow returning errors from custom minimize function ([#121](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/121)) ([c9a11b2](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/c9a11b2349d24206bc03cf3670f50a822ef7a52b))
* output documentation links on errors ([4e8afba](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/4e8afbae5c89b0fe52b83b69fb2c5ab8a6382750))


### Bug Fixes

* source map generation for multiple `minify` functions ([b736099](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/b7360993a50a4d998bb92f0a3d491030452fcb0b))

### [3.0.2](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.0.1...v3.0.2) (2021-06-25)

### Chore

* update `serialize-javascript`

### [3.0.1](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v3.0.0...v3.0.1) (2021-05-31)

### Chore

* update `jest-worker`

## [3.0.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v2.0.0...v3.0.0) (2021-05-12)


### ⚠ BREAKING CHANGES

* minimum supported `Node.js` version is `12.13.0`

## [2.0.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v1.3.0...v2.0.0) (2021-04-10)


### ⚠ BREAKING CHANGES

* update `cssnano` to `5.0.0` version
* drop `webpack` v4 support, 
* removed the `cache` option (respect the [`cache`](https://webpack.js.org/configuration/other-options/#cache) option from `webpack`)
* removed the `cacheKeys` option respect the [`cache`](https://webpack.js.org/configuration/other-options/#cache) option from `webpack`)
* removed the sourceMap option (respect the [`devtool`](https://webpack.js.org/configuration/devtool/) option from `webpack`)

### Features

* added defaults functions for `clean-css` and `csso`, please look at [here](https://github.com/webpack-contrib/css-minimizer-webpack-plugin#minify) ([5211eed](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/5211eed3c816212a30715ebf0798393c886343d0))
* added the ability to pass an array of functions to the minify ([91f9977](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/91f9977e9fdd1447cb45bc6f5d4b56fe7c26fb04))
* update `cssnano` to `5.0.0` version ([4d2a8fd](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/4d2a8fd275ab17f6d98e4581b6c2adf09c73c91c))

## [1.3.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v1.2.0...v1.3.0) (2021-03-15)


### Features

* added support `processorOptions` for `cssnano` ([8865423](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/886542375dc37411e271d151b7060be574fc5898))

## [1.2.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v1.1.5...v1.2.0) (2021-01-08)


### Features

* optimize CSS assets added later by plugins (webpack@5 only) ([#47](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/47)) ([bdb3f52](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/bdb3f524289a2c131e2ec3728cf10b4c9a0010c7))


### Bug Fixes

* crash with source maps when the `parallel` option is `false` ([#53](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/53)) ([4fe95f9](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/4fe95f9b63764d06ffc4e6d6d77a45ba2dd1b76b))

### [1.1.5](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v1.1.4...v1.1.5) (2020-10-07)

### Chore

* update `schema-utils`

### [1.1.4](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v1.1.3...v1.1.4) (2020-09-18)


### Bug Fixes

* weak cache
* source map generation
* cache warnings between builds 

### [1.1.3](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v1.1.2...v1.1.3) (2020-09-03)


### Bug Fixes

* do not crash on the `minify` option ([fd9abac](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/fd9abac59dc82561ef1db603165526caba6e9abe))

### [1.1.2](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v1.1.1...v1.1.2) (2020-08-24)


### Bug Fixes

* compatibility with webpack 5 ([6232829](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/62328298d6a81f31e773d11ced8811ee41cd0470))

### [1.1.1](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v1.1.0...v1.1.1) (2020-08-10)


### Bug Fixes

* compatibility with `10.13` version of `Node.js` ([d38ea79](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/d38ea793bc2cefc6289d2fe8f2e5df8c31e487e0))

## [1.1.0](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/compare/v1.0.0...v1.1.0) (2020-08-04)


### Features

* show minimized assets in stats for webpack@5 ([#19](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/19)) ([cb038b9](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/cb038b91b15e934a56c260635506df4f02efd747))


### Bug Fixes

* compatibility cache feature with webpack@5 ([#16](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/16)) ([997e00f](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/997e00f66298219dccfdff8c01c71bebc973df49))
* skip double compression for child compilation ([#18](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues/18)) ([ffc71c2](https://github.com/webpack-contrib/css-minimizer-webpack-plugin/commit/ffc71c2c5269ba12c794be87c3257390fdd9c926))

## 1.0.0 - 2020-08-01

Initial release
