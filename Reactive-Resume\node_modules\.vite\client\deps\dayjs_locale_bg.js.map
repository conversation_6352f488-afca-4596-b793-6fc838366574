{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/bg.js"], "sourcesContent": ["!function(e,_){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=_(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],_):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_bg=_(e.dayjs)}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),d={name:\"bg\",weekdays:\"неделя_понеделник_вторник_сряда_четвъртък_петък_събота\".split(\"_\"),weekdaysShort:\"нед_пон_вто_сря_чет_пет_съб\".split(\"_\"),weekdaysMin:\"нд_пн_вт_ср_чт_пт_сб\".split(\"_\"),months:\"януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември\".split(\"_\"),monthsShort:\"яну_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек\".split(\"_\"),weekStart:1,ordinal:function(e){var _=e%100;if(_>10&&_<20)return e+\"-ти\";var t=e%10;return 1===t?e+\"-ви\":2===t?e+\"-ри\":7===t||8===t?e+\"-ми\":e+\"-ти\"},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"D.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY H:mm\",LLLL:\"dddd, D MMMM YYYY H:mm\"},relativeTime:{future:\"след %s\",past:\"преди %s\",s:\"няколко секунди\",m:\"минута\",mm:\"%d минути\",h:\"час\",hh:\"%d часа\",d:\"ден\",dd:\"%d дена\",M:\"месец\",MM:\"%d месеца\",y:\"година\",yy:\"%d години\"}};return t.default.locale(d,null,!0),d}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,yDAAyD,MAAM,GAAG,GAAE,eAAc,8BAA8B,MAAM,GAAG,GAAE,aAAY,uBAAuB,MAAM,GAAG,GAAE,QAAO,oFAAoF,MAAM,GAAG,GAAE,aAAY,kDAAkD,MAAM,GAAG,GAAE,WAAU,GAAE,SAAQ,SAASA,IAAE;AAAC,YAAIC,KAAED,KAAE;AAAI,YAAGC,KAAE,MAAIA,KAAE,GAAG,QAAOD,KAAE;AAAM,YAAIE,KAAEF,KAAE;AAAG,eAAO,MAAIE,KAAEF,KAAE,QAAM,MAAIE,KAAEF,KAAE,QAAM,MAAIE,MAAG,MAAIA,KAAEF,KAAE,QAAMA,KAAE;AAAA,MAAK,GAAE,SAAQ,EAAC,IAAG,QAAO,KAAI,WAAU,GAAE,aAAY,IAAG,eAAc,KAAI,oBAAmB,MAAK,yBAAwB,GAAE,cAAa,EAAC,QAAO,WAAU,MAAK,YAAW,GAAE,mBAAkB,GAAE,UAAS,IAAG,aAAY,GAAE,OAAM,IAAG,WAAU,GAAE,OAAM,IAAG,WAAU,GAAE,SAAQ,IAAG,aAAY,GAAE,UAAS,IAAG,YAAW,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e", "_", "t"]}