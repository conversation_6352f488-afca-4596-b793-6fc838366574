@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\jest@29.7.0_@types+node@22._2dfb624469a3dcec928d232541fdf74b\node_modules\jest\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\jest@29.7.0_@types+node@22._2dfb624469a3dcec928d232541fdf74b\node_modules\jest\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\jest@29.7.0_@types+node@22._2dfb624469a3dcec928d232541fdf74b\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\jest@29.7.0_@types+node@22._2dfb624469a3dcec928d232541fdf74b\node_modules\jest\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\jest@29.7.0_@types+node@22._2dfb624469a3dcec928d232541fdf74b\node_modules\jest\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\jest@29.7.0_@types+node@22._2dfb624469a3dcec928d232541fdf74b\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\jest@29.7.0_@types+node@22._2dfb624469a3dcec928d232541fdf74b\node_modules\jest\bin\jest.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\jest@29.7.0_@types+node@22._2dfb624469a3dcec928d232541fdf74b\node_modules\jest\bin\jest.js" %*
)
