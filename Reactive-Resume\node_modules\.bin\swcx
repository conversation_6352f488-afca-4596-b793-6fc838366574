#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609/node_modules/@swc/cli/bin/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609/node_modules/@swc/cli/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609/node_modules/@swc/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609/node_modules/@swc/cli/bin/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609/node_modules/@swc/cli/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609/node_modules/@swc/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609/node_modules/@swc/cli/bin/swcx.js" "$@"
else
  exec node  "$basedir/../.pnpm/@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609/node_modules/@swc/cli/bin/swcx.js" "$@"
fi
