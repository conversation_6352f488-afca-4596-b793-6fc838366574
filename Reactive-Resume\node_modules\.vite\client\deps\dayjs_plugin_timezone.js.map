{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/timezone.js"], "sourcesContent": ["!function(t,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define(e):(t=\"undefined\"!=typeof globalThis?globalThis:t||self).dayjs_plugin_timezone=e()}(this,(function(){\"use strict\";var t={year:0,month:1,day:2,hour:3,minute:4,second:5},e={};return function(n,i,o){var r,a=function(t,n,i){void 0===i&&(i={});var o=new Date(t),r=function(t,n){void 0===n&&(n={});var i=n.timeZoneName||\"short\",o=t+\"|\"+i,r=e[o];return r||(r=new Intl.DateTimeFormat(\"en-US\",{hour12:!1,timeZone:t,year:\"numeric\",month:\"2-digit\",day:\"2-digit\",hour:\"2-digit\",minute:\"2-digit\",second:\"2-digit\",timeZoneName:i}),e[o]=r),r}(n,i);return r.formatToParts(o)},u=function(e,n){for(var i=a(e,n),r=[],u=0;u<i.length;u+=1){var f=i[u],s=f.type,m=f.value,c=t[s];c>=0&&(r[c]=parseInt(m,10))}var d=r[3],l=24===d?0:d,h=r[0]+\"-\"+r[1]+\"-\"+r[2]+\" \"+l+\":\"+r[4]+\":\"+r[5]+\":000\",v=+e;return(o.utc(h).valueOf()-(v-=v%1e3))/6e4},f=i.prototype;f.tz=function(t,e){void 0===t&&(t=r);var n,i=this.utcOffset(),a=this.toDate(),u=a.toLocaleString(\"en-US\",{timeZone:t}),f=Math.round((a-new Date(u))/1e3/60),s=15*-Math.round(a.getTimezoneOffset()/15)-f;if(!Number(s))n=this.utcOffset(0,e);else if(n=o(u,{locale:this.$L}).$set(\"millisecond\",this.$ms).utcOffset(s,!0),e){var m=n.utcOffset();n=n.add(i-m,\"minute\")}return n.$x.$timezone=t,n},f.offsetName=function(t){var e=this.$x.$timezone||o.tz.guess(),n=a(this.valueOf(),e,{timeZoneName:t}).find((function(t){return\"timezonename\"===t.type.toLowerCase()}));return n&&n.value};var s=f.startOf;f.startOf=function(t,e){if(!this.$x||!this.$x.$timezone)return s.call(this,t,e);var n=o(this.format(\"YYYY-MM-DD HH:mm:ss:SSS\"),{locale:this.$L});return s.call(n,t,e).tz(this.$x.$timezone,!0)},o.tz=function(t,e,n){var i=n&&e,a=n||e||r,f=u(+o(),a);if(\"string\"!=typeof t)return o(t).tz(a);var s=function(t,e,n){var i=t-60*e*1e3,o=u(i,n);if(e===o)return[i,e];var r=u(i-=60*(o-e)*1e3,n);return o===r?[i,o]:[t-60*Math.min(o,r)*1e3,Math.max(o,r)]}(o.utc(t,i).valueOf(),f,a),m=s[0],c=s[1],d=o(m).utcOffset(c);return d.$x.$timezone=a,d},o.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},o.tz.setDefault=function(t){r=t}}}));"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,wBAAsB,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,UAAI,IAAE,EAAC,MAAK,GAAE,OAAM,GAAE,KAAI,GAAE,MAAK,GAAE,QAAO,GAAE,QAAO,EAAC,GAAE,IAAE,CAAC;AAAE,aAAO,SAAS,GAAE,GAAE,GAAE;AAAC,YAAI,GAAE,IAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,qBAASA,OAAIA,KAAE,CAAC;AAAG,cAAIC,KAAE,IAAI,KAAKH,EAAC,GAAEI,KAAE,SAASJ,IAAEC,IAAE;AAAC,uBAASA,OAAIA,KAAE,CAAC;AAAG,gBAAIC,KAAED,GAAE,gBAAc,SAAQE,KAAEH,KAAE,MAAIE,IAAEE,KAAE,EAAED,EAAC;AAAE,mBAAOC,OAAIA,KAAE,IAAI,KAAK,eAAe,SAAQ,EAAC,QAAO,OAAG,UAASJ,IAAE,MAAK,WAAU,OAAM,WAAU,KAAI,WAAU,MAAK,WAAU,QAAO,WAAU,QAAO,WAAU,cAAaE,GAAC,CAAC,GAAE,EAAEC,EAAC,IAAEC,KAAGA;AAAA,UAAC,EAAEH,IAAEC,EAAC;AAAE,iBAAOE,GAAE,cAAcD,EAAC;AAAA,QAAC,GAAE,IAAE,SAASE,IAAEJ,IAAE;AAAC,mBAAQC,KAAE,EAAEG,IAAEJ,EAAC,GAAEG,KAAE,CAAC,GAAEE,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,MAAG,GAAE;AAAC,gBAAIC,KAAEL,GAAEI,EAAC,GAAEE,KAAED,GAAE,MAAK,IAAEA,GAAE,OAAM,IAAE,EAAEC,EAAC;AAAE,iBAAG,MAAIJ,GAAE,CAAC,IAAE,SAAS,GAAE,EAAE;AAAA,UAAE;AAAC,cAAI,IAAEA,GAAE,CAAC,GAAE,IAAE,OAAK,IAAE,IAAE,GAAE,IAAEA,GAAE,CAAC,IAAE,MAAIA,GAAE,CAAC,IAAE,MAAIA,GAAE,CAAC,IAAE,MAAI,IAAE,MAAIA,GAAE,CAAC,IAAE,MAAIA,GAAE,CAAC,IAAE,QAAO,IAAE,CAACC;AAAE,kBAAO,EAAE,IAAI,CAAC,EAAE,QAAQ,KAAG,KAAG,IAAE,QAAM;AAAA,QAAG,GAAE,IAAE,EAAE;AAAU,UAAE,KAAG,SAASL,IAAEK,IAAE;AAAC,qBAASL,OAAIA,KAAE;AAAG,cAAIC,IAAEC,KAAE,KAAK,UAAU,GAAEO,KAAE,KAAK,OAAO,GAAEH,KAAEG,GAAE,eAAe,SAAQ,EAAC,UAAST,GAAC,CAAC,GAAEO,KAAE,KAAK,OAAOE,KAAE,IAAI,KAAKH,EAAC,KAAG,MAAI,EAAE,GAAEE,KAAE,KAAG,CAAC,KAAK,MAAMC,GAAE,kBAAkB,IAAE,EAAE,IAAEF;AAAE,cAAG,CAAC,OAAOC,EAAC,EAAE,CAAAP,KAAE,KAAK,UAAU,GAAEI,EAAC;AAAA,mBAAUJ,KAAE,EAAEK,IAAE,EAAC,QAAO,KAAK,GAAE,CAAC,EAAE,KAAK,eAAc,KAAK,GAAG,EAAE,UAAUE,IAAE,IAAE,GAAEH,IAAE;AAAC,gBAAI,IAAEJ,GAAE,UAAU;AAAE,YAAAA,KAAEA,GAAE,IAAIC,KAAE,GAAE,QAAQ;AAAA,UAAC;AAAC,iBAAOD,GAAE,GAAG,YAAUD,IAAEC;AAAA,QAAC,GAAE,EAAE,aAAW,SAASD,IAAE;AAAC,cAAIK,KAAE,KAAK,GAAG,aAAW,EAAE,GAAG,MAAM,GAAEJ,KAAE,EAAE,KAAK,QAAQ,GAAEI,IAAE,EAAC,cAAaL,GAAC,CAAC,EAAE,KAAM,SAASA,IAAE;AAAC,mBAAM,mBAAiBA,GAAE,KAAK,YAAY;AAAA,UAAC,CAAE;AAAE,iBAAOC,MAAGA,GAAE;AAAA,QAAK;AAAE,YAAI,IAAE,EAAE;AAAQ,UAAE,UAAQ,SAASD,IAAEK,IAAE;AAAC,cAAG,CAAC,KAAK,MAAI,CAAC,KAAK,GAAG,UAAU,QAAO,EAAE,KAAK,MAAKL,IAAEK,EAAC;AAAE,cAAIJ,KAAE,EAAE,KAAK,OAAO,yBAAyB,GAAE,EAAC,QAAO,KAAK,GAAE,CAAC;AAAE,iBAAO,EAAE,KAAKA,IAAED,IAAEK,EAAC,EAAE,GAAG,KAAK,GAAG,WAAU,IAAE;AAAA,QAAC,GAAE,EAAE,KAAG,SAASL,IAAEK,IAAEJ,IAAE;AAAC,cAAIC,KAAED,MAAGI,IAAEI,KAAER,MAAGI,MAAG,GAAEE,KAAE,EAAE,CAAC,EAAE,GAAEE,EAAC;AAAE,cAAG,YAAU,OAAOT,GAAE,QAAO,EAAEA,EAAC,EAAE,GAAGS,EAAC;AAAE,cAAID,KAAE,SAASR,IAAEK,IAAEJ,IAAE;AAAC,gBAAIC,KAAEF,KAAE,KAAGK,KAAE,KAAIF,KAAE,EAAED,IAAED,EAAC;AAAE,gBAAGI,OAAIF,GAAE,QAAM,CAACD,IAAEG,EAAC;AAAE,gBAAID,KAAE,EAAEF,MAAG,MAAIC,KAAEE,MAAG,KAAIJ,EAAC;AAAE,mBAAOE,OAAIC,KAAE,CAACF,IAAEC,EAAC,IAAE,CAACH,KAAE,KAAG,KAAK,IAAIG,IAAEC,EAAC,IAAE,KAAI,KAAK,IAAID,IAAEC,EAAC,CAAC;AAAA,UAAC,EAAE,EAAE,IAAIJ,IAAEE,EAAC,EAAE,QAAQ,GAAEK,IAAEE,EAAC,GAAE,IAAED,GAAE,CAAC,GAAE,IAAEA,GAAE,CAAC,GAAE,IAAE,EAAE,CAAC,EAAE,UAAU,CAAC;AAAE,iBAAO,EAAE,GAAG,YAAUC,IAAE;AAAA,QAAC,GAAE,EAAE,GAAG,QAAM,WAAU;AAAC,iBAAO,KAAK,eAAe,EAAE,gBAAgB,EAAE;AAAA,QAAQ,GAAE,EAAE,GAAG,aAAW,SAAST,IAAE;AAAC,cAAEA;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["t", "n", "i", "o", "r", "e", "u", "f", "s", "a"]}