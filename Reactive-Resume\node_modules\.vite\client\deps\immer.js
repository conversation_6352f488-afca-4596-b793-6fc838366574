import {
  DRAFTABLE,
  Immer2,
  NOTHING,
  applyPatches,
  castDraft,
  castImmutable,
  createDraft,
  current,
  enableMapSet,
  enablePatches,
  finishDraft,
  freeze,
  isDraft,
  isDraftable,
  original,
  produce,
  produceWithPatches,
  setAutoFreeze,
  setUseStrictShallowCopy
} from "./chunk-7KFJDXYF.js";
import "./chunk-SNAQBZPT.js";
export {
  Immer2 as Immer,
  applyPatches,
  castDraft,
  castImmutable,
  createDraft,
  current,
  enableMapSet,
  enablePatches,
  finishDraft,
  freeze,
  DRAFTABLE as immerable,
  isDraft,
  isDraftable,
  NOTHING as nothing,
  original,
  produce,
  produceWithPatches,
  setAutoFreeze,
  setUseStrictShallowCopy
};
//# sourceMappingURL=immer.js.map
