---
title: "Browser Settings"
description: "Launch or connect to an existing browser and configure it to your needs."
icon: "globe"
---

Browser Use uses [playwright](https://playwright.dev/python/docs/api/class-browsertype#browser-type-launch-persistent-context) (or [patchright](https://github.com/Kaliiiiiiiiii-Vinyzu/patchright)) to manage its connection with a real browser.

---

**To launch or connect to a browser**, pass any playwright / browser-use configuration arguments you want to `BrowserSession(...)`:

```python
from browser_use import BrowserSession, Agent

browser_session = BrowserSession(
    headless=True,
    viewport={'width': 964, 'height': 647},
    user_data_dir='~/.config/browseruse/profiles/default',
)
agent = Agent('fill out the form on this page', browser_session=browser_session)
```

<Note>
  The new `BrowserSession` & `BrowserProfile` accept all the same arguments that <PERSON><PERSON>'s [`launch_persistent_context(...)`](https://playwright.dev/python/docs/api/class-browsertype#browser-type-launch-persistent-context) takes, giving you full control over browser settings at launch. (see below for the full list)
</Note>


---

## `BrowserSession`

- 🎭 `BrowserSession(**params)` is Browser Use's object that tracks a playwright connection to a running browser. It sets up:
  - the `playwright` library, `browser` and/or `browser_context`, and `page` objects and tracks which tabs the agent & human are focused on
  - methods to interact with the browser window, apply config needed by the Agent, and run the `DOMService` for element detection
  - it can take a `browser_profile=BrowserProfile(...)` template containing some config defaults, and `**kwargs` session-specific config overrides

### Browser Connection Parameters

Provide any one of these options to connect to an existing browser. These options are session-specific and cannot be stored in a `BrowserProfile(...)` template.

#### `wss_url`

```python
wss_url: str | None = None
```

WSS URL of the playwright-protocol browser server to connect to. See here for [WSS connection instructions](https://docs.browser-use.com/customize/real-browser#method-d%3A-connect-to-remote-playwright-node-js-browser-server-via-wss-url).

#### `cdp_url`

```python
cdp_url: str | None = None
```

CDP URL of the browser to connect to (e.g. `http://localhost:9222`). See here for [CDP connection instructions](https://docs.browser-use.com/customize/real-browser#method-e%3A-connect-to-remote-browser-via-cdp-url).

#### `browser_pid`

```python
browser_pid: int | None = None
```

PID of a running chromium-based browser process to connect to on localhost. See here for [connection via pid](https://docs.browser-use.com/customize/real-browser#method-c%3A-connect-to-local-browser-using-browser-pid) instructions.

<Note>
  For web scraping tasks on sites that restrict automated access, we recommend
  using [our cloud](https://browser-use.com) or an external browser provider for better reliability. 
  See the [Connect to your Browser](real-browser) guide for detailed connection instructions.
</Note>

### Session-Specific Parameters

#### `browser_profile`

```python
browser_profile: BrowserProfile = BrowserProfile()
```

Optional `BrowserProfile` template containing default config to use for the `BrowserSession`. (see below for more info)

#### `playwright`

```python
playwright: Playwright | None = None
```

Optional playwright or patchright API client object to use, the
result of `(await async_playwright().start())` or `(await async_patchright().start())`. See here for [more detailed usage instructions](https://docs.browser-use.com/customize/real-browser#method-b%3A-connect-using-existing-playwright-objects).

#### `browser`

```python
browser: Browser | None = None
```

Playwright Browser object to use (optional). See here for [more detailed usage instructions](https://docs.browser-use.com/customize/real-browser#method-b%3A-connect-using-existing-playwright-objects).

#### `browser_context`

```python
browser_context: BrowserContext | None = None
```

Playwright BrowserContext object to use (optional). See here for [more detailed usage instructions](https://docs.browser-use.com/customize/real-browser#method-b%3A-connect-using-existing-playwright-objects).

#### `page` *aka* `agent_current_page`

<a name="page"></a><a name="agent-current-page"></a>

```python
page: Page | None = None
```

Foreground Page that the agent is focused on, can also be passed as `page=...` as a shortcut. See here for [more detailed usage instructions](https://docs.browser-use.com/customize/real-browser#method-b%3A-connect-using-existing-playwright-objects).

#### `human_current_page`

```python
human_current_page: Page | None = None
```

Foreground Page that the human is focused on to start, usually not necessary to set manually.

#### `initialized`

```python
initialized: bool = False
```

Mark BrowserSession as already initialized, skips launch/connection (not recommended)


#### `**kwargs`

`BrowserSession` can also accept *all* of the parameters [below](#browserprofile).
(the parameters *above* this point are specific to `BrowserSession` and cannot be stored in a `BrowserProfile` template)

Extra `**kwargs` passed to `BrowserSession(...)` act as session-specific overrides to the `BrowserProfile(...)` template.

```python
base_iphone13 = BrowserProfile(
    storage_state='/tmp/auth.json',     # share cookies between parallel browsers
    **playwright.devices['iPhone 13'],
    timezone_id='UTC',
)
usa_phone = BrowserSession(
    browser_profile=base_iphone13,
    timezone_id='America/New_York',     # kwargs override values in base_iphone13
)
eu_phone = BrowserSession(
    browser_profile=base_iphone13,
    timezone_id='Europe/Paris',
)

usa_agent = Agent(task='show me todays schedule...', browser_session=usa_phone)
eu_agent = Agent(task='show me todays schedule...', browser_session=eu_phone)
await asyncio.gather(agent1.run(), agent2.run())
```

---


## `BrowserProfile`

A `BrowserProfile` is a 📋 config template for a 🎭 `BrowserSession(...)`.

It's basically just a typed + validated version of a `dict` to hold config.

When you find yourself storing or re-using many browser configs, you can upgrade from:

```diff
- config = {key: val, key: val, ...}
- BrowserSession(**config)
```
To this instead:
```diff
+ config = BrowserProfile(key=val, key=val, ...)
+ BrowserSession(browser_profile=config)
```

<Tip>
You don't ever *need* to use a `BrowserProfile`, you can always pass config parameters directly to `BrowserSession`:
```python
session = BrowserSession(headless=True, storage_state='auth.json', viewport={...}, ...)
```
</Tip>

`BrowserProfile` is optional, but it provides a number of benefits over a normal `dict` for holding config:

- has type hints and pydantic field descriptions that show up in your IDE
- validates config at runtime quickly without having to start a browser
- provides helper methods to autodetect screen size, set up local paths, save/load config as json, and more...

<Tip>
`BrowserProfiles`s are designed to easily be given 🆔 `uuid`s and put in a database + made editable by users. 
`BrowserSession`s get their own 🆔 `uuid`s and be linked by 🖇 foreign key to whatever `BrowserProfiles` they use.  

This cleanly separates the per-connection rows from the bulky re-usable config and avoids wasting space in your db.
This is useful because a user may only have 2 or 3 profiles, but they could have 100k+ sessions within a few months.
</Tip>


`BrowserProfile` and `BrowserSession` can both take any of the:

- [Playwright parameters](#playwright)
- [Browser-Use parameters](#browser-use-parameters) (extra options we provide on top of `playwright`)

The only parameters `BrowserProfile` can NOT take are the session-specific connection parameters and live playwright objects:  
`cdp_url`, `wss_url`, `browser_pid`, `page`, `browser`, `browser_context`, `playwright`, etc.

### Basic Example

```python
from browser_use.browser import BrowserProfile

profile = BrowserProfile(
    stealth=True,
    storage_state='/tmp/google_docs_cookies.json',
    allowed_domains=['docs.google.com', 'https://accounts.google.com'],
    viewport={'width': 396, 'height': 774},
    # ... playwright args / browser-use config args ...
)

phone1 = BrowserSession(browser_profile=profile, device_scale_factor=1)
phone2 = BrowserSession(browser_profile=profile, device_scale_factor=2)
phone3 = BrowserSession(browser_profile=profile, device_scale_factor=3)
```

### Browser-Use Parameters

These parameters control Browser Use-specific features, and are outside the standard playwright set. They can be passed to `BrowserSession(...)` and/or stored in a `BrowserProfile` template.

#### `keep_alive`

```python
keep_alive: bool | None = None
```

If `True` it wont close the browser after the first `agent.run()` ends. Useful for running multiple tasks with the same browser instance. If this is left as `None` and the Agent launched its own browser, the default is to close the browser after the agent completes. If the agent connected to an existing browser then it will leave it open.

#### `stealth`

```python
stealth: bool = False
```
Set to `True` to use [`patchright`](https://github.com/Kaliiiiiiiiii-Vinyzu/patchright) to avoid bot-blocking. (Might cause issues with some sites, requires manual testing.)

<a name="restrict-urls"></a>

#### `allowed_domains`

```python
allowed_domains: list[str] | None = None
```

List of allowed domains for navigation. If None, all domains are allowed.
Example: `['google.com', '*.wikipedia.org']` - Here the agent will only be able to access `google.com` exactly and `wikipedia.org` + `*.wikipedia.org`.
  
Glob patterns are supported:
- `['example.com']` ✅ will match only `https://example.com/*` exactly, subdomains will not be allowed.
  It's always the most secure to list all the domains you want to give the access to explicitly w/ schemes e.g.
  `['https://google.com', 'http*://www.google.com', 'https://myaccount.google.com', 'https://mail.google.com', 'https://docs.google.com']`
- `['*.example.com']` ⚠️ **CAUTION** this will match `https://example.com` and *all* its subdomains.
  Make sure *all* the subdomains are safe for the agent! `abc.example.com`, `def.example.com`, ..., `useruploads.example.com`, `admin.example.com`

#### `disable_security`

```python
disable_security: bool = False
```

Completely disables all basic browser security features. Allows interacting across cross-site iFrames boundaries, but very INSECURE, don't visit untrusted URLs or use cookies.

#### `deterministic_rendering`

```python
deterministic_rendering: bool = False
```

Enable deterministic rendering flags for consistent screenshots.

#### `highlight_elements`

```python
highlight_elements: bool = True
```

Highlight interactive elements on the screen with colorful bounding boxes.

#### `viewport_expansion`

```python
viewport_expansion: int = 500
```

Viewport expansion in pixels. With this you can control how much of the page is included in the context of the LLM:
- `-1`: All elements from the entire page will be included, regardless of visibility (highest token usage but most complete).
- `0`: Only elements which are currently visible in the viewport will be included.
- `500` (default): Elements in the viewport plus an additional 500 pixels in each direction will be included, providing a balance between context and token usage.

#### `include_dynamic_attributes`

```python
include_dynamic_attributes: bool = True
```

Include dynamic attributes in selectors for better element targeting.

#### `minimum_wait_page_load_time`

```python
minimum_wait_page_load_time: float = 0.25
```

Minimum time to wait before capturing page state for LLM input.

#### `wait_for_network_idle_page_load_time`

```python
wait_for_network_idle_page_load_time: float = 0.5
```

Time to wait for network activity to cease. Increase to 3-5s for slower websites. This tracks essential content loading, not dynamic elements like videos.

#### `maximum_wait_page_load_time`

```python
maximum_wait_page_load_time: float = 5.0
```

Maximum time to wait for page load before proceeding.

#### `wait_between_actions`

```python
wait_between_actions: float = 0.5
```

Time to wait between agent actions.

#### `cookies_file`

```python
cookies_file: str | None = None
```

JSON file path to save cookies to.

<Warning>
This option is DEPRECATED. Use [`storage_state`](#storage-state) instead, it's the standard playwright format and also supports `localStorage`!

Transfer any cookies from your existing `cookies_file` to the new JSON format:

`cookies_file.json`: `[{cookie}, {cookie}, {cookie}]`  
⬇️
`storage_state.json`: `{"cookies": [{cookie}, {cookie}, {cookie}], "origins": {... optional localstorage state ...}}`

Or run `playwright open https://example.com/ --save-storage=storage_state.json` and log into any sites you need.
</Warning>


#### `profile_directory`

```python
profile_directory: str = 'Default'
```

Chrome profile subdirectory name inside of your `user_data_dir` (e.g. `Default`, `Profile 1`, `Work`, etc.).

#### `window_position`

```python
window_position: dict | None = {"width": 0, "height": 0}
```

Window position from top-left corner.

#### `save_recording_path`

```python
save_recording_path: str | None = None
```

Directory path for saving video recordings.

#### `trace_path`

```python
trace_path: str | None = None
```

Directory path for saving Agent trace files. Files are automatically named as `{trace_path}/{context_id}.zip`.


---

<a name="playwright-parameters"></a><a name="playwright"></a>

### Playwright Launch Options


All the parameters below are standard playwright parameters and can be passed to both `BrowserSession` and `BrowserProfile`.
They are defined in `browser_use/browser/profile.py`. See here for the [official Playwright documentation](https://playwright.dev/python/docs/api/class-browsertype#browser-type-launch-persistent-context) for all of these options.

#### `headless`

```python
headless: bool | None = None
```

Runs the browser without a visible UI. If None, auto-detects based on display availability.

#### `channel`

```python
channel: BrowserChannel = 'chromium'
```

Browser channel: `'chromium'`, `'chrome'`, `'chrome-beta'`, `'chrome-dev'`, `'chrome-canary'`, `'msedge'`, `'msedge-beta'`, `'msedge-dev'`, `'msedge-canary'`

Don't worry, other chromium-based browsers not in this list (e.g. `brave`) are still supported if you provide your own [`executable_path`](#executable_path).

#### `executable_path`

```python
executable_path: str | Path | None = None
```

Path to browser executable for custom installations.

#### `user_data_dir`

```python
user_data_dir: str | Path | None = '~/.config/browseruse/profiles/default'
```

Directory for browser profile data. Set to `None` to use an ephemeral temporary profile (aka incognito mode).

Multiple running browsers **cannot share a single `user_data_dir` at the same time**. You must set it to `None` or
provide a unique `user_data_dir` per-session if you plan to run multiple browsers.

The browser version run must always be equal to or greater than the version used to create the `user_data_dir`.
If you see errors like `Failed to parse Extensions` or similar and failures when launching, you're attempting to run an older browser with an incompatible `user_data_dir` that's already been migrated to a newer schema version.

#### `args`

```python
args: list[str] = []
```
Additional command-line arguments to pass to the browser. See here for the [full list of available chrome launch options](https://peter.sh/experiments/chromium-command-line-switches/).



#### `ignore_default_args`

```python
ignore_default_args: list[str] | bool = ['--enable-automation', '--disable-extensions']
```

List of default CLI args to stop playwright from including when launching chrome. Set it to `True` to disable *all* default options (not recommended).

#### `env`

```python
env: dict[str, str] = {}
```

Extra environment variables to set when launching browser. e.g. `{'DISPLAY': '1'}` to use a specific X11 display.

#### `chromium_sandbox`

```python
chromium_sandbox: bool = not IN_DOCKER
```

Whether to enable Chromium sandboxing (recommended for security). Should always be `False` when running inside Docker
because Docker provides its own sandboxing can conflict with Chrome's.

#### `devtools`

```python
devtools: bool = False
```

Whether to open DevTools panel automatically (only works when `headless=False`).

#### `slow_mo`

```python
slow_mo: float = 0
```

Slow down actions by this many milliseconds.

#### `timeout`

```python
timeout: float = 30000
```

Default timeout in milliseconds for connecting to a remote browser.

#### `accept_downloads`

```python
accept_downloads: bool = True
```

Whether to automatically accept all downloads.

#### `proxy`

```python
proxy: dict | None = None
```

Proxy settings. Example: `{"server": "http://proxy.com:8080", "username": "user", "password": "pass"}`. 

#### `permissions`

```python
permissions: list[str] = ['clipboard-read', 'clipboard-write', 'notifications']
```

Browser permissions to grant. See here for the [full list of available permission](https://playwright.dev/python/docs/api/class-browsercontext#browser-context-grant-permissions).

#### `storage_state`

```python
storage_state: str | Path | dict | None = None
```

Browser storage state (cookies, localStorage). Can be file path or dict. See here for the [Playwright `storage_state` documentation](https://playwright.dev/python/docs/api/class-browsercontext#browser-context-storage-state) on how to use it.
This option is only applied when launching a new browser using the default builtin playwright chromium and `user_data_dir=None` is set.

```bash
# to create a storage state file, run the following and log into the sites you need once the browser opens:
playwright open https://example.com/ --save-storage=./storage_state.json
# then setup a BrowserSession with storage_state='./storage_state.json' and user_data_dir=None to use it
```

### Playwright Timing Settings

These control how the browser waits for CDP API calls to complete and pages to load.

#### `default_timeout`

```python
default_timeout: float | None = None
```

Default timeout for Playwright operations in milliseconds.

#### `default_navigation_timeout`

```python
default_navigation_timeout: float | None = None
```

Default timeout for page navigation in milliseconds.


### Playwright Viewport Options

Configure browser window size, viewport, and display properties:

#### `user_agent`

```python
user_agent: str | None = None
```

Specific user agent to use in this context.

#### `is_mobile`

```python
is_mobile: bool = False
```

Whether the meta viewport tag is taken into account and touch events are enabled.

#### `has_touch`

```python
has_touch: bool = False
```

Specifies if viewport supports touch events.

#### `geolocation`

```python
geolocation: dict | None = None
```

Geolocation coordinates. Example: `{"latitude": 59.95, "longitude": 30.31667}`

#### `locale`

```python
locale: str | None = None
```

Specify user locale, for example en-GB, de-DE, etc. Locale will affect the navigator.language value, Accept-Language request header value as well as number and date formatting rules.

#### `timezone_id`

```python
timezone_id: str | None = None
```

Timezone identifier (e.g., 'America/New_York').

#### `window_size`

```python
window_size: dict | None = None
```

Browser window size for headful mode. Example: `{"width": 1920, "height": 1080}`

#### `viewport`

```python
viewport: dict | None = None
```

Viewport size with `width` and `height`. Example: `{"width": 1280, "height": 720}`

#### `no_viewport`

```python
no_viewport: bool | None = not headless
```

Disable fixed viewport. Content will resize with window.

*Tip:* don't use this parameter, it's a playwright standard parameter but it's redundant and only serves to override the `viewport` setting above.
A viewport is *always* used in headless mode regardless of this setting, and is *never* used in headful mode unless you pass `viewport={width, height}` explicitly.

#### `device_scale_factor`

```python
device_scale_factor: float | None = None
```

Device scale factor (DPI). Useful for high-resolution screenshots (set it to 2).

#### `screen`

```python
screen: dict | None = None
```

Screen size available to browser. Auto-detected if not specified.

#### `color_scheme`

```python
color_scheme: ColorScheme = 'light'
```

Preferred color scheme: `'light'`, `'dark'`, `'no-preference'`

#### `contrast`

```python
contrast: Contrast = 'no-preference'
```

Contrast preference: `'no-preference'`, `'more'`, `'null'`

#### `reduced_motion`

```python
reduced_motion: ReducedMotion = 'no-preference'
```

Reduced motion preference: `'reduce'`, `'no-preference'`, `'null'`

#### `forced_colors`

```python
forced_colors: ForcedColors = 'none'
```

Forced colors mode: `'active'`, `'none'`, `'null'`

#### `**playwright.devices[...]`

Playwright provides launch & context arg presets to [emulate common device fingerprints](https://playwright.dev/python/docs/emulation).

```python
BrowserProfile(
    ...
    **playwright.devices['iPhone 13'],    # playwright = await async_playwright().start()
)
```

Because `BrowserSession` and `BrowserProfile` take all the standard playwright args, we are able to support these device presets as well.

### Playwright Security Options

> See `allowed_domains` above too!

#### `offline`

```python
offline: bool = False
```

Emulate network being offline.

#### `http_credentials`

```python
http_credentials: dict | None = None
```

Credentials for HTTP authentication.

#### `extra_http_headers`

```python
extra_http_headers: dict[str, str] = {}
```

Additional HTTP headers to be sent with every request.

#### `ignore_https_errors`

```python
ignore_https_errors: bool = False
```

Whether to ignore HTTPS errors when sending network requests.

#### `bypass_csp`

```python
bypass_csp: bool = False
```

Toggles bypassing Content-Security-Policy.

#### `java_script_enabled`

```python
java_script_enabled: bool = True
```

Whether or not to enable JavaScript in the context.

#### `service_workers`

```python
service_workers: ServiceWorkers = 'allow'
```

Whether to allow sites to register Service workers: `'allow'`, `'block'`

#### `base_url`

```python
base_url: str | None = None
```

Base URL to be used in `page.goto()` and similar operations.

#### `strict_selectors`

```python
strict_selectors: bool = False
```

If true, selector passed to Playwright methods will throw if more than one element matches.

#### `client_certificates`

```python
client_certificates: list[ClientCertificate] = []
```

Client certificates to be used with requests.


### Playwright Recording Options

Note: Browser Use also provides some of our own recording-related options not listed below (see above).

#### `record_video_dir`

```python
record_video_dir: str | Path | None = None
```

Directory to save video recordings. [Playwright Docs: `record_video_dir`](https://playwright.dev/python/docs/api/class-browsertype#browser-type-launch-persistent-context-option-record-video-dir)

#### `record_video_size`

```python
record_video_size: dict | None = None. [Playwright Docs: `record_video_size`](https://playwright.dev/python/docs/api/class-browsertype#browser-type-launch-persistent-context-option-record-video-size)

```

Video size. Example: `{"width": 1280, "height": 720}`

#### `record_har_path`

```python
record_har_path: str | Path | None = None
```

Path to save HAR file with network activity.

#### `record_har_content`

```python
record_har_content: RecordHarContent = 'embed'
```

How to persist HAR content: `'omit'`, `'embed'`, `'attach'`

#### `record_har_mode`

```python
record_har_mode: RecordHarMode = 'full'
```

HAR recording mode: `'full'`, `'minimal'`

#### `record_har_omit_content`

```python
record_har_omit_content: bool = False
```

Whether to omit request content from the HAR.

#### `record_har_url_filter`

```python
record_har_url_filter: str | Pattern | None = None
```

URL filter for HAR recording.

#### `downloads_path`

```python
downloads_path: str | Path | None = '~/.config/browseruse/downloads'
```

(aliases: `downloads_dir`, `save_downloads_path`)

Local filesystem directory to save downloads to.

#### `traces_dir`

```python
traces_dir: str | Path | None = None
```

Directory to save HAR trace files to.

#### `handle_sighup`

```python
handle_sighup: bool = True
```

Whether playwright should swallow SIGHUP signals and kill the browser.

#### `handle_sigint`

```python
handle_sigint: bool = False
```

Whether playwright should swallow SIGINT signals and kill the browser.

#### `handle_sigterm`

```python
handle_sigterm: bool = False
```

Whether playwright should swallow SIGTERM signals and kill the browser.

---

## Full Example

```python
from browser_use import BrowserSession, BrowserProfile, Agent

browser_profile = BrowserProfile(
    headless=False,
    storage_state="path/to/storage_state.json",
    wait_for_network_idle_page_load_time=3.0,
    viewport={"width": 1280, "height": 1100},
    locale='en-US',
    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36',
    highlight_elements=True,
    viewport_expansion=500,
    allowed_domains=['*.google.com', 'http*://*.wikipedia.org'],
    user_data_dir=None,
)

browser_session = BrowserSession(
    browser_profile=browser_profile,
    headless=True,                          # extra kwargs to the session override the defaults in the profile
)

# you can drive a session without the agent / reuse it between agents
await browser_session.start()
page = await browser_session.get_current_page()
await page.goto('https://example.com/first/page')

async def run_search():
    agent = Agent(
        task='Your task',
        llm=llm,
        page=page,                        # optional: pass a specific playwright page to start on
        browser_session=browser_session,  # optional: pass an existing browser session to an agent
    )
```

---

## Summary

- **BrowserSession** (defined in `browser_use/browser/session.py`) handles the live browser connection and runtime state
- **BrowserProfile** (defined in `browser_use/browser/profile.py`) is a template that can store default config parameters for a `BrowserSession(...)`

Configuration parameters defined in both scopes consumed by these calls depending on whether we're connecting/launching:

- `BrowserConnectArgs` - args for `playwright.BrowserType.connect_over_cdp(...)`
- `BrowserLaunchArgs` - args for `playwright.BrowserType.launch(...)`
- `BrowserNewContextArgs` - args for `playwright.BrowserType.new_context(...)`
- `BrowserLaunchPersistentContextArgs` - args for `playwright.BrowserType.launch_persistent_context(...)`
- Browser Use's own internal methods

For more details on Playwright's browser context options, see their [launch args documentation](https://playwright.dev/python/docs/api/class-browsertype#browser-type-launch-persistent-context).

---
