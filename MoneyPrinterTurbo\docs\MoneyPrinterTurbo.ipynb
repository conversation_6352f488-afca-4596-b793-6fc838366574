{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MoneyPrinterTurbo Setup Guide\n", "\n", "This notebook will guide you through the process of setting up [MoneyPrinterTurbo](https://github.com/harry0703/MoneyPrinterTurbo)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Clone Repository and Install Dependencies\n", "\n", "First, we'll clone the repository from GitHub and install all required packages:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "S8Eu-aQarY_B"}, "outputs": [], "source": ["!git clone https://github.com/harry0703/MoneyPrinterTurbo.git\n", "%cd MoneyPrinterTurbo\n", "!pip install -q -r requirements.txt\n", "!pip install pyngrok --quiet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configure <PERSON><PERSON> for Remote Access\n", "\n", "We'll use ngrok to create a secure tunnel to expose our local Streamlit server to the internet.\n", "\n", "**Important**: You need to get your authentication token from the [ngrok dashboard](https://dashboard.ngrok.com/get-started/your-authtoken) to use this service."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyngrok import ngrok\n", "\n", "# Terminate any existing ngrok tunnels\n", "ngrok.kill()\n", "\n", "# Set your authentication token\n", "# Replace \"your_ngrok_auth_token\" with your actual token\n", "ngrok.set_auth_token(\"your_ngrok_auth_token\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Launch Application and Generate Public URL\n", "\n", "Now we'll start the Streamlit server and create an ngrok tunnel to make it accessible online:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "oahsIOXmwjl9", "outputId": "ee23a96c-af21-4207-deb7-9fab69e0c05e"}, "outputs": [], "source": ["import subprocess\n", "import time\n", "\n", "print(\"🚀 Starting MoneyPrinterTurbo...\")\n", "# Start Streamlit server on port 8501\n", "streamlit_proc = subprocess.Popen([\n", "    \"streamlit\", \"run\", \"./webui/Main.py\", \"--server.port=8501\"\n", "])\n", "\n", "# Wait for the server to initialize\n", "time.sleep(5)\n", "\n", "print(\"🌐 Creating ngrok tunnel to expose the MoneyPrinterTurbo...\")\n", "public_url = ngrok.connect(8501, bind_tls=True)\n", "\n", "print(\"✅ Deployment complete! Access your MoneyPrinterTurbo at:\")\n", "print(public_url)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}