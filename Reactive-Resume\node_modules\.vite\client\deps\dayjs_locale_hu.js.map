{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/hu.js"], "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_hu=n(e.dayjs)}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=n(e),r={name:\"hu\",weekdays:\"vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat\".split(\"_\"),weekdaysShort:\"vas_hét_kedd_sze_csüt_pén_szo\".split(\"_\"),weekdaysMin:\"v_h_k_sze_cs_p_szo\".split(\"_\"),months:\"janu<PERSON>r_febru<PERSON>r_márc<PERSON>_április_május_június_július_augusztus_szeptember_október_november_december\".split(\"_\"),monthsShort:\"jan_feb_márc_ápr_máj_jún_júl_aug_szept_okt_nov_dec\".split(\"_\"),ordinal:function(e){return e+\".\"},weekStart:1,relativeTime:{future:\"%s múlva\",past:\"%s\",s:function(e,n,t,r){return\"néhány másodperc\"+(r||n?\"\":\"e\")},m:function(e,n,t,r){return\"egy perc\"+(r||n?\"\":\"e\")},mm:function(e,n,t,r){return e+\" perc\"+(r||n?\"\":\"e\")},h:function(e,n,t,r){return\"egy \"+(r||n?\"óra\":\"órája\")},hh:function(e,n,t,r){return e+\" \"+(r||n?\"óra\":\"órája\")},d:function(e,n,t,r){return\"egy \"+(r||n?\"nap\":\"napja\")},dd:function(e,n,t,r){return e+\" \"+(r||n?\"nap\":\"napja\")},M:function(e,n,t,r){return\"egy \"+(r||n?\"hónap\":\"hónapja\")},MM:function(e,n,t,r){return e+\" \"+(r||n?\"hónap\":\"hónapja\")},y:function(e,n,t,r){return\"egy \"+(r||n?\"év\":\"éve\")},yy:function(e,n,t,r){return e+\" \"+(r||n?\"év\":\"éve\")}},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"YYYY.MM.DD.\",LL:\"YYYY. MMMM D.\",LLL:\"YYYY. MMMM D. H:mm\",LLLL:\"YYYY. MMMM D., dddd H:mm\"}};return t.default.locale(r,null,!0),r}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,sDAAsD,MAAM,GAAG,GAAE,eAAc,gCAAgC,MAAM,GAAG,GAAE,aAAY,qBAAqB,MAAM,GAAG,GAAE,QAAO,oGAAoG,MAAM,GAAG,GAAE,aAAY,qDAAqD,MAAM,GAAG,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA,KAAE;AAAA,MAAG,GAAE,WAAU,GAAE,cAAa,EAAC,QAAO,YAAW,MAAK,MAAK,GAAE,SAASA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAM,sBAAoBA,MAAGF,KAAE,KAAG;AAAA,MAAI,GAAE,GAAE,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAM,cAAYA,MAAGF,KAAE,KAAG;AAAA,MAAI,GAAE,IAAG,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAOH,KAAE,WAASG,MAAGF,KAAE,KAAG;AAAA,MAAI,GAAE,GAAE,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAM,UAAQA,MAAGF,KAAE,QAAM;AAAA,MAAQ,GAAE,IAAG,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAOH,KAAE,OAAKG,MAAGF,KAAE,QAAM;AAAA,MAAQ,GAAE,GAAE,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAM,UAAQA,MAAGF,KAAE,QAAM;AAAA,MAAQ,GAAE,IAAG,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAOH,KAAE,OAAKG,MAAGF,KAAE,QAAM;AAAA,MAAQ,GAAE,GAAE,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAM,UAAQA,MAAGF,KAAE,UAAQ;AAAA,MAAU,GAAE,IAAG,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAOH,KAAE,OAAKG,MAAGF,KAAE,UAAQ;AAAA,MAAU,GAAE,GAAE,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAM,UAAQA,MAAGF,KAAE,OAAK;AAAA,MAAM,GAAE,IAAG,SAASD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,eAAOH,KAAE,OAAKG,MAAGF,KAAE,OAAK;AAAA,MAAM,EAAC,GAAE,SAAQ,EAAC,IAAG,QAAO,KAAI,WAAU,GAAE,eAAc,IAAG,iBAAgB,KAAI,sBAAqB,MAAK,2BAA0B,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e", "n", "t", "r"]}