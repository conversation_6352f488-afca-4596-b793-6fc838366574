#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66/node_modules/nx/bin/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66/node_modules/nx/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66/node_modules/nx/bin/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66/node_modules/nx/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66/node_modules/nx/bin/nx-cloud.js" "$@"
else
  exec node  "$basedir/../.pnpm/nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66/node_modules/nx/bin/nx-cloud.js" "$@"
fi
