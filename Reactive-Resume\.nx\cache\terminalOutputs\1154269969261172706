C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\schema-utils@3.3.0\node_modules\schema-utils\dist\validate.js:191
    throw new _ValidationError.default(errors, schema, configuration);
          ^

ValidationError: Invalid configuration object. Webpack has been initialized using a configuration object that does not match the API schema.
 - configuration.externals should be one of these:
   [RegExp | string | object { byLayer?, <key>: [non-empty string, ...] | boolean | string | object { … } } | function, ...] | RegExp | string | object { byLayer?, <key>: [non-empty string, ...] | boolean | string | object { … } } | function
   -> Specify dependencies that shouldn't be resolved by webpack, but should become dependencies of the resulting bundle. The kind of the dependency depends on `output.libraryTarget`.
   Details:
    * configuration.externals['0'] should be one of these:
      [non-empty string, ...] | boolean | string | object { … }
      -> The dependency used for the external.
      Details:
       * configuration.externals['0'] should be an array:
         [non-empty string, ...]
       * configuration.externals['0'] should be a boolean.
         -> `true`: The dependency name is used as target of the external.
       * configuration.externals['0'] should be a string.
         -> The target of the external.
       * configuration.externals['0'] should be an object:
         object { … }
    at validate [90m(C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\[39mnode_modules\[4m.pnpm[24m\schema-utils@3.3.0\node_modules\[4mschema-utils[24m\dist\validate.js:191:11[90m)[39m
    at validateSchema [90m(C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\[39mnode_modules\[4m.pnpm[24m\webpack@5.97.1_@swc+core@1.10.12_@swc+helpers@0.5.15_\node_modules\[4mwebpack[24m\lib\validateSchema.js:78:2[90m)[39m
    at create [90m(C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\[39mnode_modules\[4m.pnpm[24m\webpack@5.97.1_@swc+core@1.10.12_@swc+helpers@0.5.15_\node_modules\[4mwebpack[24m\lib\webpack.js:130:24[90m)[39m
    at webpack [90m(C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\[39mnode_modules\[4m.pnpm[24m\webpack@5.97.1_@swc+core@1.10.12_@swc+helpers@0.5.15_\node_modules\[4mwebpack[24m\lib\webpack.js:182:32[90m)[39m
    at f [90m(C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\[39mnode_modules\[4m.pnpm[24m\webpack@5.97.1_@swc+core@1.10.12_@swc+helpers@0.5.15_\node_modules\[4mwebpack[24m\lib\index.js:78:21[90m)[39m
    at Observable._subscribe [90m(C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\[39mnode_modules\[4m.pnpm[24m\@nx+webpack@19.8.14_@babel+_5183b3ef5a6987e34bed3067d1a4cd80\node_modules\[4m@nx[24m\webpack\src\executors\webpack\lib\run-webpack.js:12:33[90m)[39m
    at Observable._trySubscribe [90m(C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\[39mnode_modules\[4m.pnpm[24m\rxjs@7.8.1\node_modules\[4mrxjs[24m\dist\cjs\internal\Observable.js:41:25[90m)[39m
    at [90mC:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\[39mnode_modules\[4m.pnpm[24m\rxjs@7.8.1\node_modules\[4mrxjs[24m\dist\cjs\internal\Observable.js:35:31
    at Object.errorContext [90m(C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\[39mnode_modules\[4m.pnpm[24m\rxjs@7.8.1\node_modules\[4mrxjs[24m\dist\cjs\internal\util\errorContext.js:22:9[90m)[39m
    at Observable.subscribe [90m(C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\[39mnode_modules\[4m.pnpm[24m\rxjs@7.8.1\node_modules\[4mrxjs[24m\dist\cjs\internal\Observable.js:26:24[90m)[39m {
  errors: [
    {
      keyword: [32m'anyOf'[39m,
      dataPath: [32m'.externals'[39m,
      schemaPath: [32m'#/anyOf'[39m,
      params: {},
      message: [32m'should match some schema in anyOf'[39m,
      schema: [
        {
          type: [32m'array'[39m,
          items: { [32m'$ref'[39m: [32m'#/definitions/ExternalItem'[39m }
        },
        { [32m'$ref'[39m: [32m'#/definitions/ExternalItem'[39m }
      ],
      parentSchema: {
        description: [32m"Specify dependencies that shouldn't be resolved by webpack, but should become dependencies of the resulting bundle. The kind of the dependency depends on `output.libraryTarget`."[39m,
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m },
          { [32m'$ref'[39m: [32m'#/definitions/ExternalItem'[39m }
        ]
      },
      data: {
        [32m'0'[39m: [36m[Function (anonymous)][39m,
        [32m'@sindresorhus/slugify'[39m: [32m'commonjs @sindresorhus/slugify'[39m
      },
      children: [
        {
          keyword: [32m'type'[39m,
          dataPath: [32m'.externals'[39m,
          schemaPath: [32m'#/anyOf/0/type'[39m,
          params: { type: [32m'array'[39m },
          message: [32m'should be array'[39m,
          schema: [32m'array'[39m,
          parentSchema: { type: [32m'array'[39m, items: [36m[Object][39m },
          data: {
            [32m'0'[39m: [36m[Function (anonymous)][39m,
            [32m'@sindresorhus/slugify'[39m: [32m'commonjs @sindresorhus/slugify'[39m
          },
          children: [90mundefined[39m
        },
        {
          keyword: [32m'instanceof'[39m,
          dataPath: [32m'.externals'[39m,
          schemaPath: [32m'#/anyOf/0/instanceof'[39m,
          params: { keyword: [32m'instanceof'[39m },
          message: [32m'should pass "instanceof" keyword validation'[39m,
          schema: [32m'RegExp'[39m,
          parentSchema: {
            description: [32m'Every matched dependency becomes external.'[39m,
            instanceof: [32m'RegExp'[39m,
            tsType: [32m'RegExp'[39m
          },
          data: {
            [32m'0'[39m: [36m[Function (anonymous)][39m,
            [32m'@sindresorhus/slugify'[39m: [32m'commonjs @sindresorhus/slugify'[39m
          },
          children: [90mundefined[39m
        },
        {
          keyword: [32m'type'[39m,
          dataPath: [32m'.externals'[39m,
          schemaPath: [32m'#/anyOf/1/type'[39m,
          params: { type: [32m'string'[39m },
          message: [32m'should be string'[39m,
          schema: [32m'string'[39m,
          parentSchema: {
            description: [32m'An exact matched dependency becomes external. The same string is used as external dependency.'[39m,
            type: [32m'string'[39m
          },
          data: {
            [32m'0'[39m: [36m[Function (anonymous)][39m,
            [32m'@sindresorhus/slugify'[39m: [32m'commonjs @sindresorhus/slugify'[39m
          },
          children: [90mundefined[39m
        },
        {
          keyword: [32m'type'[39m,
          dataPath: [32m".externals['0']"[39m,
          schemaPath: [32m'#/definitions/ExternalItemValue/anyOf/0/type'[39m,
          params: { type: [32m'array'[39m },
          message: [32m'should be array'[39m,
          schema: [32m'array'[39m,
          parentSchema: { type: [32m'array'[39m, items: [36m[Object][39m },
          data: [36m[Function (anonymous)][39m,
          children: [90mundefined[39m
        },
        {
          keyword: [32m'type'[39m,
          dataPath: [32m".externals['0']"[39m,
          schemaPath: [32m'#/definitions/ExternalItemValue/anyOf/1/type'[39m,
          params: { type: [32m'boolean'[39m },
          message: [32m'should be boolean'[39m,
          schema: [32m'boolean'[39m,
          parentSchema: {
            description: [32m'`true`: The dependency name is used as target of the external.'[39m,
            type: [32m'boolean'[39m
          },
          data: [36m[Function (anonymous)][39m,
          children: [90mundefined[39m
        },
        {
          keyword: [32m'type'[39m,
          dataPath: [32m".externals['0']"[39m,
          schemaPath: [32m'#/definitions/ExternalItemValue/anyOf/2/type'[39m,
          params: { type: [32m'string'[39m },
          message: [32m'should be string'[39m,
          schema: [32m'string'[39m,
          parentSchema: {
            description: [32m'The target of the external.'[39m,
            type: [32m'string'[39m
          },
          data: [36m[Function (anonymous)][39m,
          children: [90mundefined[39m
        },
        {
          keyword: [32m'type'[39m,
          dataPath: [32m".externals['0']"[39m,
          schemaPath: [32m'#/definitions/ExternalItemValue/anyOf/3/type'[39m,
          params: { type: [32m'object'[39m },
          message: [32m'should be object'[39m,
          schema: [32m'object'[39m,
          parentSchema: { type: [32m'object'[39m },
          data: [36m[Function (anonymous)][39m,
          children: [90mundefined[39m
        },
        {
          keyword: [32m'anyOf'[39m,
          dataPath: [32m".externals['0']"[39m,
          schemaPath: [32m'#/definitions/ExternalItemValue/anyOf'[39m,
          params: {},
          message: [32m'should match some schema in anyOf'[39m,
          schema: [ [36m[Object][39m, [36m[Object][39m, [36m[Object][39m, [36m[Object][39m ],
          parentSchema: {
            description: [32m'The dependency used for the external.'[39m,
            anyOf: [36m[Array][39m
          },
          data: [36m[Function (anonymous)][39m,
          children: [90mundefined[39m
        },
        {
          keyword: [32m'instanceof'[39m,
          dataPath: [32m'.externals'[39m,
          schemaPath: [32m'#/anyOf/3/instanceof'[39m,
          params: { keyword: [32m'instanceof'[39m },
          message: [32m'should pass "instanceof" keyword validation'[39m,
          schema: [32m'Function'[39m,
          parentSchema: {
            description: [32m'The function is called on each dependency (`function(context, request, callback(err, result))`).'[39m,
            instanceof: [32m'Function'[39m,
            tsType: [32m'(((data: ExternalItemFunctionData, callback: (err?: (Error | null), result?: ExternalItemValue) => void) => void) | ((data: ExternalItemFunctionData) => Promise<ExternalItemValue>))'[39m
          },
          data: {
            [32m'0'[39m: [36m[Function (anonymous)][39m,
            [32m'@sindresorhus/slugify'[39m: [32m'commonjs @sindresorhus/slugify'[39m
          },
          children: [90mundefined[39m
        },
        {
          keyword: [32m'anyOf'[39m,
          dataPath: [32m'.externals'[39m,
          schemaPath: [32m'#/anyOf'[39m,
          params: {},
          message: [32m'should match some schema in anyOf'[39m,
          schema: [ [36m[Object][39m, [36m[Object][39m, [36m[Object][39m, [36m[Object][39m ],
          parentSchema: {
            description: [32m"Specify dependency that shouldn't be resolved by webpack, but should become dependencies of the resulting bundle. The kind of the dependency depends on `output.libraryTarget`."[39m,
            anyOf: [36m[Array][39m
          },
          data: {
            [32m'0'[39m: [36m[Function (anonymous)][39m,
            [32m'@sindresorhus/slugify'[39m: [32m'commonjs @sindresorhus/slugify'[39m
          },
          children: [90mundefined[39m
        }
      ]
    }
  ],
  schema: {
    definitions: {
      Amd: {
        description: [32m'Set the value of `require.amd` and `define.amd`. Or disable AMD support.'[39m,
        anyOf: [
          {
            description: [32m'You can pass `false` to disable AMD support.'[39m,
            enum: [36m[Array][39m
          },
          {
            description: [32m'You can pass an object to set the value of `require.amd` and `define.amd`.'[39m,
            type: [32m'object'[39m
          }
        ]
      },
      AmdContainer: {
        description: [32m'Add a container for define/require functions in the AMD module.'[39m,
        type: [32m'string'[39m,
        minLength: [33m1[39m
      },
      AssetFilterItemTypes: {
        description: [32m'Filtering value, regexp or function.'[39m,
        cli: { helper: [33mtrue[39m },
        anyOf: [
          { instanceof: [32m'RegExp'[39m, tsType: [32m'RegExp'[39m },
          { type: [32m'string'[39m, absolutePath: [33mfalse[39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m"((name: string, asset: import('../lib/stats/DefaultStatsFactoryPlugin').StatsAsset) => boolean)"[39m
          }
        ]
      },
      AssetFilterTypes: {
        description: [32m'Filtering modules.'[39m,
        cli: { helper: [33mtrue[39m },
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m },
          { [32m'$ref'[39m: [32m'#/definitions/AssetFilterItemTypes'[39m }
        ]
      },
      AssetGeneratorDataUrl: {
        description: [32m'The options for data url generator.'[39m,
        anyOf: [
          { [32m'$ref'[39m: [32m'#/definitions/AssetGeneratorDataUrlOptions'[39m },
          { [32m'$ref'[39m: [32m'#/definitions/AssetGeneratorDataUrlFunction'[39m }
        ]
      },
      AssetGeneratorDataUrlFunction: {
        description: [32m"Function that executes for module and should return an DataUrl string. It can have a string as 'ident' property which contributes to the module hash."[39m,
        instanceof: [32m'Function'[39m,
        tsType: [32m"((source: string | Buffer, context: { filename: string, module: import('../lib/Module') }) => string)"[39m
      },
      AssetGeneratorDataUrlOptions: {
        description: [32m'Options object for data url generation.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          encoding: {
            description: [32m'Asset encoding (defaults to base64).'[39m,
            enum: [36m[Array][39m
          },
          mimetype: {
            description: [32m'Asset mimetype (getting from file extension by default).'[39m,
            type: [32m'string'[39m
          }
        }
      },
      AssetGeneratorOptions: {
        description: [32m'Generator options for asset modules.'[39m,
        type: [32m'object'[39m,
        implements: [
          [32m'#/definitions/AssetInlineGeneratorOptions'[39m,
          [32m'#/definitions/AssetResourceGeneratorOptions'[39m
        ],
        additionalProperties: [33mfalse[39m,
        properties: {
          binary: {
            description: [32m"Whether or not this asset module should be considered binary. This can be set to 'false' to treat this asset module as text."[39m,
            type: [32m'boolean'[39m
          },
          dataUrl: { [32m'$ref'[39m: [32m'#/definitions/AssetGeneratorDataUrl'[39m },
          emit: {
            description: [32m"Emit an output asset from this asset module. This can be set to 'false' to omit emitting e. g. for SSR."[39m,
            type: [32m'boolean'[39m
          },
          filename: { [32m'$ref'[39m: [32m'#/definitions/FilenameTemplate'[39m },
          outputPath: { [32m'$ref'[39m: [32m'#/definitions/AssetModuleOutputPath'[39m },
          publicPath: { [32m'$ref'[39m: [32m'#/definitions/RawPublicPath'[39m }
        }
      },
      AssetInlineGeneratorOptions: {
        description: [32m'Generator options for asset/inline modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          binary: {
            description: [32m"Whether or not this asset module should be considered binary. This can be set to 'false' to treat this asset module as text."[39m,
            type: [32m'boolean'[39m
          },
          dataUrl: { [32m'$ref'[39m: [32m'#/definitions/AssetGeneratorDataUrl'[39m }
        }
      },
      AssetModuleFilename: {
        description: [32m"The filename of asset modules as relative path inside the 'output.path' directory."[39m,
        anyOf: [
          { type: [32m'string'[39m, absolutePath: [33mfalse[39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m'((pathData: import("../lib/Compilation").PathData, assetInfo?: import("../lib/Compilation").AssetInfo) => string)'[39m
          }
        ]
      },
      AssetModuleOutputPath: {
        description: [32m"Emit the asset in the specified folder relative to 'output.path'. This should only be needed when custom 'publicPath' is specified to match the folder structure there."[39m,
        anyOf: [
          { type: [32m'string'[39m, absolutePath: [33mfalse[39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m'((pathData: import("../lib/Compilation").PathData, assetInfo?: import("../lib/Compilation").AssetInfo) => string)'[39m
          }
        ]
      },
      AssetParserDataUrlFunction: {
        description: [32m'Function that executes for module and should return whenever asset should be inlined as DataUrl.'[39m,
        instanceof: [32m'Function'[39m,
        tsType: [32m"((source: string | Buffer, context: { filename: string, module: import('../lib/Module') }) => boolean)"[39m
      },
      AssetParserDataUrlOptions: {
        description: [32m'Options object for DataUrl condition.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          maxSize: {
            description: [32m'Maximum size of asset that should be inline as modules. Default: 8kb.'[39m,
            type: [32m'number'[39m
          }
        }
      },
      AssetParserOptions: {
        description: [32m'Parser options for asset modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          dataUrlCondition: {
            description: [32m'The condition for inlining the asset as DataUrl.'[39m,
            anyOf: [36m[Array][39m
          }
        }
      },
      AssetResourceGeneratorOptions: {
        description: [32m'Generator options for asset/resource modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          binary: {
            description: [32m"Whether or not this asset module should be considered binary. This can be set to 'false' to treat this asset module as text."[39m,
            type: [32m'boolean'[39m
          },
          emit: {
            description: [32m"Emit an output asset from this asset module. This can be set to 'false' to omit emitting e. g. for SSR."[39m,
            type: [32m'boolean'[39m
          },
          filename: { [32m'$ref'[39m: [32m'#/definitions/FilenameTemplate'[39m },
          outputPath: { [32m'$ref'[39m: [32m'#/definitions/AssetModuleOutputPath'[39m },
          publicPath: { [32m'$ref'[39m: [32m'#/definitions/RawPublicPath'[39m }
        }
      },
      AuxiliaryComment: {
        description: [32m'Add a comment in the UMD wrapper.'[39m,
        anyOf: [
          {
            description: [32m'Append the same comment above each import style.'[39m,
            type: [32m'string'[39m
          },
          { [32m'$ref'[39m: [32m'#/definitions/LibraryCustomUmdCommentObject'[39m }
        ]
      },
      Bail: {
        description: [32m'Report the first error as a hard error instead of tolerating it.'[39m,
        type: [32m'boolean'[39m
      },
      CacheOptions: {
        description: [32m'Cache generated modules and chunks to improve performance for multiple incremental builds.'[39m,
        anyOf: [
          { description: [32m'Enable in memory caching.'[39m, enum: [36m[Array][39m },
          { [32m'$ref'[39m: [32m'#/definitions/CacheOptionsNormalized'[39m }
        ]
      },
      CacheOptionsNormalized: {
        description: [32m'Cache generated modules and chunks to improve performance for multiple incremental builds.'[39m,
        anyOf: [
          { description: [32m'Disable caching.'[39m, enum: [36m[Array][39m },
          { [32m'$ref'[39m: [32m'#/definitions/MemoryCacheOptions'[39m },
          { [32m'$ref'[39m: [32m'#/definitions/FileCacheOptions'[39m }
        ]
      },
      Charset: {
        description: [32m'Add charset attribute for script tag.'[39m,
        type: [32m'boolean'[39m
      },
      ChunkFilename: {
        description: [32m"Specifies the filename template of output files of non-initial chunks on disk. You must **not** specify an absolute path here, but the path may contain folders separated by '/'! The specified path is joined with the value of the 'output.path' option to determine the location on disk."[39m,
        oneOf: [ { [32m'$ref'[39m: [32m'#/definitions/FilenameTemplate'[39m } ]
      },
      ChunkFormat: {
        description: [32m"The format of chunks (formats included by default are 'array-push' (web/WebWorker), 'commonjs' (node.js), 'module' (ESM), but others might be added by plugins)."[39m,
        anyOf: [ { enum: [36m[Array][39m }, { type: [32m'string'[39m } ]
      },
      ChunkLoadTimeout: {
        description: [32m'Number of milliseconds before chunk request expires.'[39m,
        type: [32m'number'[39m
      },
      ChunkLoading: {
        description: [32m"The method of loading chunks (methods included by default are 'jsonp' (web), 'import' (ESM), 'importScripts' (WebWorker), 'require' (sync node.js), 'async-node' (async node.js), but others might be added by plugins)."[39m,
        anyOf: [
          { enum: [36m[Array][39m },
          { [32m'$ref'[39m: [32m'#/definitions/ChunkLoadingType'[39m }
        ]
      },
      ChunkLoadingGlobal: {
        description: [32m'The global variable used by webpack for loading of chunks.'[39m,
        type: [32m'string'[39m
      },
      ChunkLoadingType: {
        description: [32m"The method of loading chunks (methods included by default are 'jsonp' (web), 'import' (ESM), 'importScripts' (WebWorker), 'require' (sync node.js), 'async-node' (async node.js), but others might be added by plugins)."[39m,
        anyOf: [ { enum: [36m[Array][39m }, { type: [32m'string'[39m } ]
      },
      Clean: {
        description: [32m'Clean the output directory before emit.'[39m,
        anyOf: [
          { type: [32m'boolean'[39m },
          { [32m'$ref'[39m: [32m'#/definitions/CleanOptions'[39m }
        ]
      },
      CleanOptions: {
        description: [32m'Advanced options for cleaning assets.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          dry: {
            description: [32m'Log the assets that should be removed instead of deleting them.'[39m,
            type: [32m'boolean'[39m
          },
          keep: { description: [32m'Keep these assets.'[39m, anyOf: [36m[Array][39m }
        }
      },
      CompareBeforeEmit: {
        description: [32m'Check if to be emitted file already exists and have the same content before writing to output filesystem.'[39m,
        type: [32m'boolean'[39m
      },
      Context: {
        description: [32m'The base directory (absolute path!) for resolving the `entry` option. If `output.pathinfo` is set, the included pathinfo is shortened to this directory.'[39m,
        type: [32m'string'[39m,
        absolutePath: [33mtrue[39m
      },
      CrossOriginLoading: {
        description: [32m'This option enables cross-origin loading of chunks.'[39m,
        enum: [ [33mfalse[39m, [32m'anonymous'[39m, [32m'use-credentials'[39m ]
      },
      CssAutoGeneratorOptions: {
        description: [32m'Generator options for css/auto modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          esModule: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorEsModule'[39m },
          exportsConvention: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorExportsConvention'[39m },
          exportsOnly: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorExportsOnly'[39m },
          localIdentName: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorLocalIdentName'[39m }
        }
      },
      CssAutoParserOptions: {
        description: [32m'Parser options for css/auto modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          import: { [32m'$ref'[39m: [32m'#/definitions/CssParserImport'[39m },
          namedExports: { [32m'$ref'[39m: [32m'#/definitions/CssParserNamedExports'[39m },
          url: { [32m'$ref'[39m: [32m'#/definitions/CssParserUrl'[39m }
        }
      },
      CssChunkFilename: {
        description: [32m"Specifies the filename template of non-initial output css files on disk. You must **not** specify an absolute path here, but the path may contain folders separated by '/'! The specified path is joined with the value of the 'output.path' option to determine the location on disk."[39m,
        oneOf: [ { [32m'$ref'[39m: [32m'#/definitions/FilenameTemplate'[39m } ]
      },
      CssFilename: {
        description: [32m"Specifies the filename template of output css files on disk. You must **not** specify an absolute path here, but the path may contain folders separated by '/'! The specified path is joined with the value of the 'output.path' option to determine the location on disk."[39m,
        oneOf: [ { [32m'$ref'[39m: [32m'#/definitions/FilenameTemplate'[39m } ]
      },
      CssGeneratorEsModule: {
        description: [32m'Configure the generated JS modules that use the ES modules syntax.'[39m,
        type: [32m'boolean'[39m
      },
      CssGeneratorExportsConvention: {
        description: [32m'Specifies the convention of exported names.'[39m,
        anyOf: [
          { enum: [36m[Array][39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m'((name: string) => string)'[39m
          }
        ]
      },
      CssGeneratorExportsOnly: {
        description: [32m'Avoid generating and loading a stylesheet and only embed exports from css into output javascript files.'[39m,
        type: [32m'boolean'[39m
      },
      CssGeneratorLocalIdentName: {
        description: [32m'Configure the generated local ident name.'[39m,
        type: [32m'string'[39m
      },
      CssGeneratorOptions: {
        description: [32m'Generator options for css modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          esModule: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorEsModule'[39m },
          exportsOnly: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorExportsOnly'[39m }
        }
      },
      CssGlobalGeneratorOptions: {
        description: [32m'Generator options for css/global modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          esModule: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorEsModule'[39m },
          exportsConvention: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorExportsConvention'[39m },
          exportsOnly: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorExportsOnly'[39m },
          localIdentName: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorLocalIdentName'[39m }
        }
      },
      CssGlobalParserOptions: {
        description: [32m'Parser options for css/global modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          import: { [32m'$ref'[39m: [32m'#/definitions/CssParserImport'[39m },
          namedExports: { [32m'$ref'[39m: [32m'#/definitions/CssParserNamedExports'[39m },
          url: { [32m'$ref'[39m: [32m'#/definitions/CssParserUrl'[39m }
        }
      },
      CssModuleGeneratorOptions: {
        description: [32m'Generator options for css/module modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          esModule: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorEsModule'[39m },
          exportsConvention: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorExportsConvention'[39m },
          exportsOnly: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorExportsOnly'[39m },
          localIdentName: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorLocalIdentName'[39m }
        }
      },
      CssModuleParserOptions: {
        description: [32m'Parser options for css/module modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          import: { [32m'$ref'[39m: [32m'#/definitions/CssParserImport'[39m },
          namedExports: { [32m'$ref'[39m: [32m'#/definitions/CssParserNamedExports'[39m },
          url: { [32m'$ref'[39m: [32m'#/definitions/CssParserUrl'[39m }
        }
      },
      CssParserImport: {
        description: [32m'Enable/disable `@import` at-rules handling.'[39m,
        type: [32m'boolean'[39m
      },
      CssParserNamedExports: {
        description: [32m'Use ES modules named export for css exports.'[39m,
        type: [32m'boolean'[39m
      },
      CssParserOptions: {
        description: [32m'Parser options for css modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          import: { [32m'$ref'[39m: [32m'#/definitions/CssParserImport'[39m },
          namedExports: { [32m'$ref'[39m: [32m'#/definitions/CssParserNamedExports'[39m },
          url: { [32m'$ref'[39m: [32m'#/definitions/CssParserUrl'[39m }
        }
      },
      CssParserUrl: {
        description: [32m'Enable/disable `url()`/`image-set()`/`src()`/`image()` functions handling.'[39m,
        type: [32m'boolean'[39m
      },
      Dependencies: {
        description: [32m'References to other configurations to depend on.'[39m,
        type: [32m'array'[39m,
        items: {
          description: [32m'References to another configuration to depend on.'[39m,
          type: [32m'string'[39m
        }
      },
      DevServer: {
        description: [32m'Options for the webpack-dev-server.'[39m,
        anyOf: [
          { description: [32m'Disable dev server.'[39m, enum: [36m[Array][39m },
          {
            description: [32m'Options for the webpack-dev-server.'[39m,
            type: [32m'object'[39m
          }
        ]
      },
      DevTool: {
        description: [32m'A developer tool to enhance debugging (false | eval | [inline-|hidden-|eval-][nosources-][cheap-[module-]]source-map).'[39m,
        anyOf: [
          { enum: [36m[Array][39m },
          {
            type: [32m'string'[39m,
            pattern: [32m'^(inline-|hidden-|eval-)?(nosources-)?(cheap-(module-)?)?source-map(-debugids)?$'[39m
          }
        ]
      },
      DevtoolFallbackModuleFilenameTemplate: {
        description: [32m'Similar to `output.devtoolModuleFilenameTemplate`, but used in the case of duplicate module identifiers.'[39m,
        anyOf: [
          { type: [32m'string'[39m },
          { instanceof: [32m'Function'[39m, tsType: [32m'Function'[39m }
        ]
      },
      DevtoolModuleFilenameTemplate: {
        description: [32m'Filename template string of function for the sources array in a generated SourceMap.'[39m,
        anyOf: [
          { type: [32m'string'[39m },
          { instanceof: [32m'Function'[39m, tsType: [32m'Function'[39m }
        ]
      },
      DevtoolNamespace: {
        description: [32m"Module namespace to use when interpolating filename template string for the sources array in a generated SourceMap. Defaults to `output.library` if not set. It's useful for avoiding runtime collisions in sourcemaps from multiple webpack projects built as libraries."[39m,
        type: [32m'string'[39m
      },
      EmptyGeneratorOptions: {
        description: [32m'No generator options are supported for this module type.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m
      },
      EmptyParserOptions: {
        description: [32m'No parser options are supported for this module type.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m
      },
      EnabledChunkLoadingTypes: {
        description: [32m'List of chunk loading types enabled for use by entry points.'[39m,
        type: [32m'array'[39m,
        items: { [32m'$ref'[39m: [32m'#/definitions/ChunkLoadingType'[39m }
      },
      EnabledLibraryTypes: {
        description: [32m'List of library types enabled for use by entry points.'[39m,
        type: [32m'array'[39m,
        items: { [32m'$ref'[39m: [32m'#/definitions/LibraryType'[39m }
      },
      EnabledWasmLoadingTypes: {
        description: [32m'List of wasm loading types enabled for use by entry points.'[39m,
        type: [32m'array'[39m,
        items: { [32m'$ref'[39m: [32m'#/definitions/WasmLoadingType'[39m }
      },
      Entry: {
        description: [32m'The entry point(s) of the compilation.'[39m,
        anyOf: [
          { [32m'$ref'[39m: [32m'#/definitions/EntryDynamic'[39m },
          { [32m'$ref'[39m: [32m'#/definitions/EntryStatic'[39m }
        ]
      },
      EntryDescription: {
        description: [32m'An object with entry point description.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          asyncChunks: {
            description: [32m'Enable/disable creating async chunks that are loaded on demand.'[39m,
            type: [32m'boolean'[39m
          },
          baseUri: { description: [32m'Base uri for this entry.'[39m, type: [32m'string'[39m },
          chunkLoading: { [32m'$ref'[39m: [32m'#/definitions/ChunkLoading'[39m },
          dependOn: {
            description: [32m'The entrypoints that the current entrypoint depend on. They must be loaded when this entrypoint is loaded.'[39m,
            anyOf: [36m[Array][39m
          },
          filename: { [32m'$ref'[39m: [32m'#/definitions/EntryFilename'[39m },
          import: { [32m'$ref'[39m: [32m'#/definitions/EntryItem'[39m },
          layer: { [32m'$ref'[39m: [32m'#/definitions/Layer'[39m },
          library: { [32m'$ref'[39m: [32m'#/definitions/LibraryOptions'[39m },
          publicPath: { [32m'$ref'[39m: [32m'#/definitions/PublicPath'[39m },
          runtime: { [32m'$ref'[39m: [32m'#/definitions/EntryRuntime'[39m },
          wasmLoading: { [32m'$ref'[39m: [32m'#/definitions/WasmLoading'[39m }
        },
        required: [ [32m'import'[39m ]
      },
      EntryDescriptionNormalized: {
        description: [32m'An object with entry point description.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          asyncChunks: {
            description: [32m'Enable/disable creating async chunks that are loaded on demand.'[39m,
            type: [32m'boolean'[39m
          },
          baseUri: { description: [32m'Base uri for this entry.'[39m, type: [32m'string'[39m },
          chunkLoading: { [32m'$ref'[39m: [32m'#/definitions/ChunkLoading'[39m },
          dependOn: {
            description: [32m'The entrypoints that the current entrypoint depend on. They must be loaded when this entrypoint is loaded.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m,
            minItems: [33m1[39m,
            uniqueItems: [33mtrue[39m
          },
          filename: { [32m'$ref'[39m: [32m'#/definitions/Filename'[39m },
          import: {
            description: [32m'Module(s) that are loaded upon startup. The last one is exported.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m,
            minItems: [33m1[39m,
            uniqueItems: [33mtrue[39m
          },
          layer: { [32m'$ref'[39m: [32m'#/definitions/Layer'[39m },
          library: { [32m'$ref'[39m: [32m'#/definitions/LibraryOptions'[39m },
          publicPath: { [32m'$ref'[39m: [32m'#/definitions/PublicPath'[39m },
          runtime: { [32m'$ref'[39m: [32m'#/definitions/EntryRuntime'[39m },
          wasmLoading: { [32m'$ref'[39m: [32m'#/definitions/WasmLoading'[39m }
        }
      },
      EntryDynamic: {
        description: [32m'A Function returning an entry object, an entry string, an entry array or a promise to these things.'[39m,
        instanceof: [32m'Function'[39m,
        tsType: [32m'(() => EntryStatic | Promise<EntryStatic>)'[39m
      },
      EntryDynamicNormalized: {
        description: [32m'A Function returning a Promise resolving to a normalized entry.'[39m,
        instanceof: [32m'Function'[39m,
        tsType: [32m'(() => Promise<EntryStaticNormalized>)'[39m
      },
      EntryFilename: {
        description: [32m"Specifies the filename of the output file on disk. You must **not** specify an absolute path here, but the path may contain folders separated by '/'! The specified path is joined with the value of the 'output.path' option to determine the location on disk."[39m,
        oneOf: [ { [32m'$ref'[39m: [32m'#/definitions/FilenameTemplate'[39m } ]
      },
      EntryItem: {
        description: [32m'Module(s) that are loaded upon startup.'[39m,
        anyOf: [
          {
            description: [32m'All modules are loaded upon startup. The last one is exported.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m,
            minItems: [33m1[39m,
            uniqueItems: [33mtrue[39m
          },
          {
            description: [32m'The string is resolved to a module which is loaded upon startup.'[39m,
            type: [32m'string'[39m,
            minLength: [33m1[39m
          }
        ]
      },
      EntryNormalized: {
        description: [32m'The entry point(s) of the compilation.'[39m,
        anyOf: [
          { [32m'$ref'[39m: [32m'#/definitions/EntryDynamicNormalized'[39m },
          { [32m'$ref'[39m: [32m'#/definitions/EntryStaticNormalized'[39m }
        ]
      },
      EntryObject: {
        description: [32m'Multiple entry bundles are created. The key is the entry name. The value can be a string, an array or an entry description object.'[39m,
        type: [32m'object'[39m,
        additionalProperties: {
          description: [32m'An entry point with name.'[39m,
          anyOf: [ [36m[Object][39m, [36m[Object][39m ]
        }
      },
      EntryRuntime: {
        description: [32m'The name of the runtime chunk. If set a runtime chunk with this name is created or an existing entrypoint is used as runtime.'[39m,
        anyOf: [ { enum: [36m[Array][39m }, { type: [32m'string'[39m, minLength: [33m1[39m } ]
      },
      EntryStatic: {
        description: [32m'A static entry description.'[39m,
        anyOf: [
          { [32m'$ref'[39m: [32m'#/definitions/EntryObject'[39m },
          { [32m'$ref'[39m: [32m'#/definitions/EntryUnnamed'[39m }
        ]
      },
      EntryStaticNormalized: {
        description: [32m'Multiple entry bundles are created. The key is the entry name. The value is an entry description object.'[39m,
        type: [32m'object'[39m,
        additionalProperties: {
          description: [32m'An object with entry point description.'[39m,
          oneOf: [ [36m[Object][39m ]
        }
      },
      EntryUnnamed: {
        description: [32m'An entry point without name.'[39m,
        oneOf: [ { [32m'$ref'[39m: [32m'#/definitions/EntryItem'[39m } ]
      },
      Environment: {
        description: [32m'The abilities of the environment where the webpack generated code should run.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          arrowFunction: {
            description: [32m"The environment supports arrow functions ('() => { ... }')."[39m,
            type: [32m'boolean'[39m
          },
          asyncFunction: {
            description: [32m"The environment supports async function and await ('async function () { await ... }')."[39m,
            type: [32m'boolean'[39m
          },
          bigIntLiteral: {
            description: [32m'The environment supports BigInt as literal (123n).'[39m,
            type: [32m'boolean'[39m
          },
          const: {
            description: [32m'The environment supports const and let for variable declarations.'[39m,
            type: [32m'boolean'[39m
          },
          destructuring: {
            description: [32m"The environment supports destructuring ('{ a, b } = obj')."[39m,
            type: [32m'boolean'[39m
          },
          document: {
            description: [32m"The environment supports 'document'."[39m,
            type: [32m'boolean'[39m
          },
          dynamicImport: {
            description: [32m'The environment supports an async import() function to import EcmaScript modules.'[39m,
            type: [32m'boolean'[39m
          },
          dynamicImportInWorker: {
            description: [32m'The environment supports an async import() is available when creating a worker.'[39m,
            type: [32m'boolean'[39m
          },
          forOf: {
            description: [32m"The environment supports 'for of' iteration ('for (const x of array) { ... }')."[39m,
            type: [32m'boolean'[39m
          },
          globalThis: {
            description: [32m"The environment supports 'globalThis'."[39m,
            type: [32m'boolean'[39m
          },
          module: {
            description: [32m"The environment supports EcmaScript Module syntax to import EcmaScript modules (import ... from '...')."[39m,
            type: [32m'boolean'[39m
          },
          nodePrefixForCoreModules: {
            description: [32m'The environment supports `node:` prefix for Node.js core modules.'[39m,
            type: [32m'boolean'[39m
          },
          optionalChaining: {
            description: [32m"The environment supports optional chaining ('obj?.a' or 'obj?.()')."[39m,
            type: [32m'boolean'[39m
          },
          templateLiteral: {
            description: [32m'The environment supports template literals.'[39m,
            type: [32m'boolean'[39m
          }
        }
      },
      Experiments: {
        description: [32m'Enables/Disables experiments (experimental features with relax SemVer compatibility).'[39m,
        type: [32m'object'[39m,
        implements: [ [32m'#/definitions/ExperimentsCommon'[39m ],
        additionalProperties: [33mfalse[39m,
        properties: {
          asyncWebAssembly: {
            description: [32m'Support WebAssembly as asynchronous EcmaScript Module.'[39m,
            type: [32m'boolean'[39m
          },
          backCompat: {
            description: [32m'Enable backward-compat layer with deprecation warnings for many webpack 4 APIs.'[39m,
            type: [32m'boolean'[39m
          },
          buildHttp: {
            description: [32m'Build http(s): urls using a lockfile and resource content cache.'[39m,
            anyOf: [36m[Array][39m
          },
          cacheUnaffected: {
            description: [32m'Enable additional in memory caching of modules that are unchanged and reference only unchanged modules.'[39m,
            type: [32m'boolean'[39m
          },
          css: { description: [32m'Enable css support.'[39m, type: [32m'boolean'[39m },
          futureDefaults: {
            description: [32m'Apply defaults of next major version.'[39m,
            type: [32m'boolean'[39m
          },
          layers: { description: [32m'Enable module layers.'[39m, type: [32m'boolean'[39m },
          lazyCompilation: {
            description: [32m'Compile entrypoints and import()s only when they are accessed.'[39m,
            anyOf: [36m[Array][39m
          },
          outputModule: {
            description: [32m'Allow output javascript files as module source type.'[39m,
            type: [32m'boolean'[39m
          },
          syncWebAssembly: {
            description: [32m'Support WebAssembly as synchronous EcmaScript Module (outdated).'[39m,
            type: [32m'boolean'[39m
          },
          topLevelAwait: {
            description: [32m'Allow using top-level-await in EcmaScript Modules.'[39m,
            type: [32m'boolean'[39m
          }
        }
      },
      ExperimentsCommon: {
        description: [32m'Enables/Disables experiments (experimental features with relax SemVer compatibility).'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          asyncWebAssembly: {
            description: [32m'Support WebAssembly as asynchronous EcmaScript Module.'[39m,
            type: [32m'boolean'[39m
          },
          backCompat: {
            description: [32m'Enable backward-compat layer with deprecation warnings for many webpack 4 APIs.'[39m,
            type: [32m'boolean'[39m
          },
          cacheUnaffected: {
            description: [32m'Enable additional in memory caching of modules that are unchanged and reference only unchanged modules.'[39m,
            type: [32m'boolean'[39m
          },
          futureDefaults: {
            description: [32m'Apply defaults of next major version.'[39m,
            type: [32m'boolean'[39m
          },
          layers: { description: [32m'Enable module layers.'[39m, type: [32m'boolean'[39m },
          outputModule: {
            description: [32m'Allow output javascript files as module source type.'[39m,
            type: [32m'boolean'[39m
          },
          syncWebAssembly: {
            description: [32m'Support WebAssembly as synchronous EcmaScript Module (outdated).'[39m,
            type: [32m'boolean'[39m
          },
          topLevelAwait: {
            description: [32m'Allow using top-level-await in EcmaScript Modules.'[39m,
            type: [32m'boolean'[39m
          }
        }
      },
      ExperimentsNormalized: {
        description: [32m'Enables/Disables experiments (experimental features with relax SemVer compatibility).'[39m,
        type: [32m'object'[39m,
        implements: [ [32m'#/definitions/ExperimentsCommon'[39m ],
        additionalProperties: [33mfalse[39m,
        properties: {
          asyncWebAssembly: {
            description: [32m'Support WebAssembly as asynchronous EcmaScript Module.'[39m,
            type: [32m'boolean'[39m
          },
          backCompat: {
            description: [32m'Enable backward-compat layer with deprecation warnings for many webpack 4 APIs.'[39m,
            type: [32m'boolean'[39m
          },
          buildHttp: {
            description: [32m'Build http(s): urls using a lockfile and resource content cache.'[39m,
            oneOf: [36m[Array][39m
          },
          cacheUnaffected: {
            description: [32m'Enable additional in memory caching of modules that are unchanged and reference only unchanged modules.'[39m,
            type: [32m'boolean'[39m
          },
          css: { description: [32m'Enable css support.'[39m, type: [32m'boolean'[39m },
          futureDefaults: {
            description: [32m'Apply defaults of next major version.'[39m,
            type: [32m'boolean'[39m
          },
          layers: { description: [32m'Enable module layers.'[39m, type: [32m'boolean'[39m },
          lazyCompilation: {
            description: [32m'Compile entrypoints and import()s only when they are accessed.'[39m,
            anyOf: [36m[Array][39m
          },
          outputModule: {
            description: [32m'Allow output javascript files as module source type.'[39m,
            type: [32m'boolean'[39m
          },
          syncWebAssembly: {
            description: [32m'Support WebAssembly as synchronous EcmaScript Module (outdated).'[39m,
            type: [32m'boolean'[39m
          },
          topLevelAwait: {
            description: [32m'Allow using top-level-await in EcmaScript Modules.'[39m,
            type: [32m'boolean'[39m
          }
        }
      },
      Extends: {
        description: [32m'Extend configuration from another configuration (only works when using webpack-cli).'[39m,
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m },
          { [32m'$ref'[39m: [32m'#/definitions/ExtendsItem'[39m }
        ]
      },
      ExtendsItem: {
        description: [32m'Path to the configuration to be extended (only works when using webpack-cli).'[39m,
        type: [32m'string'[39m
      },
      ExternalItem: {
        description: [32m"Specify dependency that shouldn't be resolved by webpack, but should become dependencies of the resulting bundle. The kind of the dependency depends on `output.libraryTarget`."[39m,
        anyOf: [
          {
            description: [32m'Every matched dependency becomes external.'[39m,
            instanceof: [32m'RegExp'[39m,
            tsType: [32m'RegExp'[39m
          },
          {
            description: [32m'An exact matched dependency becomes external. The same string is used as external dependency.'[39m,
            type: [32m'string'[39m
          },
          {
            description: [32m'If an dependency matches exactly a property of the object, the property value is used as dependency.'[39m,
            type: [32m'object'[39m,
            additionalProperties: [36m[Object][39m,
            properties: [36m[Object][39m
          },
          {
            description: [32m'The function is called on each dependency (`function(context, request, callback(err, result))`).'[39m,
            instanceof: [32m'Function'[39m,
            tsType: [32m'(((data: ExternalItemFunctionData, callback: (err?: (Error | null), result?: ExternalItemValue) => void) => void) | ((data: ExternalItemFunctionData) => Promise<ExternalItemValue>))'[39m
          }
        ]
      },
      ExternalItemFunctionData: {
        description: [32m"Data object passed as argument when a function is set for 'externals'."[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          context: {
            description: [32m'The directory in which the request is placed.'[39m,
            type: [32m'string'[39m
          },
          contextInfo: {
            description: [32m'Contextual information.'[39m,
            type: [32m'object'[39m,
            tsType: [32m"import('../lib/ModuleFactory').ModuleFactoryCreateDataContextInfo"[39m
          },
          dependencyType: {
            description: [32m'The category of the referencing dependencies.'[39m,
            type: [32m'string'[39m
          },
          getResolve: {
            description: [32m'Get a resolve function with the current resolver options.'[39m,
            instanceof: [32m'Function'[39m,
            tsType: [32m'((options?: ResolveOptions) => ((context: string, request: string, callback: (err?: Error, result?: string) => void) => void) | ((context: string, request: string) => Promise<string>))'[39m
          },
          request: {
            description: [32m'The request as written by the user in the require/import expression/statement.'[39m,
            type: [32m'string'[39m
          }
        }
      },
      ExternalItemValue: {
        description: [32m'The dependency used for the external.'[39m,
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m },
          {
            description: [32m'`true`: The dependency name is used as target of the external.'[39m,
            type: [32m'boolean'[39m
          },
          {
            description: [32m'The target of the external.'[39m,
            type: [32m'string'[39m
          },
          { type: [32m'object'[39m }
        ]
      },
      Externals: {
        description: [32m"Specify dependencies that shouldn't be resolved by webpack, but should become dependencies of the resulting bundle. The kind of the dependency depends on `output.libraryTarget`."[39m,
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m },
          { [32m'$ref'[39m: [32m'#/definitions/ExternalItem'[39m }
        ]
      },
      ExternalsPresets: {
        description: [32m'Enable presets of externals for specific targets.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          electron: {
            description: [32m"Treat common electron built-in modules in main and preload context like 'electron', 'ipc' or 'shell' as external and load them via require() when used."[39m,
            type: [32m'boolean'[39m
          },
          electronMain: {
            description: [32m"Treat electron built-in modules in the main context like 'app', 'ipc-main' or 'shell' as external and load them via require() when used."[39m,
            type: [32m'boolean'[39m
          },
          electronPreload: {
            description: [32m"Treat electron built-in modules in the preload context like 'web-frame', 'ipc-renderer' or 'shell' as external and load them via require() when used."[39m,
            type: [32m'boolean'[39m
          },
          electronRenderer: {
            description: [32m"Treat electron built-in modules in the renderer context like 'web-frame', 'ipc-renderer' or 'shell' as external and load them via require() when used."[39m,
            type: [32m'boolean'[39m
          },
          node: {
            description: [32m'Treat node.js built-in modules like fs, path or vm as external and load them via require() when used.'[39m,
            type: [32m'boolean'[39m
          },
          nwjs: {
            description: [32m'Treat NW.js legacy nw.gui module as external and load it via require() when used.'[39m,
            type: [32m'boolean'[39m
          },
          web: {
            description: [32m"Treat references to 'http(s)://...' and 'std:...' as external and load them via import when used (Note that this changes execution order as externals are executed before any other code in the chunk)."[39m,
            type: [32m'boolean'[39m
          },
          webAsync: {
            description: [32m"Treat references to 'http(s)://...' and 'std:...' as external and load them via async import() when used (Note that this external type is an async module, which has various effects on the execution)."[39m,
            type: [32m'boolean'[39m
          }
        }
      },
      ExternalsType: {
        description: [32m"Specifies the default type of externals ('amd*', 'umd*', 'system' and 'jsonp' depend on output.libraryTarget set to the same value)."[39m,
        enum: [
          [32m'var'[39m,             [32m'module'[39m,
          [32m'assign'[39m,          [32m'this'[39m,
          [32m'window'[39m,          [32m'self'[39m,
          [32m'global'[39m,          [32m'commonjs'[39m,
          [32m'commonjs2'[39m,       [32m'commonjs-module'[39m,
          [32m'commonjs-static'[39m, [32m'amd'[39m,
          [32m'amd-require'[39m,     [32m'umd'[39m,
          [32m'umd2'[39m,            [32m'jsonp'[39m,
          [32m'system'[39m,          [32m'promise'[39m,
          [32m'import'[39m,          [32m'module-import'[39m,
          [32m'script'[39m,          [32m'node-commonjs'[39m
        ]
      },
      Falsy: {
        description: [32m"These values will be ignored by webpack and created to be used with '&&' or '||' to improve readability of configurations."[39m,
        cli: { exclude: [33mtrue[39m },
        enum: [ [33mfalse[39m, [33m0[39m, [32m''[39m, [1mnull[22m ],
        undefinedAsNull: [33mtrue[39m,
        tsType: [32m"false | 0 | '' | null | undefined"[39m
      },
      FileCacheOptions: {
        description: [32m'Options object for persistent file-based caching.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          allowCollectingMemory: {
            description: [32m'Allows to collect unused memory allocated during deserialization. This requires copying data into smaller buffers and has a performance cost.'[39m,
            type: [32m'boolean'[39m
          },
          buildDependencies: {
            description: [32m"Dependencies the build depends on (in multiple categories, default categories: 'defaultWebpack')."[39m,
            type: [32m'object'[39m,
            additionalProperties: [36m[Object][39m
          },
          cacheDirectory: {
            description: [32m'Base directory for the cache (defaults to node_modules/.cache/webpack).'[39m,
            type: [32m'string'[39m,
            absolutePath: [33mtrue[39m
          },
          cacheLocation: {
            description: [32m'Locations for the cache (defaults to cacheDirectory / name).'[39m,
            type: [32m'string'[39m,
            absolutePath: [33mtrue[39m
          },
          compression: {
            description: [32m'Compression type used for the cache files.'[39m,
            enum: [36m[Array][39m
          },
          hashAlgorithm: {
            description: [32m'Algorithm used for generation the hash (see node.js crypto package).'[39m,
            type: [32m'string'[39m
          },
          idleTimeout: {
            description: [32m'Time in ms after which idle period the cache storing should happen.'[39m,
            type: [32m'number'[39m,
            minimum: [33m0[39m
          },
          idleTimeoutAfterLargeChanges: {
            description: [32m'Time in ms after which idle period the cache storing should happen when larger changes has been detected (cumulative build time > 2 x avg cache store time).'[39m,
            type: [32m'number'[39m,
            minimum: [33m0[39m
          },
          idleTimeoutForInitialStore: {
            description: [32m'Time in ms after which idle period the initial cache storing should happen.'[39m,
            type: [32m'number'[39m,
            minimum: [33m0[39m
          },
          immutablePaths: {
            description: [32m'List of paths that are managed by a package manager and contain a version or hash in its path so all files are immutable.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          managedPaths: {
            description: [32m'List of paths that are managed by a package manager and can be trusted to not be modified otherwise.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          maxAge: {
            description: [32m'Time for which unused cache entries stay in the filesystem cache at minimum (in milliseconds).'[39m,
            type: [32m'number'[39m,
            minimum: [33m0[39m
          },
          maxMemoryGenerations: {
            description: [32m'Number of generations unused cache entries stay in memory cache at minimum (0 = no memory cache used, 1 = may be removed after unused for a single compilation, ..., Infinity: kept forever). Cache entries will be deserialized from disk when removed from memory cache.'[39m,
            type: [32m'number'[39m,
            minimum: [33m0[39m
          },
          memoryCacheUnaffected: {
            description: [32m'Additionally cache computation of modules that are unchanged and reference only unchanged modules in memory.'[39m,
            type: [32m'boolean'[39m
          },
          name: {
            description: [32m'Name for the cache. Different names will lead to different coexisting caches.'[39m,
            type: [32m'string'[39m
          },
          profile: {
            description: [32m'Track and log detailed timing information for individual cache items.'[39m,
            type: [32m'boolean'[39m
          },
          readonly: {
            description: [32m'Enable/disable readonly mode.'[39m,
            type: [32m'boolean'[39m
          },
          store: {
            description: [32m'When to store data to the filesystem. (pack: Store data when compiler is idle in a single file).'[39m,
            enum: [36m[Array][39m
          },
          type: { description: [32m'Filesystem caching.'[39m, enum: [36m[Array][39m },
          version: {
            description: [32m"Version of the cache data. Different versions won't allow to reuse the cache and override existing content. Update the version when config changed in a way which doesn't allow to reuse cache. This will invalidate the cache."[39m,
            type: [32m'string'[39m
          }
        },
        required: [ [32m'type'[39m ]
      },
      Filename: {
        description: [32m"Specifies the filename of output files on disk. You must **not** specify an absolute path here, but the path may contain folders separated by '/'! The specified path is joined with the value of the 'output.path' option to determine the location on disk."[39m,
        oneOf: [ { [32m'$ref'[39m: [32m'#/definitions/FilenameTemplate'[39m } ]
      },
      FilenameTemplate: {
        description: [32m"Specifies the filename template of output files on disk. You must **not** specify an absolute path here, but the path may contain folders separated by '/'! The specified path is joined with the value of the 'output.path' option to determine the location on disk."[39m,
        anyOf: [
          { type: [32m'string'[39m, absolutePath: [33mfalse[39m, minLength: [33m1[39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m'((pathData: import("../lib/Compilation").PathData, assetInfo?: import("../lib/Compilation").AssetInfo) => string)'[39m
          }
        ]
      },
      FilterItemTypes: {
        description: [32m'Filtering value, regexp or function.'[39m,
        cli: { helper: [33mtrue[39m },
        anyOf: [
          { instanceof: [32m'RegExp'[39m, tsType: [32m'RegExp'[39m },
          { type: [32m'string'[39m, absolutePath: [33mfalse[39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m'((value: string) => boolean)'[39m
          }
        ]
      },
      FilterTypes: {
        description: [32m'Filtering values.'[39m,
        cli: { helper: [33mtrue[39m },
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m },
          { [32m'$ref'[39m: [32m'#/definitions/FilterItemTypes'[39m }
        ]
      },
      GeneratorOptionsByModuleType: {
        description: [32m'Specify options for each generator.'[39m,
        type: [32m'object'[39m,
        additionalProperties: {
          description: [32m'Options for generating.'[39m,
          type: [32m'object'[39m,
          additionalProperties: [33mtrue[39m
        },
        properties: {
          asset: { [32m'$ref'[39m: [32m'#/definitions/AssetGeneratorOptions'[39m },
          [32m'asset/inline'[39m: { [32m'$ref'[39m: [32m'#/definitions/AssetInlineGeneratorOptions'[39m },
          [32m'asset/resource'[39m: { [32m'$ref'[39m: [32m'#/definitions/AssetResourceGeneratorOptions'[39m },
          css: { [32m'$ref'[39m: [32m'#/definitions/CssGeneratorOptions'[39m },
          [32m'css/auto'[39m: { [32m'$ref'[39m: [32m'#/definitions/CssAutoGeneratorOptions'[39m },
          [32m'css/global'[39m: { [32m'$ref'[39m: [32m'#/definitions/CssGlobalGeneratorOptions'[39m },
          [32m'css/module'[39m: { [32m'$ref'[39m: [32m'#/definitions/CssModuleGeneratorOptions'[39m },
          javascript: { [32m'$ref'[39m: [32m'#/definitions/EmptyGeneratorOptions'[39m },
          [32m'javascript/auto'[39m: { [32m'$ref'[39m: [32m'#/definitions/EmptyGeneratorOptions'[39m },
          [32m'javascript/dynamic'[39m: { [32m'$ref'[39m: [32m'#/definitions/EmptyGeneratorOptions'[39m },
          [32m'javascript/esm'[39m: { [32m'$ref'[39m: [32m'#/definitions/EmptyGeneratorOptions'[39m }
        }
      },
      GlobalObject: {
        description: [32m'An expression which is used to address the global object/scope in runtime code.'[39m,
        type: [32m'string'[39m,
        minLength: [33m1[39m
      },
      HashDigest: { description: [32m'Digest type used for the hash.'[39m, type: [32m'string'[39m },
      HashDigestLength: {
        description: [32m'Number of chars which are used for the hash.'[39m,
        type: [32m'number'[39m,
        minimum: [33m1[39m
      },
      HashFunction: {
        description: [32m'Algorithm used for generation the hash (see node.js crypto package).'[39m,
        anyOf: [
          { type: [32m'string'[39m, minLength: [33m1[39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m"typeof import('../lib/util/Hash')"[39m
          }
        ]
      },
      HashSalt: {
        description: [32m'Any string which is added to the hash to salt it.'[39m,
        type: [32m'string'[39m,
        minLength: [33m1[39m
      },
      HotUpdateChunkFilename: {
        description: [32m'The filename of the Hot Update Chunks. They are inside the output.path directory.'[39m,
        type: [32m'string'[39m,
        absolutePath: [33mfalse[39m
      },
      HotUpdateGlobal: {
        description: [32m'The global variable used by webpack for loading of hot update chunks.'[39m,
        type: [32m'string'[39m
      },
      HotUpdateMainFilename: {
        description: [32m"The filename of the Hot Update Main File. It is inside the 'output.path' directory."[39m,
        type: [32m'string'[39m,
        absolutePath: [33mfalse[39m
      },
      HttpUriAllowedUris: {
        description: [32m'List of allowed URIs for building http resources.'[39m,
        cli: { exclude: [33mtrue[39m },
        oneOf: [ { [32m'$ref'[39m: [32m'#/definitions/HttpUriOptionsAllowedUris'[39m } ]
      },
      HttpUriOptions: {
        description: [32m'Options for building http resources.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          allowedUris: { [32m'$ref'[39m: [32m'#/definitions/HttpUriOptionsAllowedUris'[39m },
          cacheLocation: {
            description: [32m"Location where resource content is stored for lockfile entries. It's also possible to disable storing by passing false."[39m,
            anyOf: [36m[Array][39m
          },
          frozen: {
            description: [32m'When set, anything that would lead to a modification of the lockfile or any resource content, will result in an error.'[39m,
            type: [32m'boolean'[39m
          },
          lockfileLocation: {
            description: [32m'Location of the lockfile.'[39m,
            type: [32m'string'[39m,
            absolutePath: [33mtrue[39m
          },
          proxy: {
            description: [32m'Proxy configuration, which can be used to specify a proxy server to use for HTTP requests.'[39m,
            type: [32m'string'[39m
          },
          upgrade: {
            description: [32m'When set, resources of existing lockfile entries will be fetched and entries will be upgraded when resource content has changed.'[39m,
            type: [32m'boolean'[39m
          }
        },
        required: [ [32m'allowedUris'[39m ]
      },
      HttpUriOptionsAllowedUris: {
        description: [32m'List of allowed URIs (resp. the beginning of them).'[39m,
        type: [32m'array'[39m,
        items: {
          description: [32m'List of allowed URIs (resp. the beginning of them).'[39m,
          anyOf: [ [36m[Object][39m, [36m[Object][39m, [36m[Object][39m ]
        }
      },
      IgnoreWarnings: {
        description: [32m'Ignore specific warnings.'[39m,
        type: [32m'array'[39m,
        items: {
          description: [32m'Ignore specific warnings.'[39m,
          anyOf: [ [36m[Object][39m, [36m[Object][39m, [36m[Object][39m ]
        }
      },
      IgnoreWarningsNormalized: {
        description: [32m'Ignore specific warnings.'[39m,
        type: [32m'array'[39m,
        items: {
          description: [32m'A function to select warnings based on the raw warning instance.'[39m,
          instanceof: [32m'Function'[39m,
          tsType: [32m"((warning: import('../lib/WebpackError'), compilation: import('../lib/Compilation')) => boolean)"[39m
        }
      },
      Iife: {
        description: [32m"Wrap javascript code into IIFE's to avoid leaking into global scope."[39m,
        type: [32m'boolean'[39m
      },
      ImportFunctionName: {
        description: [32m'The name of the native import() function (can be exchanged for a polyfill).'[39m,
        type: [32m'string'[39m
      },
      ImportMetaName: {
        description: [32m'The name of the native import.meta object (can be exchanged for a polyfill).'[39m,
        type: [32m'string'[39m
      },
      InfrastructureLogging: {
        description: [32m'Options for infrastructure level logging.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          appendOnly: {
            description: [32m'Only appends lines to the output. Avoids updating existing output e. g. for status messages. This option is only used when no custom console is provided.'[39m,
            type: [32m'boolean'[39m
          },
          colors: {
            description: [32m'Enables/Disables colorful output. This option is only used when no custom console is provided.'[39m,
            type: [32m'boolean'[39m
          },
          console: {
            description: [32m'Custom console used for logging.'[39m,
            tsType: [32m'Console'[39m
          },
          debug: {
            description: [32m'Enable debug logging for specific loggers.'[39m,
            anyOf: [36m[Array][39m
          },
          level: { description: [32m'Log level.'[39m, enum: [36m[Array][39m },
          stream: {
            description: [32m'Stream used for logging output. Defaults to process.stderr. This option is only used when no custom console is provided.'[39m,
            tsType: [32m'NodeJS.WritableStream'[39m
          }
        }
      },
      JavascriptParserOptions: {
        description: [32m'Parser options for javascript modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mtrue[39m,
        properties: {
          amd: { [32m'$ref'[39m: [32m'#/definitions/Amd'[39m },
          browserify: {
            description: [32m'Enable/disable special handling for browserify bundles.'[39m,
            type: [32m'boolean'[39m
          },
          commonjs: {
            description: [32m'Enable/disable parsing of CommonJs syntax.'[39m,
            type: [32m'boolean'[39m
          },
          commonjsMagicComments: {
            description: [32m'Enable/disable parsing of magic comments in CommonJs syntax.'[39m,
            type: [32m'boolean'[39m
          },
          createRequire: {
            description: [32m'Enable/disable parsing "import { createRequire } from "module"" and evaluating createRequire().'[39m,
            anyOf: [36m[Array][39m
          },
          dynamicImportFetchPriority: {
            description: [32m'Specifies global fetchPriority for dynamic import.'[39m,
            enum: [36m[Array][39m
          },
          dynamicImportMode: {
            description: [32m'Specifies global mode for dynamic import.'[39m,
            enum: [36m[Array][39m
          },
          dynamicImportPrefetch: {
            description: [32m'Specifies global prefetch for dynamic import.'[39m,
            anyOf: [36m[Array][39m
          },
          dynamicImportPreload: {
            description: [32m'Specifies global preload for dynamic import.'[39m,
            anyOf: [36m[Array][39m
          },
          exportsPresence: {
            description: [32m'Specifies the behavior of invalid export names in "import ... from ..." and "export ... from ...".'[39m,
            enum: [36m[Array][39m
          },
          exprContextCritical: {
            description: [32m'Enable warnings for full dynamic dependencies.'[39m,
            type: [32m'boolean'[39m
          },
          exprContextRecursive: {
            description: [32m'Enable recursive directory lookup for full dynamic dependencies.'[39m,
            type: [32m'boolean'[39m
          },
          exprContextRegExp: {
            description: [32m'Sets the default regular expression for full dynamic dependencies.'[39m,
            anyOf: [36m[Array][39m
          },
          exprContextRequest: {
            description: [32m'Set the default request for full dynamic dependencies.'[39m,
            type: [32m'string'[39m
          },
          harmony: {
            description: [32m'Enable/disable parsing of EcmaScript Modules syntax.'[39m,
            type: [32m'boolean'[39m
          },
          import: {
            description: [32m'Enable/disable parsing of import() syntax.'[39m,
            type: [32m'boolean'[39m
          },
          importExportsPresence: {
            description: [32m'Specifies the behavior of invalid export names in "import ... from ...".'[39m,
            enum: [36m[Array][39m
          },
          importMeta: {
            description: [32m'Enable/disable evaluating import.meta.'[39m,
            type: [32m'boolean'[39m
          },
          importMetaContext: {
            description: [32m'Enable/disable evaluating import.meta.webpackContext.'[39m,
            type: [32m'boolean'[39m
          },
          node: { [32m'$ref'[39m: [32m'#/definitions/Node'[39m },
          overrideStrict: {
            description: [32m'Override the module to strict or non-strict. This may affect the behavior of the module (some behaviors differ between strict and non-strict), so please configure this option carefully.'[39m,
            enum: [36m[Array][39m
          },
          reexportExportsPresence: {
            description: [32m'Specifies the behavior of invalid export names in "export ... from ...". This might be useful to disable during the migration from "export ... from ..." to "export type ... from ..." when reexporting types in TypeScript.'[39m,
            enum: [36m[Array][39m
          },
          requireContext: {
            description: [32m'Enable/disable parsing of require.context syntax.'[39m,
            type: [32m'boolean'[39m
          },
          requireEnsure: {
            description: [32m'Enable/disable parsing of require.ensure syntax.'[39m,
            type: [32m'boolean'[39m
          },
          requireInclude: {
            description: [32m'Enable/disable parsing of require.include syntax.'[39m,
            type: [32m'boolean'[39m
          },
          requireJs: {
            description: [32m'Enable/disable parsing of require.js special syntax like require.config, requirejs.config, require.version and requirejs.onError.'[39m,
            type: [32m'boolean'[39m
          },
          strictExportPresence: {
            description: [32m`Deprecated in favor of "exportsPresence". Emit errors instead of warnings when imported names don't exist in imported module.`[39m,
            type: [32m'boolean'[39m
          },
          strictThisContextOnImports: {
            description: [32m'Handle the this context correctly according to the spec for namespace objects.'[39m,
            type: [32m'boolean'[39m
          },
          system: {
            description: [32m'Enable/disable parsing of System.js special syntax like System.import, System.get, System.set and System.register.'[39m,
            type: [32m'boolean'[39m
          },
          unknownContextCritical: {
            description: [32m'Enable warnings when using the require function in a not statically analyse-able way.'[39m,
            type: [32m'boolean'[39m
          },
          unknownContextRecursive: {
            description: [32m'Enable recursive directory lookup when using the require function in a not statically analyse-able way.'[39m,
            type: [32m'boolean'[39m
          },
          unknownContextRegExp: {
            description: [32m'Sets the regular expression when using the require function in a not statically analyse-able way.'[39m,
            anyOf: [36m[Array][39m
          },
          unknownContextRequest: {
            description: [32m'Sets the request when using the require function in a not statically analyse-able way.'[39m,
            type: [32m'string'[39m
          },
          url: {
            description: [32m'Enable/disable parsing of new URL() syntax.'[39m,
            anyOf: [36m[Array][39m
          },
          worker: {
            description: [32m'Disable or configure parsing of WebWorker syntax like new Worker() or navigator.serviceWorker.register().'[39m,
            anyOf: [36m[Array][39m
          },
          wrappedContextCritical: {
            description: [32m'Enable warnings for partial dynamic dependencies.'[39m,
            type: [32m'boolean'[39m
          },
          wrappedContextRecursive: {
            description: [32m'Enable recursive directory lookup for partial dynamic dependencies.'[39m,
            type: [32m'boolean'[39m
          },
          wrappedContextRegExp: {
            description: [32m'Set the inner regular expression for partial dynamic dependencies.'[39m,
            instanceof: [32m'RegExp'[39m,
            tsType: [32m'RegExp'[39m
          }
        }
      },
      Layer: {
        description: [32m'Specifies the layer in which modules of this entrypoint are placed.'[39m,
        anyOf: [ { enum: [36m[Array][39m }, { type: [32m'string'[39m, minLength: [33m1[39m } ]
      },
      LazyCompilationDefaultBackendOptions: {
        description: [32m'Options for the default backend.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          client: { description: [32m'A custom client.'[39m, type: [32m'string'[39m },
          listen: {
            description: [32m'Specifies where to listen to from the server.'[39m,
            anyOf: [36m[Array][39m
          },
          protocol: {
            description: [32m'Specifies the protocol the client should use to connect to the server.'[39m,
            enum: [36m[Array][39m
          },
          server: {
            description: [32m'Specifies how to create the server handling the EventSource requests.'[39m,
            anyOf: [36m[Array][39m
          }
        }
      },
      LazyCompilationOptions: {
        description: [32m'Options for compiling entrypoints and import()s only when they are accessed.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          backend: {
            description: [32m'Specifies the backend that should be used for handling client keep alive.'[39m,
            anyOf: [36m[Array][39m
          },
          entries: {
            description: [32m'Enable/disable lazy compilation for entries.'[39m,
            type: [32m'boolean'[39m
          },
          imports: {
            description: [32m'Enable/disable lazy compilation for import() modules.'[39m,
            type: [32m'boolean'[39m
          },
          test: {
            description: [32m'Specify which entrypoints or import()ed modules should be lazily compiled. This is matched with the imported module and not the entrypoint name.'[39m,
            anyOf: [36m[Array][39m
          }
        }
      },
      Library: {
        description: [32m'Make the output files a library, exporting the exports of the entry point.'[39m,
        anyOf: [
          { [32m'$ref'[39m: [32m'#/definitions/LibraryName'[39m },
          { [32m'$ref'[39m: [32m'#/definitions/LibraryOptions'[39m }
        ]
      },
      LibraryCustomUmdCommentObject: {
        description: [32m'Set explicit comments for `commonjs`, `commonjs2`, `amd`, and `root`.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          amd: {
            description: [32m'Set comment for `amd` section in UMD.'[39m,
            type: [32m'string'[39m
          },
          commonjs: {
            description: [32m'Set comment for `commonjs` (exports) section in UMD.'[39m,
            type: [32m'string'[39m
          },
          commonjs2: {
            description: [32m'Set comment for `commonjs2` (module.exports) section in UMD.'[39m,
            type: [32m'string'[39m
          },
          root: {
            description: [32m'Set comment for `root` (global variable) section in UMD.'[39m,
            type: [32m'string'[39m
          }
        }
      },
      LibraryCustomUmdObject: {
        description: [32m'Description object for all UMD variants of the library name.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          amd: {
            description: [32m'Name of the exposed AMD library in the UMD.'[39m,
            type: [32m'string'[39m,
            minLength: [33m1[39m
          },
          commonjs: {
            description: [32m'Name of the exposed commonjs export in the UMD.'[39m,
            type: [32m'string'[39m,
            minLength: [33m1[39m
          },
          root: {
            description: [32m'Name of the property exposed globally by a UMD library.'[39m,
            anyOf: [36m[Array][39m
          }
        }
      },
      LibraryExport: {
        description: [32m'Specify which export should be exposed as library.'[39m,
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m },
          { type: [32m'string'[39m, minLength: [33m1[39m }
        ]
      },
      LibraryName: {
        description: [32m'The name of the library (some types allow unnamed libraries too).'[39m,
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m, minItems: [33m1[39m },
          { type: [32m'string'[39m, minLength: [33m1[39m },
          { [32m'$ref'[39m: [32m'#/definitions/LibraryCustomUmdObject'[39m }
        ]
      },
      LibraryOptions: {
        description: [32m'Options for library.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          amdContainer: { [32m'$ref'[39m: [32m'#/definitions/AmdContainer'[39m },
          auxiliaryComment: { [32m'$ref'[39m: [32m'#/definitions/AuxiliaryComment'[39m },
          export: { [32m'$ref'[39m: [32m'#/definitions/LibraryExport'[39m },
          name: { [32m'$ref'[39m: [32m'#/definitions/LibraryName'[39m },
          type: { [32m'$ref'[39m: [32m'#/definitions/LibraryType'[39m },
          umdNamedDefine: { [32m'$ref'[39m: [32m'#/definitions/UmdNamedDefine'[39m }
        },
        required: [ [32m'type'[39m ]
      },
      LibraryType: {
        description: [32m"Type of library (types included by default are 'var', 'module', 'assign', 'assign-properties', 'this', 'window', 'self', 'global', 'commonjs', 'commonjs2', 'commonjs-module', 'commonjs-static', 'amd', 'amd-require', 'umd', 'umd2', 'jsonp', 'system', but others might be added by plugins)."[39m,
        anyOf: [ { enum: [36m[Array][39m }, { type: [32m'string'[39m } ]
      },
      Loader: {
        description: [32m'Custom values available in the loader context.'[39m,
        type: [32m'object'[39m
      },
      MemoryCacheOptions: {
        description: [32m'Options object for in-memory caching.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          cacheUnaffected: {
            description: [32m'Additionally cache computation of modules that are unchanged and reference only unchanged modules.'[39m,
            type: [32m'boolean'[39m
          },
          maxGenerations: {
            description: [32m'Number of generations unused cache entries stay in memory cache at minimum (1 = may be removed after unused for a single compilation, ..., Infinity: kept forever).'[39m,
            type: [32m'number'[39m,
            minimum: [33m1[39m
          },
          type: { description: [32m'In memory caching.'[39m, enum: [36m[Array][39m }
        },
        required: [ [32m'type'[39m ]
      },
      Mode: {
        description: [32m'Enable production optimizations or development hints.'[39m,
        enum: [ [32m'development'[39m, [32m'production'[39m, [32m'none'[39m ]
      },
      ModuleFilterItemTypes: {
        description: [32m'Filtering value, regexp or function.'[39m,
        cli: { helper: [33mtrue[39m },
        anyOf: [
          { instanceof: [32m'RegExp'[39m, tsType: [32m'RegExp'[39m },
          { type: [32m'string'[39m, absolutePath: [33mfalse[39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m"((name: string, module: import('../lib/stats/DefaultStatsFactoryPlugin').StatsModule, type: 'module' | 'chunk' | 'root-of-chunk' | 'nested') => boolean)"[39m
          }
        ]
      },
      ModuleFilterTypes: {
        description: [32m'Filtering modules.'[39m,
        cli: { helper: [33mtrue[39m },
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m },
          { [32m'$ref'[39m: [32m'#/definitions/ModuleFilterItemTypes'[39m }
        ]
      },
      ModuleOptions: {
        description: [32m'Options affecting the normal modules (`NormalModuleFactory`).'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          defaultRules: {
            description: [32m'An array of rules applied by default for modules.'[39m,
            cli: [36m[Object][39m,
            oneOf: [36m[Array][39m
          },
          exprContextCritical: {
            description: [32m'Enable warnings for full dynamic dependencies.'[39m,
            type: [32m'boolean'[39m
          },
          exprContextRecursive: {
            description: [32m"Enable recursive directory lookup for full dynamic dependencies. Deprecated: This option has moved to 'module.parser.javascript.exprContextRecursive'."[39m,
            type: [32m'boolean'[39m
          },
          exprContextRegExp: {
            description: [32m"Sets the default regular expression for full dynamic dependencies. Deprecated: This option has moved to 'module.parser.javascript.exprContextRegExp'."[39m,
            anyOf: [36m[Array][39m
          },
          exprContextRequest: {
            description: [32m"Set the default request for full dynamic dependencies. Deprecated: This option has moved to 'module.parser.javascript.exprContextRequest'."[39m,
            type: [32m'string'[39m
          },
          generator: { [32m'$ref'[39m: [32m'#/definitions/GeneratorOptionsByModuleType'[39m },
          noParse: { [32m'$ref'[39m: [32m'#/definitions/NoParse'[39m },
          parser: { [32m'$ref'[39m: [32m'#/definitions/ParserOptionsByModuleType'[39m },
          rules: {
            description: [32m'An array of rules applied for modules.'[39m,
            oneOf: [36m[Array][39m
          },
          strictExportPresence: {
            description: [32m"Emit errors instead of warnings when imported names don't exist in imported module. Deprecated: This option has moved to 'module.parser.javascript.strictExportPresence'."[39m,
            type: [32m'boolean'[39m
          },
          strictThisContextOnImports: {
            description: [32m"Handle the this context correctly according to the spec for namespace objects. Deprecated: This option has moved to 'module.parser.javascript.strictThisContextOnImports'."[39m,
            type: [32m'boolean'[39m
          },
          unknownContextCritical: {
            description: [32m"Enable warnings when using the require function in a not statically analyse-able way. Deprecated: This option has moved to 'module.parser.javascript.unknownContextCritical'."[39m,
            type: [32m'boolean'[39m
          },
          unknownContextRecursive: {
            description: [32m"Enable recursive directory lookup when using the require function in a not statically analyse-able way. Deprecated: This option has moved to 'module.parser.javascript.unknownContextRecursive'."[39m,
            type: [32m'boolean'[39m
          },
          unknownContextRegExp: {
            description: [32m"Sets the regular expression when using the require function in a not statically analyse-able way. Deprecated: This option has moved to 'module.parser.javascript.unknownContextRegExp'."[39m,
            anyOf: [36m[Array][39m
          },
          unknownContextRequest: {
            description: [32m"Sets the request when using the require function in a not statically analyse-able way. Deprecated: This option has moved to 'module.parser.javascript.unknownContextRequest'."[39m,
            type: [32m'string'[39m
          },
          unsafeCache: {
            description: [32m'Cache the resolving of module requests.'[39m,
            anyOf: [36m[Array][39m
          },
          wrappedContextCritical: {
            description: [32m"Enable warnings for partial dynamic dependencies. Deprecated: This option has moved to 'module.parser.javascript.wrappedContextCritical'."[39m,
            type: [32m'boolean'[39m
          },
          wrappedContextRecursive: {
            description: [32m"Enable recursive directory lookup for partial dynamic dependencies. Deprecated: This option has moved to 'module.parser.javascript.wrappedContextRecursive'."[39m,
            type: [32m'boolean'[39m
          },
          wrappedContextRegExp: {
            description: [32m"Set the inner regular expression for partial dynamic dependencies. Deprecated: This option has moved to 'module.parser.javascript.wrappedContextRegExp'."[39m,
            instanceof: [32m'RegExp'[39m,
            tsType: [32m'RegExp'[39m
          }
        }
      },
      ModuleOptionsNormalized: {
        description: [32m'Options affecting the normal modules (`NormalModuleFactory`).'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          defaultRules: {
            description: [32m'An array of rules applied by default for modules.'[39m,
            cli: [36m[Object][39m,
            oneOf: [36m[Array][39m
          },
          generator: { [32m'$ref'[39m: [32m'#/definitions/GeneratorOptionsByModuleType'[39m },
          noParse: { [32m'$ref'[39m: [32m'#/definitions/NoParse'[39m },
          parser: { [32m'$ref'[39m: [32m'#/definitions/ParserOptionsByModuleType'[39m },
          rules: {
            description: [32m'An array of rules applied for modules.'[39m,
            oneOf: [36m[Array][39m
          },
          unsafeCache: {
            description: [32m'Cache the resolving of module requests.'[39m,
            anyOf: [36m[Array][39m
          }
        },
        required: [ [32m'defaultRules'[39m, [32m'generator'[39m, [32m'parser'[39m, [32m'rules'[39m ]
      },
      Name: {
        description: [32m'Name of the configuration. Used when loading multiple configurations.'[39m,
        type: [32m'string'[39m
      },
      NoParse: {
        description: [32m"Don't parse files matching. It's matched against the full resolved request."[39m,
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m, minItems: [33m1[39m },
          {
            description: [32m'A regular expression, when matched the module is not parsed.'[39m,
            instanceof: [32m'RegExp'[39m,
            tsType: [32m'RegExp'[39m
          },
          {
            description: [32m'An absolute path, when the module starts with this path it is not parsed.'[39m,
            type: [32m'string'[39m,
            absolutePath: [33mtrue[39m
          },
          { instanceof: [32m'Function'[39m, tsType: [32m'Function'[39m }
        ]
      },
      Node: {
        description: [32m'Include polyfills or mocks for various node stuff.'[39m,
        anyOf: [ { enum: [36m[Array][39m }, { [32m'$ref'[39m: [32m'#/definitions/NodeOptions'[39m } ]
      },
      NodeOptions: {
        description: [32m'Options object for node compatibility features.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          __dirname: {
            description: [32m"Include a polyfill for the '__dirname' variable."[39m,
            enum: [36m[Array][39m
          },
          __filename: {
            description: [32m"Include a polyfill for the '__filename' variable."[39m,
            enum: [36m[Array][39m
          },
          global: {
            description: [32m"Include a polyfill for the 'global' variable."[39m,
            enum: [36m[Array][39m
          }
        }
      },
      Optimization: {
        description: [32m'Enables/Disables integrated optimizations.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          avoidEntryIife: {
            description: [32m'Avoid wrapping the entry module in an IIFE.'[39m,
            type: [32m'boolean'[39m
          },
          checkWasmTypes: {
            description: [32m'Check for incompatible wasm types when importing/exporting from/to ESM.'[39m,
            type: [32m'boolean'[39m
          },
          chunkIds: {
            description: [32m'Define the algorithm to choose chunk ids (named: readable ids for better debugging, deterministic: numeric hash ids for better long term caching, size: numeric ids focused on minimal initial download size, total-size: numeric ids focused on minimal total download size, false: no algorithm used, as custom one can be provided via plugin).'[39m,
            enum: [36m[Array][39m
          },
          concatenateModules: {
            description: [32m'Concatenate modules when possible to generate less modules, more efficient code and enable more optimizations by the minimizer.'[39m,
            type: [32m'boolean'[39m
          },
          emitOnErrors: {
            description: [32m'Emit assets even when errors occur. Critical errors are emitted into the generated code and will cause errors at runtime.'[39m,
            type: [32m'boolean'[39m
          },
          flagIncludedChunks: {
            description: [32m'Also flag chunks as loaded which contain a subset of the modules.'[39m,
            type: [32m'boolean'[39m
          },
          innerGraph: {
            description: [32m'Creates a module-internal dependency graph for top level symbols, exports and imports, to improve unused exports detection.'[39m,
            type: [32m'boolean'[39m
          },
          mangleExports: {
            description: [32m'Rename exports when possible to generate shorter code (depends on optimization.usedExports and optimization.providedExports, true/"deterministic": generate short deterministic names optimized for caching, "size": generate the shortest possible names).'[39m,
            anyOf: [36m[Array][39m
          },
          mangleWasmImports: {
            description: [32m'Reduce size of WASM by changing imports to shorter strings.'[39m,
            type: [32m'boolean'[39m
          },
          mergeDuplicateChunks: {
            description: [32m'Merge chunks which contain the same modules.'[39m,
            type: [32m'boolean'[39m
          },
          minimize: {
            description: [32m'Enable minimizing the output. Uses optimization.minimizer.'[39m,
            type: [32m'boolean'[39m
          },
          minimizer: {
            description: [32m'Minimizer(s) to use for minimizing the output.'[39m,
            type: [32m'array'[39m,
            cli: [36m[Object][39m,
            items: [36m[Object][39m
          },
          moduleIds: {
            description: [32m'Define the algorithm to choose module ids (natural: numeric ids in order of usage, named: readable ids for better debugging, hashed: (deprecated) short hashes as ids for better long term caching, deterministic: numeric hash ids for better long term caching, size: numeric ids focused on minimal initial download size, false: no algorithm used, as custom one can be provided via plugin).'[39m,
            enum: [36m[Array][39m
          },
          noEmitOnErrors: {
            description: [32m"Avoid emitting assets when errors occur (deprecated: use 'emitOnErrors' instead)."[39m,
            type: [32m'boolean'[39m,
            cli: [36m[Object][39m
          },
          nodeEnv: {
            description: [32m'Set process.env.NODE_ENV to a specific value.'[39m,
            anyOf: [36m[Array][39m
          },
          portableRecords: {
            description: [32m'Generate records with relative paths to be able to move the context folder.'[39m,
            type: [32m'boolean'[39m
          },
          providedExports: {
            description: [32m'Figure out which exports are provided by modules to generate more efficient code.'[39m,
            type: [32m'boolean'[39m
          },
          realContentHash: {
            description: [32m'Use real [contenthash] based on final content of the assets.'[39m,
            type: [32m'boolean'[39m
          },
          removeAvailableModules: {
            description: [32m'Removes modules from chunks when these modules are already included in all parents.'[39m,
            type: [32m'boolean'[39m
          },
          removeEmptyChunks: {
            description: [32m'Remove chunks which are empty.'[39m,
            type: [32m'boolean'[39m
          },
          runtimeChunk: { [32m'$ref'[39m: [32m'#/definitions/OptimizationRuntimeChunk'[39m },
          sideEffects: {
            description: [32m"Skip over modules which contain no side effects when exports are not used (false: disabled, 'flag': only use manually placed side effects flag, true: also analyse source code for side effects)."[39m,
            anyOf: [36m[Array][39m
          },
          splitChunks: {
            description: [32m'Optimize duplication and caching by splitting chunks by shared modules and cache group.'[39m,
            anyOf: [36m[Array][39m
          },
          usedExports: {
            description: [32m'Figure out which exports are used by modules to mangle export names, omit unused exports and generate more efficient code (true: analyse used exports for each runtime, "global": analyse exports globally for all runtimes combined).'[39m,
            anyOf: [36m[Array][39m
          }
        }
      },
      OptimizationRuntimeChunk: {
        description: [32m'Create an additional chunk which contains only the webpack runtime and chunk hash maps.'[39m,
        anyOf: [
          { enum: [36m[Array][39m },
          { type: [32m'boolean'[39m },
          {
            type: [32m'object'[39m,
            additionalProperties: [33mfalse[39m,
            properties: [36m[Object][39m
          }
        ]
      },
      OptimizationRuntimeChunkNormalized: {
        description: [32m'Create an additional chunk which contains only the webpack runtime and chunk hash maps.'[39m,
        anyOf: [
          { enum: [36m[Array][39m },
          {
            type: [32m'object'[39m,
            additionalProperties: [33mfalse[39m,
            properties: [36m[Object][39m
          }
        ]
      },
      OptimizationSplitChunksCacheGroup: {
        description: [32m'Options object for describing behavior of a cache group selecting modules that should be cached together.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          automaticNameDelimiter: {
            description: [32m'Sets the name delimiter for created chunks.'[39m,
            type: [32m'string'[39m,
            minLength: [33m1[39m
          },
          chunks: {
            description: [32m'Select chunks for determining cache group content (defaults to "initial", "initial" and "all" requires adding these chunks to the HTML).'[39m,
            anyOf: [36m[Array][39m
          },
          enforce: {
            description: [32m'Ignore minimum size, minimum chunks and maximum requests and always create chunks for this cache group.'[39m,
            type: [32m'boolean'[39m
          },
          enforceSizeThreshold: {
            description: [32m'Size threshold at which splitting is enforced and other restrictions (minRemainingSize, maxAsyncRequests, maxInitialRequests) are ignored.'[39m,
            oneOf: [36m[Array][39m
          },
          filename: {
            description: [32m'Sets the template for the filename for created chunks.'[39m,
            anyOf: [36m[Array][39m
          },
          idHint: {
            description: [32m'Sets the hint for chunk id.'[39m,
            type: [32m'string'[39m
          },
          layer: {
            description: [32m'Assign modules to a cache group by module layer.'[39m,
            anyOf: [36m[Array][39m
          },
          maxAsyncRequests: {
            description: [32m'Maximum number of requests which are accepted for on-demand loading.'[39m,
            type: [32m'number'[39m,
            minimum: [33m1[39m
          },
          maxAsyncSize: {
            description: [32m'Maximal size hint for the on-demand chunks.'[39m,
            oneOf: [36m[Array][39m
          },
          maxInitialRequests: {
            description: [32m'Maximum number of initial chunks which are accepted for an entry point.'[39m,
            type: [32m'number'[39m,
            minimum: [33m1[39m
          },
          maxInitialSize: {
            description: [32m'Maximal size hint for the initial chunks.'[39m,
            oneOf: [36m[Array][39m
          },
          maxSize: {
            description: [32m'Maximal size hint for the created chunks.'[39m,
            oneOf: [36m[Array][39m
          },
          minChunks: {
            description: [32m"Minimum number of times a module has to be duplicated until it's considered for splitting."[39m,
            type: [32m'number'[39m,
            minimum: [33m1[39m
          },
          minRemainingSize: {
            description: [32m'Minimal size for the chunks the stay after moving the modules to a new chunk.'[39m,
            oneOf: [36m[Array][39m
          },
          minSize: {
            description: [32m'Minimal size for the created chunk.'[39m,
            oneOf: [36m[Array][39m
          },
          minSizeReduction: {
            description: [32m'Minimum size reduction due to the created chunk.'[39m,
            oneOf: [36m[Array][39m
          },
          name: {
            description: [32m'Give chunks for this cache group a name (chunks with equal name are merged).'[39m,
            anyOf: [36m[Array][39m
          },
          priority: {
            description: [32m'Priority of this cache group.'[39m,
            type: [32m'number'[39m
          },
          reuseExistingChunk: {
            description: [32m'Try to reuse existing chunk (with name) when it has matching modules.'[39m,
            type: [32m'boolean'[39m
          },
          test: {
            description: [32m'Assign modules to a cache group by module name.'[39m,
            anyOf: [36m[Array][39m
          },
          type: {
            description: [32m'Assign modules to a cache group by module type.'[39m,
            anyOf: [36m[Array][39m
          },
          usedExports: {
            description: [32m'Compare used exports when checking common modules. Modules will only be put in the same chunk when exports are equal.'[39m,
            type: [32m'boolean'[39m
          }
        }
      },
      OptimizationSplitChunksGetCacheGroups: {
        description: [32m'A function returning cache groups.'[39m,
        instanceof: [32m'Function'[39m,
        tsType: [32m"((module: import('../lib/Module')) => OptimizationSplitChunksCacheGroup | OptimizationSplitChunksCacheGroup[] | void)"[39m
      },
      OptimizationSplitChunksOptions: {
        description: [32m'Options object for splitting chunks into smaller chunks.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          automaticNameDelimiter: {
            description: [32m'Sets the name delimiter for created chunks.'[39m,
            type: [32m'string'[39m,
            minLength: [33m1[39m
          },
          cacheGroups: {
            description: [32m"Assign modules to a cache group (modules from different cache groups are tried to keep in separate chunks, default categories: 'default', 'defaultVendors')."[39m,
            type: [32m'object'[39m,
            additionalProperties: [36m[Object][39m,
            not: [36m[Object][39m
          },
          chunks: {
            description: [32m'Select chunks for determining shared modules (defaults to "async", "initial" and "all" requires adding these chunks to the HTML).'[39m,
            anyOf: [36m[Array][39m
          },
          defaultSizeTypes: {
            description: [32m'Sets the size types which are used when a number is used for sizes.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m,
            minItems: [33m1[39m
          },
          enforceSizeThreshold: {
            description: [32m'Size threshold at which splitting is enforced and other restrictions (minRemainingSize, maxAsyncRequests, maxInitialRequests) are ignored.'[39m,
            oneOf: [36m[Array][39m
          },
          fallbackCacheGroup: {
            description: [32m'Options for modules not selected by any other cache group.'[39m,
            type: [32m'object'[39m,
            additionalProperties: [33mfalse[39m,
            properties: [36m[Object][39m
          },
          filename: {
            description: [32m'Sets the template for the filename for created chunks.'[39m,
            anyOf: [36m[Array][39m
          },
          hidePathInfo: {
            description: [32m'Prevents exposing path info when creating names for parts splitted by maxSize.'[39m,
            type: [32m'boolean'[39m
          },
          maxAsyncRequests: {
            description: [32m'Maximum number of requests which are accepted for on-demand loading.'[39m,
            type: [32m'number'[39m,
            minimum: [33m1[39m
          },
          maxAsyncSize: {
            description: [32m'Maximal size hint for the on-demand chunks.'[39m,
            oneOf: [36m[Array][39m
          },
          maxInitialRequests: {
            description: [32m'Maximum number of initial chunks which are accepted for an entry point.'[39m,
            type: [32m'number'[39m,
            minimum: [33m1[39m
          },
          maxInitialSize: {
            description: [32m'Maximal size hint for the initial chunks.'[39m,
            oneOf: [36m[Array][39m
          },
          maxSize: {
            description: [32m'Maximal size hint for the created chunks.'[39m,
            oneOf: [36m[Array][39m
          },
          minChunks: {
            description: [32m"Minimum number of times a module has to be duplicated until it's considered for splitting."[39m,
            type: [32m'number'[39m,
            minimum: [33m1[39m
          },
          minRemainingSize: {
            description: [32m'Minimal size for the chunks the stay after moving the modules to a new chunk.'[39m,
            oneOf: [36m[Array][39m
          },
          minSize: {
            description: [32m'Minimal size for the created chunks.'[39m,
            oneOf: [36m[Array][39m
          },
          minSizeReduction: {
            description: [32m'Minimum size reduction due to the created chunk.'[39m,
            oneOf: [36m[Array][39m
          },
          name: {
            description: [32m'Give chunks created a name (chunks with equal name are merged).'[39m,
            anyOf: [36m[Array][39m
          },
          usedExports: {
            description: [32m'Compare used exports when checking common modules. Modules will only be put in the same chunk when exports are equal.'[39m,
            type: [32m'boolean'[39m
          }
        }
      },
      OptimizationSplitChunksSizes: {
        description: [32m'Size description for limits.'[39m,
        anyOf: [
          {
            description: [32m'Size of the javascript part of the chunk.'[39m,
            type: [32m'number'[39m,
            minimum: [33m0[39m
          },
          {
            description: [32m'Specify size limits per size type.'[39m,
            type: [32m'object'[39m,
            additionalProperties: [36m[Object][39m
          }
        ]
      },
      Output: {
        description: [32m'Options affecting the output of the compilation. `output` options tell webpack how to write the compiled files to disk.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          amdContainer: { cli: [36m[Object][39m, oneOf: [36m[Array][39m },
          assetModuleFilename: { [32m'$ref'[39m: [32m'#/definitions/AssetModuleFilename'[39m },
          asyncChunks: {
            description: [32m'Enable/disable creating async chunks that are loaded on demand.'[39m,
            type: [32m'boolean'[39m
          },
          auxiliaryComment: { cli: [36m[Object][39m, oneOf: [36m[Array][39m },
          charset: { [32m'$ref'[39m: [32m'#/definitions/Charset'[39m },
          chunkFilename: { [32m'$ref'[39m: [32m'#/definitions/ChunkFilename'[39m },
          chunkFormat: { [32m'$ref'[39m: [32m'#/definitions/ChunkFormat'[39m },
          chunkLoadTimeout: { [32m'$ref'[39m: [32m'#/definitions/ChunkLoadTimeout'[39m },
          chunkLoading: { [32m'$ref'[39m: [32m'#/definitions/ChunkLoading'[39m },
          chunkLoadingGlobal: { [32m'$ref'[39m: [32m'#/definitions/ChunkLoadingGlobal'[39m },
          clean: { [32m'$ref'[39m: [32m'#/definitions/Clean'[39m },
          compareBeforeEmit: { [32m'$ref'[39m: [32m'#/definitions/CompareBeforeEmit'[39m },
          crossOriginLoading: { [32m'$ref'[39m: [32m'#/definitions/CrossOriginLoading'[39m },
          cssChunkFilename: { [32m'$ref'[39m: [32m'#/definitions/CssChunkFilename'[39m },
          cssFilename: { [32m'$ref'[39m: [32m'#/definitions/CssFilename'[39m },
          devtoolFallbackModuleFilenameTemplate: {
            [32m'$ref'[39m: [32m'#/definitions/DevtoolFallbackModuleFilenameTemplate'[39m
          },
          devtoolModuleFilenameTemplate: { [32m'$ref'[39m: [32m'#/definitions/DevtoolModuleFilenameTemplate'[39m },
          devtoolNamespace: { [32m'$ref'[39m: [32m'#/definitions/DevtoolNamespace'[39m },
          enabledChunkLoadingTypes: { [32m'$ref'[39m: [32m'#/definitions/EnabledChunkLoadingTypes'[39m },
          enabledLibraryTypes: { [32m'$ref'[39m: [32m'#/definitions/EnabledLibraryTypes'[39m },
          enabledWasmLoadingTypes: { [32m'$ref'[39m: [32m'#/definitions/EnabledWasmLoadingTypes'[39m },
          environment: { [32m'$ref'[39m: [32m'#/definitions/Environment'[39m },
          filename: { [32m'$ref'[39m: [32m'#/definitions/Filename'[39m },
          globalObject: { [32m'$ref'[39m: [32m'#/definitions/GlobalObject'[39m },
          hashDigest: { [32m'$ref'[39m: [32m'#/definitions/HashDigest'[39m },
          hashDigestLength: { [32m'$ref'[39m: [32m'#/definitions/HashDigestLength'[39m },
          hashFunction: { [32m'$ref'[39m: [32m'#/definitions/HashFunction'[39m },
          hashSalt: { [32m'$ref'[39m: [32m'#/definitions/HashSalt'[39m },
          hotUpdateChunkFilename: { [32m'$ref'[39m: [32m'#/definitions/HotUpdateChunkFilename'[39m },
          hotUpdateGlobal: { [32m'$ref'[39m: [32m'#/definitions/HotUpdateGlobal'[39m },
          hotUpdateMainFilename: { [32m'$ref'[39m: [32m'#/definitions/HotUpdateMainFilename'[39m },
          ignoreBrowserWarnings: {
            description: [32m'Ignore warnings in the browser.'[39m,
            type: [32m'boolean'[39m
          },
          iife: { [32m'$ref'[39m: [32m'#/definitions/Iife'[39m },
          importFunctionName: { [32m'$ref'[39m: [32m'#/definitions/ImportFunctionName'[39m },
          importMetaName: { [32m'$ref'[39m: [32m'#/definitions/ImportMetaName'[39m },
          library: { [32m'$ref'[39m: [32m'#/definitions/Library'[39m },
          libraryExport: { cli: [36m[Object][39m, oneOf: [36m[Array][39m },
          libraryTarget: { cli: [36m[Object][39m, oneOf: [36m[Array][39m },
          module: { [32m'$ref'[39m: [32m'#/definitions/OutputModule'[39m },
          path: { [32m'$ref'[39m: [32m'#/definitions/Path'[39m },
          pathinfo: { [32m'$ref'[39m: [32m'#/definitions/Pathinfo'[39m },
          publicPath: { [32m'$ref'[39m: [32m'#/definitions/PublicPath'[39m },
          scriptType: { [32m'$ref'[39m: [32m'#/definitions/ScriptType'[39m },
          sourceMapFilename: { [32m'$ref'[39m: [32m'#/definitions/SourceMapFilename'[39m },
          sourcePrefix: { [32m'$ref'[39m: [32m'#/definitions/SourcePrefix'[39m },
          strictModuleErrorHandling: { [32m'$ref'[39m: [32m'#/definitions/StrictModuleErrorHandling'[39m },
          strictModuleExceptionHandling: { [32m'$ref'[39m: [32m'#/definitions/StrictModuleExceptionHandling'[39m },
          trustedTypes: {
            description: [32m"Use a Trusted Types policy to create urls for chunks. 'output.uniqueName' is used a default policy name. Passing a string sets a custom policy name."[39m,
            anyOf: [36m[Array][39m
          },
          umdNamedDefine: { cli: [36m[Object][39m, oneOf: [36m[Array][39m },
          uniqueName: { [32m'$ref'[39m: [32m'#/definitions/UniqueName'[39m },
          wasmLoading: { [32m'$ref'[39m: [32m'#/definitions/WasmLoading'[39m },
          webassemblyModuleFilename: { [32m'$ref'[39m: [32m'#/definitions/WebassemblyModuleFilename'[39m },
          workerChunkLoading: { [32m'$ref'[39m: [32m'#/definitions/ChunkLoading'[39m },
          workerPublicPath: { [32m'$ref'[39m: [32m'#/definitions/WorkerPublicPath'[39m },
          workerWasmLoading: { [32m'$ref'[39m: [32m'#/definitions/WasmLoading'[39m }
        }
      },
      OutputModule: {
        description: [32m'Output javascript files as module source type.'[39m,
        type: [32m'boolean'[39m
      },
      OutputNormalized: {
        description: [32m'Normalized options affecting the output of the compilation. `output` options tell webpack how to write the compiled files to disk.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          assetModuleFilename: { [32m'$ref'[39m: [32m'#/definitions/AssetModuleFilename'[39m },
          asyncChunks: {
            description: [32m'Enable/disable creating async chunks that are loaded on demand.'[39m,
            type: [32m'boolean'[39m
          },
          charset: { [32m'$ref'[39m: [32m'#/definitions/Charset'[39m },
          chunkFilename: { [32m'$ref'[39m: [32m'#/definitions/ChunkFilename'[39m },
          chunkFormat: { [32m'$ref'[39m: [32m'#/definitions/ChunkFormat'[39m },
          chunkLoadTimeout: { [32m'$ref'[39m: [32m'#/definitions/ChunkLoadTimeout'[39m },
          chunkLoading: { [32m'$ref'[39m: [32m'#/definitions/ChunkLoading'[39m },
          chunkLoadingGlobal: { [32m'$ref'[39m: [32m'#/definitions/ChunkLoadingGlobal'[39m },
          clean: { [32m'$ref'[39m: [32m'#/definitions/Clean'[39m },
          compareBeforeEmit: { [32m'$ref'[39m: [32m'#/definitions/CompareBeforeEmit'[39m },
          crossOriginLoading: { [32m'$ref'[39m: [32m'#/definitions/CrossOriginLoading'[39m },
          cssChunkFilename: { [32m'$ref'[39m: [32m'#/definitions/CssChunkFilename'[39m },
          cssFilename: { [32m'$ref'[39m: [32m'#/definitions/CssFilename'[39m },
          devtoolFallbackModuleFilenameTemplate: {
            [32m'$ref'[39m: [32m'#/definitions/DevtoolFallbackModuleFilenameTemplate'[39m
          },
          devtoolModuleFilenameTemplate: { [32m'$ref'[39m: [32m'#/definitions/DevtoolModuleFilenameTemplate'[39m },
          devtoolNamespace: { [32m'$ref'[39m: [32m'#/definitions/DevtoolNamespace'[39m },
          enabledChunkLoadingTypes: { [32m'$ref'[39m: [32m'#/definitions/EnabledChunkLoadingTypes'[39m },
          enabledLibraryTypes: { [32m'$ref'[39m: [32m'#/definitions/EnabledLibraryTypes'[39m },
          enabledWasmLoadingTypes: { [32m'$ref'[39m: [32m'#/definitions/EnabledWasmLoadingTypes'[39m },
          environment: { [32m'$ref'[39m: [32m'#/definitions/Environment'[39m },
          filename: { [32m'$ref'[39m: [32m'#/definitions/Filename'[39m },
          globalObject: { [32m'$ref'[39m: [32m'#/definitions/GlobalObject'[39m },
          hashDigest: { [32m'$ref'[39m: [32m'#/definitions/HashDigest'[39m },
          hashDigestLength: { [32m'$ref'[39m: [32m'#/definitions/HashDigestLength'[39m },
          hashFunction: { [32m'$ref'[39m: [32m'#/definitions/HashFunction'[39m },
          hashSalt: { [32m'$ref'[39m: [32m'#/definitions/HashSalt'[39m },
          hotUpdateChunkFilename: { [32m'$ref'[39m: [32m'#/definitions/HotUpdateChunkFilename'[39m },
          hotUpdateGlobal: { [32m'$ref'[39m: [32m'#/definitions/HotUpdateGlobal'[39m },
          hotUpdateMainFilename: { [32m'$ref'[39m: [32m'#/definitions/HotUpdateMainFilename'[39m },
          ignoreBrowserWarnings: {
            description: [32m'Ignore warnings in the browser.'[39m,
            type: [32m'boolean'[39m
          },
          iife: { [32m'$ref'[39m: [32m'#/definitions/Iife'[39m },
          importFunctionName: { [32m'$ref'[39m: [32m'#/definitions/ImportFunctionName'[39m },
          importMetaName: { [32m'$ref'[39m: [32m'#/definitions/ImportMetaName'[39m },
          library: { [32m'$ref'[39m: [32m'#/definitions/LibraryOptions'[39m },
          module: { [32m'$ref'[39m: [32m'#/definitions/OutputModule'[39m },
          path: { [32m'$ref'[39m: [32m'#/definitions/Path'[39m },
          pathinfo: { [32m'$ref'[39m: [32m'#/definitions/Pathinfo'[39m },
          publicPath: { [32m'$ref'[39m: [32m'#/definitions/PublicPath'[39m },
          scriptType: { [32m'$ref'[39m: [32m'#/definitions/ScriptType'[39m },
          sourceMapFilename: { [32m'$ref'[39m: [32m'#/definitions/SourceMapFilename'[39m },
          sourcePrefix: { [32m'$ref'[39m: [32m'#/definitions/SourcePrefix'[39m },
          strictModuleErrorHandling: { [32m'$ref'[39m: [32m'#/definitions/StrictModuleErrorHandling'[39m },
          strictModuleExceptionHandling: { [32m'$ref'[39m: [32m'#/definitions/StrictModuleExceptionHandling'[39m },
          trustedTypes: { [32m'$ref'[39m: [32m'#/definitions/TrustedTypes'[39m },
          uniqueName: { [32m'$ref'[39m: [32m'#/definitions/UniqueName'[39m },
          wasmLoading: { [32m'$ref'[39m: [32m'#/definitions/WasmLoading'[39m },
          webassemblyModuleFilename: { [32m'$ref'[39m: [32m'#/definitions/WebassemblyModuleFilename'[39m },
          workerChunkLoading: { [32m'$ref'[39m: [32m'#/definitions/ChunkLoading'[39m },
          workerPublicPath: { [32m'$ref'[39m: [32m'#/definitions/WorkerPublicPath'[39m },
          workerWasmLoading: { [32m'$ref'[39m: [32m'#/definitions/WasmLoading'[39m }
        },
        required: [
          [32m'environment'[39m,
          [32m'enabledChunkLoadingTypes'[39m,
          [32m'enabledLibraryTypes'[39m,
          [32m'enabledWasmLoadingTypes'[39m
        ]
      },
      Parallelism: {
        description: [32m'The number of parallel processed modules in the compilation.'[39m,
        type: [32m'number'[39m,
        minimum: [33m1[39m
      },
      ParserOptionsByModuleType: {
        description: [32m'Specify options for each parser.'[39m,
        type: [32m'object'[39m,
        additionalProperties: {
          description: [32m'Options for parsing.'[39m,
          type: [32m'object'[39m,
          additionalProperties: [33mtrue[39m
        },
        properties: {
          asset: { [32m'$ref'[39m: [32m'#/definitions/AssetParserOptions'[39m },
          [32m'asset/inline'[39m: { [32m'$ref'[39m: [32m'#/definitions/EmptyParserOptions'[39m },
          [32m'asset/resource'[39m: { [32m'$ref'[39m: [32m'#/definitions/EmptyParserOptions'[39m },
          [32m'asset/source'[39m: { [32m'$ref'[39m: [32m'#/definitions/EmptyParserOptions'[39m },
          css: { [32m'$ref'[39m: [32m'#/definitions/CssParserOptions'[39m },
          [32m'css/auto'[39m: { [32m'$ref'[39m: [32m'#/definitions/CssAutoParserOptions'[39m },
          [32m'css/global'[39m: { [32m'$ref'[39m: [32m'#/definitions/CssGlobalParserOptions'[39m },
          [32m'css/module'[39m: { [32m'$ref'[39m: [32m'#/definitions/CssModuleParserOptions'[39m },
          javascript: { [32m'$ref'[39m: [32m'#/definitions/JavascriptParserOptions'[39m },
          [32m'javascript/auto'[39m: { [32m'$ref'[39m: [32m'#/definitions/JavascriptParserOptions'[39m },
          [32m'javascript/dynamic'[39m: { [32m'$ref'[39m: [32m'#/definitions/JavascriptParserOptions'[39m },
          [32m'javascript/esm'[39m: { [32m'$ref'[39m: [32m'#/definitions/JavascriptParserOptions'[39m }
        }
      },
      Path: {
        description: [32m'The output directory as **absolute path** (required).'[39m,
        type: [32m'string'[39m,
        absolutePath: [33mtrue[39m
      },
      Pathinfo: {
        description: [32m'Include comments with information about the modules.'[39m,
        anyOf: [ { enum: [36m[Array][39m }, { type: [32m'boolean'[39m } ]
      },
      Performance: {
        description: [32m'Configuration for web performance recommendations.'[39m,
        anyOf: [
          { enum: [36m[Array][39m },
          { [32m'$ref'[39m: [32m'#/definitions/PerformanceOptions'[39m }
        ]
      },
      PerformanceOptions: {
        description: [32m'Configuration object for web performance recommendations.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          assetFilter: {
            description: [32m'Filter function to select assets that are checked.'[39m,
            instanceof: [32m'Function'[39m,
            tsType: [32m'Function'[39m
          },
          hints: {
            description: [32m'Sets the format of the hints: warnings, errors or nothing at all.'[39m,
            enum: [36m[Array][39m
          },
          maxAssetSize: {
            description: [32m'File size limit (in bytes) when exceeded, that webpack will provide performance hints.'[39m,
            type: [32m'number'[39m
          },
          maxEntrypointSize: {
            description: [32m'Total size of an entry point (in bytes).'[39m,
            type: [32m'number'[39m
          }
        }
      },
      Plugins: {
        description: [32m'Add additional plugins to the compiler.'[39m,
        type: [32m'array'[39m,
        items: {
          description: [32m'Plugin of type object or instanceof Function.'[39m,
          anyOf: [ [36m[Object][39m, [36m[Object][39m, [36m[Object][39m ]
        }
      },
      Profile: {
        description: [32m'Capture timing information for each module.'[39m,
        type: [32m'boolean'[39m
      },
      PublicPath: {
        description: [32m"The 'publicPath' specifies the public URL address of the output files when referenced in a browser."[39m,
        anyOf: [
          { enum: [36m[Array][39m },
          { [32m'$ref'[39m: [32m'#/definitions/RawPublicPath'[39m }
        ]
      },
      RawPublicPath: {
        description: [32m"The 'publicPath' specifies the public URL address of the output files when referenced in a browser."[39m,
        anyOf: [
          { type: [32m'string'[39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m'((pathData: import("../lib/Compilation").PathData, assetInfo?: import("../lib/Compilation").AssetInfo) => string)'[39m
          }
        ]
      },
      RecordsInputPath: {
        description: [32m'Store compiler state to a json file.'[39m,
        anyOf: [ { enum: [36m[Array][39m }, { type: [32m'string'[39m, absolutePath: [33mtrue[39m } ]
      },
      RecordsOutputPath: {
        description: [32m'Load compiler state from a json file.'[39m,
        anyOf: [ { enum: [36m[Array][39m }, { type: [32m'string'[39m, absolutePath: [33mtrue[39m } ]
      },
      RecordsPath: {
        description: [32m'Store/Load compiler state from/to a json file. This will result in persistent ids of modules and chunks. An absolute path is expected. `recordsPath` is used for `recordsInputPath` and `recordsOutputPath` if they left undefined.'[39m,
        anyOf: [ { enum: [36m[Array][39m }, { type: [32m'string'[39m, absolutePath: [33mtrue[39m } ]
      },
      Resolve: {
        description: [32m'Options for the resolver.'[39m,
        oneOf: [ { [32m'$ref'[39m: [32m'#/definitions/ResolveOptions'[39m } ]
      },
      ResolveAlias: {
        description: [32m'Redirect module requests.'[39m,
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m },
          { type: [32m'object'[39m, additionalProperties: [36m[Object][39m }
        ]
      },
      ResolveLoader: {
        description: [32m'Options for the resolver when resolving loaders.'[39m,
        oneOf: [ { [32m'$ref'[39m: [32m'#/definitions/ResolveOptions'[39m } ]
      },
      ResolveOptions: {
        description: [32m'Options object for resolving requests.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          alias: { [32m'$ref'[39m: [32m'#/definitions/ResolveAlias'[39m },
          aliasFields: {
            description: [32m'Fields in the description file (usually package.json) which are used to redirect requests inside the module.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          byDependency: {
            description: [32m'Extra resolve options per dependency category. Typical categories are "commonjs", "amd", "esm".'[39m,
            type: [32m'object'[39m,
            additionalProperties: [36m[Object][39m
          },
          cache: {
            description: [32m'Enable caching of successfully resolved requests (cache entries are revalidated).'[39m,
            type: [32m'boolean'[39m
          },
          cachePredicate: {
            description: [32m'Predicate function to decide which requests should be cached.'[39m,
            instanceof: [32m'Function'[39m,
            tsType: [32m"((request: import('enhanced-resolve').ResolveRequest) => boolean)"[39m
          },
          cacheWithContext: {
            description: [32m'Include the context information in the cache identifier when caching.'[39m,
            type: [32m'boolean'[39m
          },
          conditionNames: {
            description: [32m'Condition names for exports field entry point.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          descriptionFiles: {
            description: [32m'Filenames used to find a description file (like a package.json).'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          enforceExtension: {
            description: [32m'Enforce the resolver to use one of the extensions from the extensions option (User must specify requests without extension).'[39m,
            type: [32m'boolean'[39m
          },
          exportsFields: {
            description: [32m'Field names from the description file (usually package.json) which are used to provide entry points of a package.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          extensionAlias: {
            description: [32m'An object which maps extension to extension aliases.'[39m,
            type: [32m'object'[39m,
            additionalProperties: [36m[Object][39m
          },
          extensions: {
            description: [32m'Extensions added to the request when trying to find the file.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          fallback: {
            description: [32m'Redirect module requests when normal resolving fails.'[39m,
            oneOf: [36m[Array][39m
          },
          fileSystem: {
            description: [32m'Filesystem for the resolver.'[39m,
            tsType: [32m"(import('../lib/util/fs').InputFileSystem)"[39m
          },
          fullySpecified: {
            description: [32m"Treats the request specified by the user as fully specified, meaning no extensions are added and the mainFiles in directories are not resolved (This doesn't affect requests from mainFields, aliasFields or aliases)."[39m,
            type: [32m'boolean'[39m
          },
          importsFields: {
            description: [32m'Field names from the description file (usually package.json) which are used to provide internal request of a package (requests starting with # are considered as internal).'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          mainFields: {
            description: [32m'Field names from the description file (package.json) which are used to find the default entry point.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          mainFiles: {
            description: [32m'Filenames used to find the default entry point if there is no description file or main field.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          modules: {
            description: [32m'Folder names or directory paths where to find modules.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          plugins: {
            description: [32m'Plugins for the resolver.'[39m,
            type: [32m'array'[39m,
            cli: [36m[Object][39m,
            items: [36m[Object][39m
          },
          preferAbsolute: {
            description: [32m"Prefer to resolve server-relative URLs (starting with '/') as absolute paths before falling back to resolve in 'resolve.roots'."[39m,
            type: [32m'boolean'[39m
          },
          preferRelative: {
            description: [32m'Prefer to resolve module requests as relative request and fallback to resolving as module.'[39m,
            type: [32m'boolean'[39m
          },
          resolver: {
            description: [32m'Custom resolver.'[39m,
            tsType: [32m"(import('enhanced-resolve').Resolver)"[39m
          },
          restrictions: {
            description: [32m'A list of resolve restrictions. Resolve results must fulfill all of these restrictions to resolve successfully. Other resolve paths are taken when restrictions are not met.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          roots: {
            description: [32m"A list of directories in which requests that are server-relative URLs (starting with '/') are resolved."[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          symlinks: {
            description: [32m'Enable resolving symlinks to the original location.'[39m,
            type: [32m'boolean'[39m
          },
          unsafeCache: {
            description: [32m'Enable caching of successfully resolved requests (cache entries are not revalidated).'[39m,
            anyOf: [36m[Array][39m
          },
          useSyncFileSystemCalls: {
            description: [32m'Use synchronous filesystem calls for the resolver.'[39m,
            type: [32m'boolean'[39m
          }
        }
      },
      ResolvePluginInstance: {
        description: [32m'Plugin instance.'[39m,
        anyOf: [
          {
            type: [32m'object'[39m,
            additionalProperties: [33mtrue[39m,
            properties: [36m[Object][39m,
            required: [36m[Array][39m
          },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m"((this: import('enhanced-resolve').Resolver, arg1: import('enhanced-resolve').Resolver) => void)"[39m
          }
        ]
      },
      RuleSetCondition: {
        description: [32m'A condition matcher.'[39m,
        cli: { helper: [33mtrue[39m },
        anyOf: [
          { instanceof: [32m'RegExp'[39m, tsType: [32m'RegExp'[39m },
          { type: [32m'string'[39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m'((value: string) => boolean)'[39m
          },
          { [32m'$ref'[39m: [32m'#/definitions/RuleSetLogicalConditions'[39m },
          { [32m'$ref'[39m: [32m'#/definitions/RuleSetConditions'[39m }
        ]
      },
      RuleSetConditionAbsolute: {
        description: [32m'A condition matcher matching an absolute path.'[39m,
        cli: { helper: [33mtrue[39m },
        anyOf: [
          { instanceof: [32m'RegExp'[39m, tsType: [32m'RegExp'[39m },
          { type: [32m'string'[39m, absolutePath: [33mtrue[39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m'((value: string) => boolean)'[39m
          },
          { [32m'$ref'[39m: [32m'#/definitions/RuleSetLogicalConditionsAbsolute'[39m },
          { [32m'$ref'[39m: [32m'#/definitions/RuleSetConditionsAbsolute'[39m }
        ]
      },
      RuleSetConditionOrConditions: {
        description: [32m'One or multiple rule conditions.'[39m,
        cli: { helper: [33mtrue[39m },
        anyOf: [
          { [32m'$ref'[39m: [32m'#/definitions/RuleSetCondition'[39m },
          { [32m'$ref'[39m: [32m'#/definitions/RuleSetConditions'[39m }
        ]
      },
      RuleSetConditionOrConditionsAbsolute: {
        description: [32m'One or multiple rule conditions matching an absolute path.'[39m,
        cli: { helper: [33mtrue[39m },
        anyOf: [
          { [32m'$ref'[39m: [32m'#/definitions/RuleSetConditionAbsolute'[39m },
          { [32m'$ref'[39m: [32m'#/definitions/RuleSetConditionsAbsolute'[39m }
        ]
      },
      RuleSetConditions: {
        description: [32m'A list of rule conditions.'[39m,
        type: [32m'array'[39m,
        items: { description: [32m'A rule condition.'[39m, oneOf: [ [36m[Object][39m ] }
      },
      RuleSetConditionsAbsolute: {
        description: [32m'A list of rule conditions matching an absolute path.'[39m,
        type: [32m'array'[39m,
        items: {
          description: [32m'A rule condition matching an absolute path.'[39m,
          oneOf: [ [36m[Object][39m ]
        }
      },
      RuleSetLoader: {
        description: [32m'A loader request.'[39m,
        type: [32m'string'[39m,
        minLength: [33m1[39m
      },
      RuleSetLoaderOptions: {
        description: [32m'Options passed to a loader.'[39m,
        anyOf: [ { type: [32m'string'[39m }, { type: [32m'object'[39m } ]
      },
      RuleSetLogicalConditions: {
        description: [32m'Logic operators used in a condition matcher.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          and: { description: [32m'Logical AND.'[39m, oneOf: [36m[Array][39m },
          not: { description: [32m'Logical NOT.'[39m, oneOf: [36m[Array][39m },
          or: { description: [32m'Logical OR.'[39m, oneOf: [36m[Array][39m }
        }
      },
      RuleSetLogicalConditionsAbsolute: {
        description: [32m'Logic operators used in a condition matcher.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          and: { description: [32m'Logical AND.'[39m, oneOf: [36m[Array][39m },
          not: { description: [32m'Logical NOT.'[39m, oneOf: [36m[Array][39m },
          or: { description: [32m'Logical OR.'[39m, oneOf: [36m[Array][39m }
        }
      },
      RuleSetRule: {
        description: [32m'A rule description with conditions and effects for modules.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          assert: {
            description: [32m'Match on import assertions of the dependency.'[39m,
            type: [32m'object'[39m,
            additionalProperties: [36m[Object][39m
          },
          compiler: {
            description: [32m'Match the child compiler name.'[39m,
            oneOf: [36m[Array][39m
          },
          dependency: { description: [32m'Match dependency type.'[39m, oneOf: [36m[Array][39m },
          descriptionData: {
            description: [32m'Match values of properties in the description file (usually package.json).'[39m,
            type: [32m'object'[39m,
            additionalProperties: [36m[Object][39m
          },
          enforce: {
            description: [32m'Enforce this rule as pre or post step.'[39m,
            enum: [36m[Array][39m
          },
          exclude: {
            description: [32m'Shortcut for resource.exclude.'[39m,
            oneOf: [36m[Array][39m
          },
          generator: {
            description: [32m'The options for the module generator.'[39m,
            type: [32m'object'[39m
          },
          include: {
            description: [32m'Shortcut for resource.include.'[39m,
            oneOf: [36m[Array][39m
          },
          issuer: {
            description: [32m'Match the issuer of the module (The module pointing to this module).'[39m,
            oneOf: [36m[Array][39m
          },
          issuerLayer: {
            description: [32m'Match layer of the issuer of this module (The module pointing to this module).'[39m,
            oneOf: [36m[Array][39m
          },
          layer: {
            description: [32m'Specifies the layer in which the module should be placed in.'[39m,
            type: [32m'string'[39m
          },
          loader: { description: [32m'Shortcut for use.loader.'[39m, oneOf: [36m[Array][39m },
          mimetype: {
            description: [32m'Match module mimetype when load from Data URI.'[39m,
            oneOf: [36m[Array][39m
          },
          oneOf: {
            description: [32m'Only execute the first matching rule in this array.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          options: {
            description: [32m'Shortcut for use.options.'[39m,
            cli: [36m[Object][39m,
            oneOf: [36m[Array][39m
          },
          parser: {
            description: [32m'Options for parsing.'[39m,
            type: [32m'object'[39m,
            additionalProperties: [33mtrue[39m
          },
          realResource: {
            description: [32m'Match the real resource path of the module.'[39m,
            oneOf: [36m[Array][39m
          },
          resolve: {
            description: [32m'Options for the resolver.'[39m,
            type: [32m'object'[39m,
            oneOf: [36m[Array][39m
          },
          resource: {
            description: [32m'Match the resource path of the module.'[39m,
            oneOf: [36m[Array][39m
          },
          resourceFragment: {
            description: [32m'Match the resource fragment of the module.'[39m,
            oneOf: [36m[Array][39m
          },
          resourceQuery: {
            description: [32m'Match the resource query of the module.'[39m,
            oneOf: [36m[Array][39m
          },
          rules: {
            description: [32m'Match and execute these rules when this rule is matched.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          scheme: { description: [32m'Match module scheme.'[39m, oneOf: [36m[Array][39m },
          sideEffects: {
            description: [32m'Flags a module as with or without side effects.'[39m,
            type: [32m'boolean'[39m
          },
          test: {
            description: [32m'Shortcut for resource.test.'[39m,
            oneOf: [36m[Array][39m
          },
          type: {
            description: [32m'Module type to use for the module.'[39m,
            type: [32m'string'[39m
          },
          use: {
            description: [32m'Modifiers applied to the module when rule is matched.'[39m,
            oneOf: [36m[Array][39m
          },
          with: {
            description: [32m'Match on import attributes of the dependency.'[39m,
            type: [32m'object'[39m,
            additionalProperties: [36m[Object][39m
          }
        }
      },
      RuleSetRules: {
        description: [32m'A list of rules.'[39m,
        type: [32m'array'[39m,
        items: {
          description: [32m'A rule.'[39m,
          anyOf: [ [36m[Object][39m, [36m[Object][39m, [36m[Object][39m ]
        }
      },
      RuleSetUse: {
        description: [32m'A list of descriptions of loaders applied.'[39m,
        anyOf: [
          { type: [32m'array'[39m, items: [36m[Object][39m },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m'((data: { resource: string, realResource: string, resourceQuery: string, issuer: string, compiler: string }) => (Falsy | RuleSetUseItem)[])'[39m
          },
          { [32m'$ref'[39m: [32m'#/definitions/RuleSetUseItem'[39m }
        ]
      },
      RuleSetUseItem: {
        description: [32m'A description of an applied loader.'[39m,
        anyOf: [
          {
            type: [32m'object'[39m,
            additionalProperties: [33mfalse[39m,
            properties: [36m[Object][39m
          },
          {
            instanceof: [32m'Function'[39m,
            tsType: [32m'((data: object) => RuleSetUseItem | (Falsy | RuleSetUseItem)[])'[39m
          },
          { [32m'$ref'[39m: [32m'#/definitions/RuleSetLoader'[39m }
        ]
      },
      ScriptType: {
        description: [32m'This option enables loading async chunks via a custom script type, such as script type="module".'[39m,
        enum: [ [33mfalse[39m, [32m'text/javascript'[39m, [32m'module'[39m ]
      },
      SnapshotOptions: {
        description: [32m'Options affecting how file system snapshots are created and validated.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          buildDependencies: {
            description: [32m'Options for snapshotting build dependencies to determine if the whole cache need to be invalidated.'[39m,
            type: [32m'object'[39m,
            additionalProperties: [33mfalse[39m,
            properties: [36m[Object][39m
          },
          immutablePaths: {
            description: [32m'List of paths that are managed by a package manager and contain a version or hash in its path so all files are immutable.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          managedPaths: {
            description: [32m'List of paths that are managed by a package manager and can be trusted to not be modified otherwise.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          },
          module: {
            description: [32m'Options for snapshotting dependencies of modules to determine if they need to be built again.'[39m,
            type: [32m'object'[39m,
            additionalProperties: [33mfalse[39m,
            properties: [36m[Object][39m
          },
          resolve: {
            description: [32m'Options for snapshotting dependencies of request resolving to determine if requests need to be re-resolved.'[39m,
            type: [32m'object'[39m,
            additionalProperties: [33mfalse[39m,
            properties: [36m[Object][39m
          },
          resolveBuildDependencies: {
            description: [32m'Options for snapshotting the resolving of build dependencies to determine if the build dependencies need to be re-resolved.'[39m,
            type: [32m'object'[39m,
            additionalProperties: [33mfalse[39m,
            properties: [36m[Object][39m
          },
          unmanagedPaths: {
            description: [32m'List of paths that are not managed by a package manager and the contents are subject to change.'[39m,
            type: [32m'array'[39m,
            items: [36m[Object][39m
          }
        }
      },
      SourceMapFilename: {
        description: [32m"The filename of the SourceMaps for the JavaScript files. They are inside the 'output.path' directory."[39m,
        type: [32m'string'[39m,
        absolutePath: [33mfalse[39m
      },
      SourcePrefix: {
        description: [32m'Prefixes every line of the source in the bundle with this string.'[39m,
        type: [32m'string'[39m
      },
      StatsOptions: {
        description: [32m'Stats options object.'[39m,
        type: [32m'object'[39m,
        additionalProperties: [33mfalse[39m,
        properties: {
          all: {
            description: [32m'Fallback value for stats options when an option is not defined (has precedence over local webpack defaults).'[39m,
            type: [32m'boolean'[39m
          },
          assets: { description: [32m'Add assets information.'[39m, type: [32m'boolean'[39m },
          assetsSort: {
            description: [32m'Sort the assets by that field.'[39m,
            type: [32m'string'[39m
          },
          assetsSpace: {
            description: [32m'Space to display assets (groups will be collapsed to fit this space).'[39m,
            type: [32m'number'[39m
          },
          builtAt: {
            description: [32m'Add built at time information.'[39m,
            type: [32m'boolean'[39m
          },
          cached: {
            description: [32m"Add information about cached (not built) modules (deprecated: use 'cachedModules' instead)."[39m,
            type: [32m'boolean'[39m
          },
          cachedAssets: {
            description: [32m'Show cached assets (setting this to `false` only shows emitted files).'[39m,
            type: [32m'boolean'[39m
          },
          cachedModules: {
            description: [32m'Add information about cached (not built) modules.'[39m,
            type: [32m'boolean'[39m
          },
          children: { description: [32m'Add children information.'[39m, type: [32m'boolean'[39m },
          chunkGroupAuxiliary: {
            description: [32m'Display auxiliary assets in chunk groups.'[39m,
            type: [32m'boolean'[39m
          },
          chunkGroupChildren: {
            description: [32m'Display children of chunk groups.'[39m,
            type: [32m'boolean'[39m
          },
          chunkGroupMaxAssets: {
            description: [32m'Limit of assets displayed in chunk groups.'[39m,
            type: [32m'number'[39m
          },
          chunkGroups: {
            description: [32m'Display all chunk groups with the corresponding bundles.'[39m,
            type: [32m'boolean'[39m
          },
          chunkModules: {
            description: [32m'Add built modules information to chunk information.'[39m,
            type: [32m'boolean'[39m
          },
          chunkModulesSpace: {
            descr