import {
  require_dayjs_min
} from "./chunk-M2A4HNR4.js";
import {
  __commonJS
} from "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/cs.js
var require_cs = __commonJS({
  "node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/cs.js"(exports, module) {
    !function(e, n) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = n(require_dayjs_min()) : "function" == typeof define && define.amd ? define(["dayjs"], n) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_locale_cs = n(e.dayjs);
    }(exports, function(e) {
      "use strict";
      function n(e2) {
        return e2 && "object" == typeof e2 && "default" in e2 ? e2 : { default: e2 };
      }
      var t = n(e);
      function s(e2) {
        return e2 > 1 && e2 < 5 && 1 != ~~(e2 / 10);
      }
      function r(e2, n2, t2, r2) {
        var d2 = e2 + " ";
        switch (t2) {
          case "s":
            return n2 || r2 ? "pár sekund" : "pár sekundami";
          case "m":
            return n2 ? "minuta" : r2 ? "minutu" : "minutou";
          case "mm":
            return n2 || r2 ? d2 + (s(e2) ? "minuty" : "minut") : d2 + "minutami";
          case "h":
            return n2 ? "hodina" : r2 ? "hodinu" : "hodinou";
          case "hh":
            return n2 || r2 ? d2 + (s(e2) ? "hodiny" : "hodin") : d2 + "hodinami";
          case "d":
            return n2 || r2 ? "den" : "dnem";
          case "dd":
            return n2 || r2 ? d2 + (s(e2) ? "dny" : "dní") : d2 + "dny";
          case "M":
            return n2 || r2 ? "měsíc" : "měsícem";
          case "MM":
            return n2 || r2 ? d2 + (s(e2) ? "měsíce" : "měsíců") : d2 + "měsíci";
          case "y":
            return n2 || r2 ? "rok" : "rokem";
          case "yy":
            return n2 || r2 ? d2 + (s(e2) ? "roky" : "let") : d2 + "lety";
        }
      }
      var d = { name: "cs", weekdays: "neděle_pondělí_úterý_středa_čtvrtek_pátek_sobota".split("_"), weekdaysShort: "ne_po_út_st_čt_pá_so".split("_"), weekdaysMin: "ne_po_út_st_čt_pá_so".split("_"), months: "leden_únor_březen_duben_květen_červen_červenec_srpen_září_říjen_listopad_prosinec".split("_"), monthsShort: "led_úno_bře_dub_kvě_čvn_čvc_srp_zář_říj_lis_pro".split("_"), weekStart: 1, yearStart: 4, ordinal: function(e2) {
        return e2 + ".";
      }, formats: { LT: "H:mm", LTS: "H:mm:ss", L: "DD.MM.YYYY", LL: "D. MMMM YYYY", LLL: "D. MMMM YYYY H:mm", LLLL: "dddd D. MMMM YYYY H:mm", l: "D. M. YYYY" }, relativeTime: { future: "za %s", past: "před %s", s: r, m: r, mm: r, h: r, hh: r, d: r, dd: r, M: r, MM: r, y: r, yy: r } };
      return t.default.locale(d, null, true), d;
    });
  }
});
export default require_cs();
//# sourceMappingURL=dayjs_locale_cs.js.map
