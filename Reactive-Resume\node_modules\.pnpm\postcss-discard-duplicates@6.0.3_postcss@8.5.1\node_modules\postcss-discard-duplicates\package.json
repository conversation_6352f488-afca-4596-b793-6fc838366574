{"name": "postcss-discard-duplicates", "version": "6.0.3", "description": "Discard duplicate rules in your CSS files with PostCSS.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["src", "LICENSE-MIT", "types"], "keywords": ["css", "dedupe", "optimise", "postcss", "postcss-plugin"], "license": "MIT", "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "devDependencies": {"postcss": "^8.4.35"}, "peerDependencies": {"postcss": "^8.4.31"}}