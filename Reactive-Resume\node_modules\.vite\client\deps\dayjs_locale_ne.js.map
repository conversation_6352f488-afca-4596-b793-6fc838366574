{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/ne.js"], "sourcesContent": ["!function(e,_){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=_(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],_):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_ne=_(e.dayjs)}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),d={name:\"ne\",weekdays:\"आइतबार_सोमबार_मङ्गलबार_बुधबार_बिहिबार_शुक्रबार_शनिबार\".split(\"_\"),weekdaysShort:\"आइत._सोम._मङ्गल._बुध._बिहि._शुक्र._शनि.\".split(\"_\"),weekdaysMin:\"आ._सो._मं._बु._बि._शु._श.\".split(\"_\"),months:\"जनवरी_फेब्रुवरी_मार्च_अप्रिल_मे_जुन_जुलाई_अगष्ट_सेप्टेम्बर_अक्टोबर_नोभेम्बर_डिसेम्बर\".split(\"_\"),monthsShort:\"जन._फेब्रु._मार्च_अप्रि._मई_जुन_जुलाई._अग._सेप्ट._अक्टो._नोभे._डिसे.\".split(\"_\"),relativeTime:{future:\"%s पछि\",past:\"%s अघि\",s:\"सेकेन्ड\",m:\"एक मिनेट\",mm:\"%d मिनेट\",h:\"घन्टा\",hh:\"%d घन्टा\",d:\"एक दिन\",dd:\"%d दिन\",M:\"एक महिना\",MM:\"%d महिना\",y:\"एक वर्ष\",yy:\"%d वर्ष\"},ordinal:function(e){return(\"\"+e).replace(/\\d/g,(function(e){return\"०१२३४५६७८९\"[e]}))},formats:{LT:\"Aको h:mm बजे\",LTS:\"Aको h:mm:ss बजे\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, Aको h:mm बजे\",LLLL:\"dddd, D MMMM YYYY, Aको h:mm बजे\"}};return t.default.locale(d,null,!0),d}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,wDAAwD,MAAM,GAAG,GAAE,eAAc,0CAA0C,MAAM,GAAG,GAAE,aAAY,4BAA4B,MAAM,GAAG,GAAE,QAAO,uFAAuF,MAAM,GAAG,GAAE,aAAY,uEAAuE,MAAM,GAAG,GAAE,cAAa,EAAC,QAAO,UAAS,MAAK,UAAS,GAAE,WAAU,GAAE,YAAW,IAAG,YAAW,GAAE,SAAQ,IAAG,YAAW,GAAE,UAAS,IAAG,UAAS,GAAE,YAAW,IAAG,YAAW,GAAE,WAAU,IAAG,UAAS,GAAE,SAAQ,SAASA,IAAE;AAAC,gBAAO,KAAGA,IAAG,QAAQ,OAAO,SAASA,IAAE;AAAC,iBAAM,aAAaA,EAAC;AAAA,QAAC,CAAE;AAAA,MAAC,GAAE,SAAQ,EAAC,IAAG,gBAAe,KAAI,mBAAkB,GAAE,cAAa,IAAG,eAAc,KAAI,6BAA4B,MAAK,kCAAiC,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e"]}