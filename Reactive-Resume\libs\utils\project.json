{"name": "utils", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/utils/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:swc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/utils", "main": "libs/utils/src/index.ts", "tsConfig": "libs/utils/tsconfig.lib.json", "assets": ["libs/utils/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/utils/**/*.ts", "libs/utils/package.json"]}}, "test": {"executor": "@nx/vite:test", "outputs": ["{options.reportsDirectory}"], "options": {"passWithNoTests": true, "reportsDirectory": "../../coverage/libs/utils"}}}, "tags": ["frontend", "backend"]}