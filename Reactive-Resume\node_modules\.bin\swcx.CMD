@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609\node_modules\@swc\cli\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609\node_modules\@swc\cli\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609\node_modules\@swc\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609\node_modules\@swc\cli\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609\node_modules\@swc\cli\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609\node_modules\@swc\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609\node_modules\@swc\cli\bin\swcx.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\@swc+cli@0.4.0_@swc+core@1._726aeaaaed3172bf763bc12d6f0c2609\node_modules\@swc\cli\bin\swcx.js" %*
)
