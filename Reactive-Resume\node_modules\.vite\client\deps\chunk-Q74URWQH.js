import {
  useLayoutEffect2
} from "./chunk-4GZBC5L5.js";
import {
  Primitive
} from "./chunk-ZMNVLMX7.js";
import {
  require_jsx_runtime
} from "./chunk-FYG2RCCP.js";
import {
  require_react_dom
} from "./chunk-763LFKMO.js";
import {
  require_react
} from "./chunk-4ALVB3Y3.js";
import {
  __toESM
} from "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/@radix-ui+react-portal@1.1._7c94298102bb3bef9c60a2ae937b94b9/node_modules/@radix-ui/react-portal/dist/index.mjs
var React = __toESM(require_react(), 1);
var import_react_dom = __toESM(require_react_dom(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var PORTAL_NAME = "Portal";
var Portal = React.forwardRef((props, forwardedRef) => {
  var _a;
  const { container: containerProp, ...portalProps } = props;
  const [mounted, setMounted] = React.useState(false);
  useLayoutEffect2(() => setMounted(true), []);
  const container = containerProp || mounted && ((_a = globalThis == null ? void 0 : globalThis.document) == null ? void 0 : _a.body);
  return container ? import_react_dom.default.createPortal((0, import_jsx_runtime.jsx)(Primitive.div, { ...portalProps, ref: forwardedRef }), container) : null;
});
Portal.displayName = PORTAL_NAME;
var Root = Portal;

export {
  Portal,
  Root
};
//# sourceMappingURL=chunk-Q74URWQH.js.map
