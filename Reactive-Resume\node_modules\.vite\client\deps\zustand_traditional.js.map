{"version": 3, "sources": ["../../../.pnpm/zustand@4.5.6_@types+react@18.3.18_immer@10.1.1_react@18.3.1/node_modules/zustand/esm/traditional.mjs"], "sourcesContent": ["import ReactExports from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\nimport { createStore } from 'zustand/vanilla';\n\nconst { useDebugValue } = ReactExports;\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nconst identity = (arg) => arg;\nfunction useStoreWithEqualityFn(api, selector = identity, equalityFn) {\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getServerState || api.getInitialState,\n    selector,\n    equalityFn\n  );\n  useDebugValue(slice);\n  return slice;\n}\nconst createWithEqualityFnImpl = (createState, defaultEqualityFn) => {\n  const api = createStore(createState);\n  const useBoundStoreWithEqualityFn = (selector, equalityFn = defaultEqualityFn) => useStoreWithEqualityFn(api, selector, equalityFn);\n  Object.assign(useBoundStoreWithEqualityFn, api);\n  return useBoundStoreWithEqualityFn;\n};\nconst createWithEqualityFn = (createState, defaultEqualityFn) => createState ? createWithEqualityFnImpl(createState, defaultEqualityFn) : createWithEqualityFnImpl;\n\nexport { createWithEqualityFn, useStoreWithEqualityFn };\n"], "mappings": ";;;;;;;;;;;;;AAAA,mBAAyB;AACzB,2BAAwC;AAGxC,IAAM,EAAE,cAAc,IAAI,aAAAA;AAC1B,IAAM,EAAE,iCAAiC,IAAI,qBAAAC;AAC7C,IAAM,WAAW,CAAC,QAAQ;AAC1B,SAAS,uBAAuB,KAAK,WAAW,UAAU,YAAY;AACpE,QAAM,QAAQ;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI,kBAAkB,IAAI;AAAA,IAC1B;AAAA,IACA;AAAA,EACF;AACA,gBAAc,KAAK;AACnB,SAAO;AACT;AACA,IAAM,2BAA2B,CAAC,aAAa,sBAAsB;AACnE,QAAM,MAAM,YAAY,WAAW;AACnC,QAAM,8BAA8B,CAAC,UAAU,aAAa,sBAAsB,uBAAuB,KAAK,UAAU,UAAU;AAClI,SAAO,OAAO,6BAA6B,GAAG;AAC9C,SAAO;AACT;AACA,IAAM,uBAAuB,CAAC,aAAa,sBAAsB,cAAc,yBAAyB,aAAa,iBAAiB,IAAI;", "names": ["ReactExports", "useSyncExternalStoreExports"]}