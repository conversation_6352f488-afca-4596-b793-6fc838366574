{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "module": "commonjs", "types": ["node"], "emitDecoratorMetadata": true, "target": "es2021", "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true}, "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"], "include": ["src/**/*.ts"]}