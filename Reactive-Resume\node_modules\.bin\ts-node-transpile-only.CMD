@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46\node_modules\ts-node\dist\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46\node_modules\ts-node\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46\node_modules\ts-node\dist\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46\node_modules\ts-node\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46\node_modules\ts-node\dist\bin-transpile.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\ts-node@10.9.2_@swc+core@1._f8fe0305fc9fa47fe005b14f024afc46\node_modules\ts-node\dist\bin-transpile.js" %*
)
