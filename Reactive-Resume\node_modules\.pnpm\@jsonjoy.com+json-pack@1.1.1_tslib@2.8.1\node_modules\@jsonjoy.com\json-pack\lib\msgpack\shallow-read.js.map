{"version": 3, "file": "shallow-read.js", "sourceRoot": "", "sources": ["../../src/msgpack/shallow-read.ts"], "names": [], "mappings": ";;;AACA,mEAA8D;AAO9D,MAAM,MAAM,GAAG,CAAC,GAAW,EAAE,EAAE;IAC7B,MAAM,GAAG,GAAa,EAAE,CAAC;IACzB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,OAAO,IAAI,GAAG,MAAM,EAAE,CAAC;QACrB,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;QACnC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChB,SAAS;QACX,CAAC;aAAM,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACtC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;gBACvC,IAAI,IAAI,GAAG,MAAM,EAAE,CAAC;oBAClB,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBACnC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE,CAAC;wBAChC,IAAI,EAAE,CAAC;wBACP,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;oBAC9D,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/B,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;gBACxC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;gBACxC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;gBACxC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YACzC,CAAC;QACH,CAAC;QACD,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAClC,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAEK,MAAM,gBAAgB,GAAG,CAAC,IAAU,EAAM,EAAE;IACjD,MAAM,OAAO,GAAG,IAAI,iBAAO,CAAK;QAC9B,IAAI,EAAE,CAAC,KAAK,CAAC;QACb,IAAI,EAAE,aAAa;QACnB,QAAQ,EAAE,qBAAqB;QAC/B,QAAQ,EAAE,aAAa;KACxB,CAAC,CAAC;IAEH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACrB,QAAQ,OAAO,IAAI,EAAE,CAAC;YACpB,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACnC,MAAM,KAAK,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACpC,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACrC,OAAO,CAAC,EAAE,CAAU,OAAO,IAAI,sBAAsB,CAAC,CAAC;gBACvD,OAAO,CAAC,EAAE,CAAU,OAAO,MAAM,WAAW,CAAC,CAAC;gBAC9C,OAAO,CAAC,EAAE,CAAC,WAAW,KAAK,SAAS,KAAK,MAAM,IAAI,KAAK,KAAK,OAAO,CAAC,CAAC;gBACtE,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAC9B,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACnC,OAAO,CAAC,EAAE,CAAU,OAAO,IAAI,sBAAsB,CAAC,CAAC;gBACvD,OAAO,CAAC,EAAE,CAAU,OAAO,IAAI,QAAQ,MAAM,cAAc,IAAI,+BAA+B,CAAC,CAAC;gBAChG,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC1B,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;wBACxB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAClC,MAAM,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBAClE,OAAO,CAAC,EAAE,CACR,mBAAmB,SAAS,OAC1B,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,EAClD,4BAA4B,CAC7B,CAAC;oBACJ,CAAC;yBAAM,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;wBAC/B,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAClC,MAAM,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBAClE,OAAO,CAAC,EAAE,CACR,mBAAmB,SAAS,OAC1B,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,EAClD,4BAA4B,CAC7B,CAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBACrC,OAAO,CAAC,EAAE,CACR,kBAAkB,KAAK,OACrB,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,EAClD,4BAA4B,CAC7B,CAAC;oBACJ,CAAC;gBACH,CAAC;gBACD,OAAO,CAAC,EAAE,CAAC,GAAG,MAAM,UAAU,CAAC,CAAC;gBAChC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;gBACrB,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;gBAChB,OAAO,CAAC,EAAE,CAAC,QAAQ,MAAM,qCAAqC,CAAC,CAAC;gBAChE,MAAM;YACR,CAAC;YACD,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACnC,OAAO,CAAC,EAAE,CAAU,OAAO,IAAI,sBAAsB,CAAC,CAAC;gBACvD,OAAO,CAAC,EAAE,CAAU,MAAM,IAAI,OAAO,IAAI,2CAA2C,CAAC,CAAC;gBACtF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE;oBAAE,OAAO,CAAC,EAAE,CAAU,gBAAgB,CAAC,CAAC;gBACrE,MAAM;YACR,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACR,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;AAC3B,CAAC,CAAC;AArEW,QAAA,gBAAgB,oBAqE3B"}