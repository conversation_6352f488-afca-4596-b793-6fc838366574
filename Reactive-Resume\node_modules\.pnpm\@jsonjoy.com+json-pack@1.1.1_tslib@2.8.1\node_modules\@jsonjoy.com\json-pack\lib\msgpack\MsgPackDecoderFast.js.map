{"version": 3, "file": "MsgPackDecoderFast.js", "sourceRoot": "", "sources": ["../../src/msgpack/MsgPackDecoderFast.ts"], "names": [], "mappings": ";;;;AAAA,4DAAuD;AACvD,iEAA4D;AAE5D,iIAAiG;AAOjG,MAAa,kBAAkB;IAC7B,YACS,SAAY,IAAI,eAAM,EAAO,EACjB,aAAgC,iCAAuB;QADnE,WAAM,GAAN,MAAM,CAAuB;QACjB,eAAU,GAAV,UAAU,CAA6C;IACzE,CAAC;IAGG,MAAM,CAAC,KAAiB;QAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;IACpB,CAAC;IAEM,IAAI,CAAC,KAAiB;QAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,GAAG,EAAe,CAAC;IACjC,CAAC;IAEM,GAAG;QACR,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;QACzB,IAAI,IAAI,IAAI,IAAI;YAAE,OAAO,IAAI,GAAG,KAAK,CAAC;QACtC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC;gBAChB,IAAI,IAAI,IAAI,SAAS;oBAAE,OAAO,IAAI,CAAC;gBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,GAAG,IAAI;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;;oBAE3C,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;QACD,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;oBACjB,IAAI,IAAI,IAAI,IAAI;wBAAE,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;;wBAC7E,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC7D,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,IAAI,IAAI;wBAAE,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;;wBACxF,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC7E,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,IAAI,IAAI;oBACjB,CAAC,CAAC,IAAI,IAAI,IAAI;wBACZ,CAAC,CAAC,IAAI,KAAK,IAAI;4BACb,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE;4BACd,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;wBAC1B,CAAC,CAAC,IAAI,KAAK,IAAI;4BACb,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE;4BACb,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE;oBAClB,CAAC,CAAC,IAAI,IAAI,IAAI;wBACZ,CAAC,CAAC,IAAI,KAAK,IAAI;4BACb,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE;4BACd,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE;wBAChB,CAAC,CAAC,IAAI,KAAK,IAAI;4BACb,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE;4BACb,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,UAAU,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;YACnD,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACxB,OAAO,IAAI,IAAI,IAAI;gBACjB,CAAC,CAAC,IAAI,IAAI,IAAI;oBACZ,CAAC,CAAC,IAAI,KAAK,IAAI;wBACb,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE;wBACd,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE;oBAChB,CAAC,CAAC,IAAI,KAAK,IAAI;wBACb,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;wBACb,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,UAAU,GAAG,MAAM,CAAC,GAAG,EAAE;gBAC9C,CAAC,CAAC,IAAI,IAAI,IAAI;oBACZ,CAAC,CAAC,IAAI,KAAK,IAAI;wBACb,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;wBACb,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,IAAI,KAAK,IAAI;wBACb,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;wBACd,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,IAAI;oBACP,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;gBAClC,KAAK,IAAI;oBACP,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;gBACnC,KAAK,IAAI;oBACP,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;gBACnC,KAAK,IAAI;oBACP,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;gBAChC,KAAK,IAAI;oBACP,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;gBAChC,KAAK,IAAI;oBACP,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;gBAChC,KAAK,IAAI;oBACP,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEM,GAAG;QACR,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;QACzB,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK;YAAE,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;QAC5D,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAClC,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YACnC,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;QACrC,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAGS,GAAG,CAAC,IAAY;QACxB,MAAM,GAAG,GAA4B,EAAE,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,IAAI,GAAG,KAAK,WAAW;gBAAE,QAA+B;YACxD,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACxB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAGS,GAAG;QACX,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAI,IAAI,IAAI,UAAU,IAAI,IAAI,IAAI,UAAU,EAAE,CAAC;YAC7C,MAAM,IAAI,GAAG,IAAI,GAAG,OAAO,CAAC;YAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;YACrE,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;YACrB,OAAO,GAAG,CAAC;QACb,CAAC;aAAM,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YACzB,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAChD,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;gBACd,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;gBACrE,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;gBACrB,OAAO,GAAG,CAAC;YACb,CAAC;QACH,CAAC;QACD,MAAM,CAAC,CAAC,EAAE,CAAC;QACX,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAClC,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YACnC,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YACnC;gBACE,OAAO,EAAE,CAAC;QACd,CAAC;IACH,CAAC;IAGS,GAAG,CAAC,IAAY;QACxB,MAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE;YAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACpD,OAAO,GAAG,CAAC;IACb,CAAC;IAGS,GAAG,CAAC,IAAY;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;QACzB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;QAC5B,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACjD,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;QACf,OAAO,IAAI,qCAAiB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC1C,CAAC;IAES,IAAI,CAAC,KAAa;QAC1B,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC;IACzB,CAAC;CACF;AA1KD,gDA0KC"}