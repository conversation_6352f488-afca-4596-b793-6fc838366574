import "./chunk-OP7CRF6T.js";
import {
  createStore
} from "./chunk-FZMYGAZU.js";
import "./chunk-JA6YWDPZ.js";
import "./chunk-4ALVB3Y3.js";
import "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/zundo@2.3.0_zustand@4.5.6_@_06bb7f4e76ea0ac514c57617851ac37e/node_modules/zundo/dist/index.js
var temporalStateCreator = (userSet, userGet, options) => {
  const stateCreator = (set, get) => {
    return {
      pastStates: (options == null ? void 0 : options.pastStates) || [],
      futureStates: (options == null ? void 0 : options.futureStates) || [],
      undo: (steps = 1) => {
        var _a, _b;
        if (get().pastStates.length) {
          const currentState = ((_a = options == null ? void 0 : options.partialize) == null ? void 0 : _a.call(options, userGet())) || userGet();
          const statesToApply = get().pastStates.splice(-steps, steps);
          const nextState = statesToApply.shift();
          userSet(nextState);
          set({
            pastStates: get().pastStates,
            futureStates: get().futureStates.concat(
              ((_b = options == null ? void 0 : options.diff) == null ? void 0 : _b.call(options, currentState, nextState)) || currentState,
              statesToApply.reverse()
            )
          });
        }
      },
      redo: (steps = 1) => {
        var _a, _b;
        if (get().futureStates.length) {
          const currentState = ((_a = options == null ? void 0 : options.partialize) == null ? void 0 : _a.call(options, userGet())) || userGet();
          const statesToApply = get().futureStates.splice(-steps, steps);
          const nextState = statesToApply.shift();
          userSet(nextState);
          set({
            pastStates: get().pastStates.concat(
              ((_b = options == null ? void 0 : options.diff) == null ? void 0 : _b.call(options, currentState, nextState)) || currentState,
              statesToApply.reverse()
            ),
            futureStates: get().futureStates
          });
        }
      },
      clear: () => set({ pastStates: [], futureStates: [] }),
      isTracking: true,
      pause: () => set({ isTracking: false }),
      resume: () => set({ isTracking: true }),
      setOnSave: (_onSave) => set({ _onSave }),
      // Internal properties
      _onSave: options == null ? void 0 : options.onSave,
      _handleSet: (pastState, replace, currentState, deltaState) => {
        var _a, _b;
        if ((options == null ? void 0 : options.limit) && get().pastStates.length >= (options == null ? void 0 : options.limit)) {
          get().pastStates.shift();
        }
        (_b = (_a = get())._onSave) == null ? void 0 : _b.call(_a, pastState, currentState);
        set({
          pastStates: get().pastStates.concat(deltaState || pastState),
          futureStates: []
        });
      }
    };
  };
  return stateCreator;
};
var temporal = (config, options) => {
  const configWithTemporal = (set, get, store) => {
    var _a, _b;
    store.temporal = createStore(
      ((_a = options == null ? void 0 : options.wrapTemporal) == null ? void 0 : _a.call(options, temporalStateCreator(set, get, options))) || temporalStateCreator(set, get, options)
    );
    const curriedHandleSet = ((_b = options == null ? void 0 : options.handleSet) == null ? void 0 : _b.call(
      options,
      store.temporal.getState()._handleSet
    )) || store.temporal.getState()._handleSet;
    const temporalHandleSet = (pastState) => {
      var _a2, _b2, _c;
      if (!store.temporal.getState().isTracking) return;
      const currentState = ((_a2 = options == null ? void 0 : options.partialize) == null ? void 0 : _a2.call(options, get())) || get();
      const deltaState = (_b2 = options == null ? void 0 : options.diff) == null ? void 0 : _b2.call(options, pastState, currentState);
      if (
        // Don't call handleSet if state hasn't changed, as determined by diff fn or equality fn
        !// If the user has provided a diff function but nothing has been changed, deltaState will be null
        (deltaState === null || // If the user has provided an equality function, use it
        ((_c = options == null ? void 0 : options.equality) == null ? void 0 : _c.call(options, pastState, currentState)))
      ) {
        curriedHandleSet(
          pastState,
          void 0,
          currentState,
          deltaState
        );
      }
    };
    const setState = store.setState;
    store.setState = (...args) => {
      var _a2;
      const pastState = ((_a2 = options == null ? void 0 : options.partialize) == null ? void 0 : _a2.call(options, get())) || get();
      setState(...args);
      temporalHandleSet(pastState);
    };
    return config(
      // Modify the set function to call the userlandSet function
      (...args) => {
        var _a2;
        const pastState = ((_a2 = options == null ? void 0 : options.partialize) == null ? void 0 : _a2.call(options, get())) || get();
        set(...args);
        temporalHandleSet(pastState);
      },
      get,
      store
    );
  };
  return configWithTemporal;
};
export {
  temporal
};
//# sourceMappingURL=zundo.js.map
