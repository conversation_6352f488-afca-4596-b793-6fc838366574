{"version": 3, "sources": ["../../../.pnpm/lodash.get@4.4.2/node_modules/lodash.get/index.js"], "sourcesContent": ["/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/** `Object#toString` result references. */\nvar funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    symbolTag = '[object Symbol]';\n\n/** Used to match property names within property paths. */\nvar reIsDeepProp = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/,\n    reIsPlainProp = /^\\w*$/,\n    reLeadingDot = /^\\./,\n    rePropName = /[^.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))/g;\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\n\n/** Used to match backslashes in property paths. */\nvar reEscapeChar = /\\\\(\\\\)?/g;\n\n/** Used to detect host constructors (Safari). */\nvar reIsHostCtor = /^\\[object .+?Constructor\\]$/;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\n/**\n * Checks if `value` is a host object in IE < 9.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a host object, else `false`.\n */\nfunction isHostObject(value) {\n  // Many host objects are `Object` objects that can coerce to strings\n  // despite having improperly defined `toString` methods.\n  var result = false;\n  if (value != null && typeof value.toString != 'function') {\n    try {\n      result = !!(value + '');\n    } catch (e) {}\n  }\n  return result;\n}\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype,\n    funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\n/** Used to detect methods masquerading as native. */\nvar maskSrcKey = (function() {\n  var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');\n  return uid ? ('Symbol(src)_1.' + uid) : '';\n}());\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Used to detect if a method is native. */\nvar reIsNative = RegExp('^' +\n  funcToString.call(hasOwnProperty).replace(reRegExpChar, '\\\\$&')\n  .replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g, '$1.*?') + '$'\n);\n\n/** Built-in value references. */\nvar Symbol = root.Symbol,\n    splice = arrayProto.splice;\n\n/* Built-in method references that are verified to be native. */\nvar Map = getNative(root, 'Map'),\n    nativeCreate = getNative(Object, 'create');\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolToString = symbolProto ? symbolProto.toString : undefined;\n\n/**\n * Creates a hash object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Hash(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the hash.\n *\n * @private\n * @name clear\n * @memberOf Hash\n */\nfunction hashClear() {\n  this.__data__ = nativeCreate ? nativeCreate(null) : {};\n}\n\n/**\n * Removes `key` and its value from the hash.\n *\n * @private\n * @name delete\n * @memberOf Hash\n * @param {Object} hash The hash to modify.\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction hashDelete(key) {\n  return this.has(key) && delete this.__data__[key];\n}\n\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n\n/**\n * Checks if a hash value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf Hash\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction hashHas(key) {\n  var data = this.__data__;\n  return nativeCreate ? data[key] !== undefined : hasOwnProperty.call(data, key);\n}\n\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;\n  return this;\n}\n\n// Add methods to `Hash`.\nHash.prototype.clear = hashClear;\nHash.prototype['delete'] = hashDelete;\nHash.prototype.get = hashGet;\nHash.prototype.has = hashHas;\nHash.prototype.set = hashSet;\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the list cache.\n *\n * @private\n * @name clear\n * @memberOf ListCache\n */\nfunction listCacheClear() {\n  this.__data__ = [];\n}\n\n/**\n * Removes `key` and its value from the list cache.\n *\n * @private\n * @name delete\n * @memberOf ListCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction listCacheDelete(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    return false;\n  }\n  var lastIndex = data.length - 1;\n  if (index == lastIndex) {\n    data.pop();\n  } else {\n    splice.call(data, index, 1);\n  }\n  return true;\n}\n\n/**\n * Gets the list cache value for `key`.\n *\n * @private\n * @name get\n * @memberOf ListCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction listCacheGet(key) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  return index < 0 ? undefined : data[index][1];\n}\n\n/**\n * Checks if a list cache value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf ListCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction listCacheHas(key) {\n  return assocIndexOf(this.__data__, key) > -1;\n}\n\n/**\n * Sets the list cache `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf ListCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the list cache instance.\n */\nfunction listCacheSet(key, value) {\n  var data = this.__data__,\n      index = assocIndexOf(data, key);\n\n  if (index < 0) {\n    data.push([key, value]);\n  } else {\n    data[index][1] = value;\n  }\n  return this;\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries ? entries.length : 0;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.__data__ = {\n    'hash': new Hash,\n    'map': new (Map || ListCache),\n    'string': new Hash\n  };\n}\n\n/**\n * Removes `key` and its value from the map.\n *\n * @private\n * @name delete\n * @memberOf MapCache\n * @param {string} key The key of the value to remove.\n * @returns {boolean} Returns `true` if the entry was removed, else `false`.\n */\nfunction mapCacheDelete(key) {\n  return getMapData(this, key)['delete'](key);\n}\n\n/**\n * Gets the map value for `key`.\n *\n * @private\n * @name get\n * @memberOf MapCache\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction mapCacheGet(key) {\n  return getMapData(this, key).get(key);\n}\n\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  getMapData(this, key).set(key, value);\n  return this;\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n\n/**\n * Gets the index at which the `key` is found in `array` of key-value pairs.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} key The key to search for.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction assocIndexOf(array, key) {\n  var length = array.length;\n  while (length--) {\n    if (eq(array[length][0], key)) {\n      return length;\n    }\n  }\n  return -1;\n}\n\n/**\n * The base implementation of `_.get` without support for default values.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @returns {*} Returns the resolved value.\n */\nfunction baseGet(object, path) {\n  path = isKey(path, object) ? [path] : castPath(path);\n\n  var index = 0,\n      length = path.length;\n\n  while (object != null && index < length) {\n    object = object[toKey(path[index++])];\n  }\n  return (index && index == length) ? object : undefined;\n}\n\n/**\n * The base implementation of `_.isNative` without bad shim checks.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n */\nfunction baseIsNative(value) {\n  if (!isObject(value) || isMasked(value)) {\n    return false;\n  }\n  var pattern = (isFunction(value) || isHostObject(value)) ? reIsNative : reIsHostCtor;\n  return pattern.test(toSource(value));\n}\n\n/**\n * The base implementation of `_.toString` which doesn't convert nullish\n * values to empty strings.\n *\n * @private\n * @param {*} value The value to process.\n * @returns {string} Returns the string.\n */\nfunction baseToString(value) {\n  // Exit early for strings to avoid a performance hit in some environments.\n  if (typeof value == 'string') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return symbolToString ? symbolToString.call(value) : '';\n  }\n  var result = (value + '');\n  return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;\n}\n\n/**\n * Casts `value` to a path array if it's not one.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {Array} Returns the cast property path array.\n */\nfunction castPath(value) {\n  return isArray(value) ? value : stringToPath(value);\n}\n\n/**\n * Gets the data for `map`.\n *\n * @private\n * @param {Object} map The map to query.\n * @param {string} key The reference key.\n * @returns {*} Returns the map data.\n */\nfunction getMapData(map, key) {\n  var data = map.__data__;\n  return isKeyable(key)\n    ? data[typeof key == 'string' ? 'string' : 'hash']\n    : data.map;\n}\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\n/**\n * Checks if `value` is a property name and not a property path.\n *\n * @private\n * @param {*} value The value to check.\n * @param {Object} [object] The object to query keys on.\n * @returns {boolean} Returns `true` if `value` is a property name, else `false`.\n */\nfunction isKey(value, object) {\n  if (isArray(value)) {\n    return false;\n  }\n  var type = typeof value;\n  if (type == 'number' || type == 'symbol' || type == 'boolean' ||\n      value == null || isSymbol(value)) {\n    return true;\n  }\n  return reIsPlainProp.test(value) || !reIsDeepProp.test(value) ||\n    (object != null && value in Object(object));\n}\n\n/**\n * Checks if `value` is suitable for use as unique object key.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is suitable, else `false`.\n */\nfunction isKeyable(value) {\n  var type = typeof value;\n  return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')\n    ? (value !== '__proto__')\n    : (value === null);\n}\n\n/**\n * Checks if `func` has its source masked.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` is masked, else `false`.\n */\nfunction isMasked(func) {\n  return !!maskSrcKey && (maskSrcKey in func);\n}\n\n/**\n * Converts `string` to a property path array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the property path array.\n */\nvar stringToPath = memoize(function(string) {\n  string = toString(string);\n\n  var result = [];\n  if (reLeadingDot.test(string)) {\n    result.push('');\n  }\n  string.replace(rePropName, function(match, number, quote, string) {\n    result.push(quote ? string.replace(reEscapeChar, '$1') : (number || match));\n  });\n  return result;\n});\n\n/**\n * Converts `value` to a string key if it's not a string or symbol.\n *\n * @private\n * @param {*} value The value to inspect.\n * @returns {string|symbol} Returns the key.\n */\nfunction toKey(value) {\n  if (typeof value == 'string' || isSymbol(value)) {\n    return value;\n  }\n  var result = (value + '');\n  return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;\n}\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to process.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return (func + '');\n    } catch (e) {}\n  }\n  return '';\n}\n\n/**\n * Creates a function that memoizes the result of `func`. If `resolver` is\n * provided, it determines the cache key for storing the result based on the\n * arguments provided to the memoized function. By default, the first argument\n * provided to the memoized function is used as the map cache key. The `func`\n * is invoked with the `this` binding of the memoized function.\n *\n * **Note:** The cache is exposed as the `cache` property on the memoized\n * function. Its creation may be customized by replacing the `_.memoize.Cache`\n * constructor with one whose instances implement the\n * [`Map`](http://ecma-international.org/ecma-262/7.0/#sec-properties-of-the-map-prototype-object)\n * method interface of `delete`, `get`, `has`, and `set`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to have its output memoized.\n * @param {Function} [resolver] The function to resolve the cache key.\n * @returns {Function} Returns the new memoized function.\n * @example\n *\n * var object = { 'a': 1, 'b': 2 };\n * var other = { 'c': 3, 'd': 4 };\n *\n * var values = _.memoize(_.values);\n * values(object);\n * // => [1, 2]\n *\n * values(other);\n * // => [3, 4]\n *\n * object.a = 2;\n * values(object);\n * // => [1, 2]\n *\n * // Modify the result cache.\n * values.cache.set(object, ['a', 'b']);\n * values(object);\n * // => ['a', 'b']\n *\n * // Replace `_.memoize.Cache`.\n * _.memoize.Cache = WeakMap;\n */\nfunction memoize(func, resolver) {\n  if (typeof func != 'function' || (resolver && typeof resolver != 'function')) {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  var memoized = function() {\n    var args = arguments,\n        key = resolver ? resolver.apply(this, args) : args[0],\n        cache = memoized.cache;\n\n    if (cache.has(key)) {\n      return cache.get(key);\n    }\n    var result = func.apply(this, args);\n    memoized.cache = cache.set(key, result);\n    return result;\n  };\n  memoized.cache = new (memoize.Cache || MapCache);\n  return memoized;\n}\n\n// Assign cache to `_.memoize`.\nmemoize.Cache = MapCache;\n\n/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */\nfunction eq(value, other) {\n  return value === other || (value !== value && other !== other);\n}\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 8-9 which returns 'object' for typed array and other constructors.\n  var tag = isObject(value) ? objectToString.call(value) : '';\n  return tag == funcTag || tag == genTag;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a string. An empty string is returned for `null`\n * and `undefined` values. The sign of `-0` is preserved.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {string} Returns the string.\n * @example\n *\n * _.toString(null);\n * // => ''\n *\n * _.toString(-0);\n * // => '-0'\n *\n * _.toString([1, 2, 3]);\n * // => '1,2,3'\n */\nfunction toString(value) {\n  return value == null ? '' : baseToString(value);\n}\n\n/**\n * Gets the value at `path` of `object`. If the resolved value is\n * `undefined`, the `defaultValue` is returned in its place.\n *\n * @static\n * @memberOf _\n * @since 3.7.0\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @param {*} [defaultValue] The value returned for `undefined` resolved values.\n * @returns {*} Returns the resolved value.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c': 3 } }] };\n *\n * _.get(object, 'a[0].b.c');\n * // => 3\n *\n * _.get(object, ['a', '0', 'b', 'c']);\n * // => 3\n *\n * _.get(object, 'a.b.c', 'default');\n * // => 'default'\n */\nfunction get(object, path, defaultValue) {\n  var result = object == null ? undefined : baseGet(object, path);\n  return result === undefined ? defaultValue : result;\n}\n\nmodule.exports = get;\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,QAAI,kBAAkB;AAGtB,QAAI,iBAAiB;AAGrB,QAAI,WAAW,IAAI;AAGnB,QAAI,UAAU;AAAd,QACI,SAAS;AADb,QAEI,YAAY;AAGhB,QAAI,eAAe;AAAnB,QACI,gBAAgB;AADpB,QAEI,eAAe;AAFnB,QAGI,aAAa;AAMjB,QAAI,eAAe;AAGnB,QAAI,eAAe;AAGnB,QAAI,eAAe;AAGnB,QAAI,aAAa,OAAO,UAAU,YAAY,UAAU,OAAO,WAAW,UAAU;AAGpF,QAAI,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,WAAW,UAAU;AAG5E,QAAI,OAAO,cAAc,YAAY,SAAS,aAAa,EAAE;AAU7D,aAAS,SAAS,QAAQ,KAAK;AAC7B,aAAO,UAAU,OAAO,SAAY,OAAO,GAAG;AAAA,IAChD;AASA,aAAS,aAAa,OAAO;AAG3B,UAAI,SAAS;AACb,UAAI,SAAS,QAAQ,OAAO,MAAM,YAAY,YAAY;AACxD,YAAI;AACF,mBAAS,CAAC,EAAE,QAAQ;AAAA,QACtB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AACA,aAAO;AAAA,IACT;AAGA,QAAI,aAAa,MAAM;AAAvB,QACI,YAAY,SAAS;AADzB,QAEI,cAAc,OAAO;AAGzB,QAAI,aAAa,KAAK,oBAAoB;AAG1C,QAAI,aAAc,WAAW;AAC3B,UAAI,MAAM,SAAS,KAAK,cAAc,WAAW,QAAQ,WAAW,KAAK,YAAY,EAAE;AACvF,aAAO,MAAO,mBAAmB,MAAO;AAAA,IAC1C,EAAE;AAGF,QAAI,eAAe,UAAU;AAG7B,QAAI,iBAAiB,YAAY;AAOjC,QAAI,iBAAiB,YAAY;AAGjC,QAAI,aAAa;AAAA,MAAO,MACtB,aAAa,KAAK,cAAc,EAAE,QAAQ,cAAc,MAAM,EAC7D,QAAQ,0DAA0D,OAAO,IAAI;AAAA,IAChF;AAGA,QAAI,SAAS,KAAK;AAAlB,QACI,SAAS,WAAW;AAGxB,QAAI,MAAM,UAAU,MAAM,KAAK;AAA/B,QACI,eAAe,UAAU,QAAQ,QAAQ;AAG7C,QAAI,cAAc,SAAS,OAAO,YAAY;AAA9C,QACI,iBAAiB,cAAc,YAAY,WAAW;AAS1D,aAAS,KAAK,SAAS;AACrB,UAAI,QAAQ,IACR,SAAS,UAAU,QAAQ,SAAS;AAExC,WAAK,MAAM;AACX,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,QAAQ,KAAK;AACzB,aAAK,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,MAC7B;AAAA,IACF;AASA,aAAS,YAAY;AACnB,WAAK,WAAW,eAAe,aAAa,IAAI,IAAI,CAAC;AAAA,IACvD;AAYA,aAAS,WAAW,KAAK;AACvB,aAAO,KAAK,IAAI,GAAG,KAAK,OAAO,KAAK,SAAS,GAAG;AAAA,IAClD;AAWA,aAAS,QAAQ,KAAK;AACpB,UAAI,OAAO,KAAK;AAChB,UAAI,cAAc;AAChB,YAAI,SAAS,KAAK,GAAG;AACrB,eAAO,WAAW,iBAAiB,SAAY;AAAA,MACjD;AACA,aAAO,eAAe,KAAK,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI;AAAA,IACtD;AAWA,aAAS,QAAQ,KAAK;AACpB,UAAI,OAAO,KAAK;AAChB,aAAO,eAAe,KAAK,GAAG,MAAM,SAAY,eAAe,KAAK,MAAM,GAAG;AAAA,IAC/E;AAYA,aAAS,QAAQ,KAAK,OAAO;AAC3B,UAAI,OAAO,KAAK;AAChB,WAAK,GAAG,IAAK,gBAAgB,UAAU,SAAa,iBAAiB;AACrE,aAAO;AAAA,IACT;AAGA,SAAK,UAAU,QAAQ;AACvB,SAAK,UAAU,QAAQ,IAAI;AAC3B,SAAK,UAAU,MAAM;AACrB,SAAK,UAAU,MAAM;AACrB,SAAK,UAAU,MAAM;AASrB,aAAS,UAAU,SAAS;AAC1B,UAAI,QAAQ,IACR,SAAS,UAAU,QAAQ,SAAS;AAExC,WAAK,MAAM;AACX,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,QAAQ,KAAK;AACzB,aAAK,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,MAC7B;AAAA,IACF;AASA,aAAS,iBAAiB;AACxB,WAAK,WAAW,CAAC;AAAA,IACnB;AAWA,aAAS,gBAAgB,KAAK;AAC5B,UAAI,OAAO,KAAK,UACZ,QAAQ,aAAa,MAAM,GAAG;AAElC,UAAI,QAAQ,GAAG;AACb,eAAO;AAAA,MACT;AACA,UAAI,YAAY,KAAK,SAAS;AAC9B,UAAI,SAAS,WAAW;AACtB,aAAK,IAAI;AAAA,MACX,OAAO;AACL,eAAO,KAAK,MAAM,OAAO,CAAC;AAAA,MAC5B;AACA,aAAO;AAAA,IACT;AAWA,aAAS,aAAa,KAAK;AACzB,UAAI,OAAO,KAAK,UACZ,QAAQ,aAAa,MAAM,GAAG;AAElC,aAAO,QAAQ,IAAI,SAAY,KAAK,KAAK,EAAE,CAAC;AAAA,IAC9C;AAWA,aAAS,aAAa,KAAK;AACzB,aAAO,aAAa,KAAK,UAAU,GAAG,IAAI;AAAA,IAC5C;AAYA,aAAS,aAAa,KAAK,OAAO;AAChC,UAAI,OAAO,KAAK,UACZ,QAAQ,aAAa,MAAM,GAAG;AAElC,UAAI,QAAQ,GAAG;AACb,aAAK,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,MACxB,OAAO;AACL,aAAK,KAAK,EAAE,CAAC,IAAI;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAGA,cAAU,UAAU,QAAQ;AAC5B,cAAU,UAAU,QAAQ,IAAI;AAChC,cAAU,UAAU,MAAM;AAC1B,cAAU,UAAU,MAAM;AAC1B,cAAU,UAAU,MAAM;AAS1B,aAAS,SAAS,SAAS;AACzB,UAAI,QAAQ,IACR,SAAS,UAAU,QAAQ,SAAS;AAExC,WAAK,MAAM;AACX,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,QAAQ,QAAQ,KAAK;AACzB,aAAK,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,MAC7B;AAAA,IACF;AASA,aAAS,gBAAgB;AACvB,WAAK,WAAW;AAAA,QACd,QAAQ,IAAI;AAAA,QACZ,OAAO,KAAK,OAAO;AAAA,QACnB,UAAU,IAAI;AAAA,MAChB;AAAA,IACF;AAWA,aAAS,eAAe,KAAK;AAC3B,aAAO,WAAW,MAAM,GAAG,EAAE,QAAQ,EAAE,GAAG;AAAA,IAC5C;AAWA,aAAS,YAAY,KAAK;AACxB,aAAO,WAAW,MAAM,GAAG,EAAE,IAAI,GAAG;AAAA,IACtC;AAWA,aAAS,YAAY,KAAK;AACxB,aAAO,WAAW,MAAM,GAAG,EAAE,IAAI,GAAG;AAAA,IACtC;AAYA,aAAS,YAAY,KAAK,OAAO;AAC/B,iBAAW,MAAM,GAAG,EAAE,IAAI,KAAK,KAAK;AACpC,aAAO;AAAA,IACT;AAGA,aAAS,UAAU,QAAQ;AAC3B,aAAS,UAAU,QAAQ,IAAI;AAC/B,aAAS,UAAU,MAAM;AACzB,aAAS,UAAU,MAAM;AACzB,aAAS,UAAU,MAAM;AAUzB,aAAS,aAAa,OAAO,KAAK;AAChC,UAAI,SAAS,MAAM;AACnB,aAAO,UAAU;AACf,YAAI,GAAG,MAAM,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG;AAC7B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAUA,aAAS,QAAQ,QAAQ,MAAM;AAC7B,aAAO,MAAM,MAAM,MAAM,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;AAEnD,UAAI,QAAQ,GACR,SAAS,KAAK;AAElB,aAAO,UAAU,QAAQ,QAAQ,QAAQ;AACvC,iBAAS,OAAO,MAAM,KAAK,OAAO,CAAC,CAAC;AAAA,MACtC;AACA,aAAQ,SAAS,SAAS,SAAU,SAAS;AAAA,IAC/C;AAUA,aAAS,aAAa,OAAO;AAC3B,UAAI,CAAC,SAAS,KAAK,KAAK,SAAS,KAAK,GAAG;AACvC,eAAO;AAAA,MACT;AACA,UAAI,UAAW,WAAW,KAAK,KAAK,aAAa,KAAK,IAAK,aAAa;AACxE,aAAO,QAAQ,KAAK,SAAS,KAAK,CAAC;AAAA,IACrC;AAUA,aAAS,aAAa,OAAO;AAE3B,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO;AAAA,MACT;AACA,UAAI,SAAS,KAAK,GAAG;AACnB,eAAO,iBAAiB,eAAe,KAAK,KAAK,IAAI;AAAA,MACvD;AACA,UAAI,SAAU,QAAQ;AACtB,aAAQ,UAAU,OAAQ,IAAI,SAAU,CAAC,WAAY,OAAO;AAAA,IAC9D;AASA,aAAS,SAAS,OAAO;AACvB,aAAO,QAAQ,KAAK,IAAI,QAAQ,aAAa,KAAK;AAAA,IACpD;AAUA,aAAS,WAAW,KAAK,KAAK;AAC5B,UAAI,OAAO,IAAI;AACf,aAAO,UAAU,GAAG,IAChB,KAAK,OAAO,OAAO,WAAW,WAAW,MAAM,IAC/C,KAAK;AAAA,IACX;AAUA,aAAS,UAAU,QAAQ,KAAK;AAC9B,UAAI,QAAQ,SAAS,QAAQ,GAAG;AAChC,aAAO,aAAa,KAAK,IAAI,QAAQ;AAAA,IACvC;AAUA,aAAS,MAAM,OAAO,QAAQ;AAC5B,UAAI,QAAQ,KAAK,GAAG;AAClB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,OAAO;AAClB,UAAI,QAAQ,YAAY,QAAQ,YAAY,QAAQ,aAChD,SAAS,QAAQ,SAAS,KAAK,GAAG;AACpC,eAAO;AAAA,MACT;AACA,aAAO,cAAc,KAAK,KAAK,KAAK,CAAC,aAAa,KAAK,KAAK,KACzD,UAAU,QAAQ,SAAS,OAAO,MAAM;AAAA,IAC7C;AASA,aAAS,UAAU,OAAO;AACxB,UAAI,OAAO,OAAO;AAClB,aAAQ,QAAQ,YAAY,QAAQ,YAAY,QAAQ,YAAY,QAAQ,YACvE,UAAU,cACV,UAAU;AAAA,IACjB;AASA,aAAS,SAAS,MAAM;AACtB,aAAO,CAAC,CAAC,cAAe,cAAc;AAAA,IACxC;AASA,QAAI,eAAe,QAAQ,SAAS,QAAQ;AAC1C,eAAS,SAAS,MAAM;AAExB,UAAI,SAAS,CAAC;AACd,UAAI,aAAa,KAAK,MAAM,GAAG;AAC7B,eAAO,KAAK,EAAE;AAAA,MAChB;AACA,aAAO,QAAQ,YAAY,SAAS,OAAO,QAAQ,OAAOA,SAAQ;AAChE,eAAO,KAAK,QAAQA,QAAO,QAAQ,cAAc,IAAI,IAAK,UAAU,KAAM;AAAA,MAC5E,CAAC;AACD,aAAO;AAAA,IACT,CAAC;AASD,aAAS,MAAM,OAAO;AACpB,UAAI,OAAO,SAAS,YAAY,SAAS,KAAK,GAAG;AAC/C,eAAO;AAAA,MACT;AACA,UAAI,SAAU,QAAQ;AACtB,aAAQ,UAAU,OAAQ,IAAI,SAAU,CAAC,WAAY,OAAO;AAAA,IAC9D;AASA,aAAS,SAAS,MAAM;AACtB,UAAI,QAAQ,MAAM;AAChB,YAAI;AACF,iBAAO,aAAa,KAAK,IAAI;AAAA,QAC/B,SAAS,GAAG;AAAA,QAAC;AACb,YAAI;AACF,iBAAQ,OAAO;AAAA,QACjB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AACA,aAAO;AAAA,IACT;AA8CA,aAAS,QAAQ,MAAM,UAAU;AAC/B,UAAI,OAAO,QAAQ,cAAe,YAAY,OAAO,YAAY,YAAa;AAC5E,cAAM,IAAI,UAAU,eAAe;AAAA,MACrC;AACA,UAAI,WAAW,WAAW;AACxB,YAAI,OAAO,WACP,MAAM,WAAW,SAAS,MAAM,MAAM,IAAI,IAAI,KAAK,CAAC,GACpD,QAAQ,SAAS;AAErB,YAAI,MAAM,IAAI,GAAG,GAAG;AAClB,iBAAO,MAAM,IAAI,GAAG;AAAA,QACtB;AACA,YAAI,SAAS,KAAK,MAAM,MAAM,IAAI;AAClC,iBAAS,QAAQ,MAAM,IAAI,KAAK,MAAM;AACtC,eAAO;AAAA,MACT;AACA,eAAS,QAAQ,KAAK,QAAQ,SAAS;AACvC,aAAO;AAAA,IACT;AAGA,YAAQ,QAAQ;AAkChB,aAAS,GAAG,OAAO,OAAO;AACxB,aAAO,UAAU,SAAU,UAAU,SAAS,UAAU;AAAA,IAC1D;AAyBA,QAAI,UAAU,MAAM;AAmBpB,aAAS,WAAW,OAAO;AAGzB,UAAI,MAAM,SAAS,KAAK,IAAI,eAAe,KAAK,KAAK,IAAI;AACzD,aAAO,OAAO,WAAW,OAAO;AAAA,IAClC;AA2BA,aAAS,SAAS,OAAO;AACvB,UAAI,OAAO,OAAO;AAClB,aAAO,CAAC,CAAC,UAAU,QAAQ,YAAY,QAAQ;AAAA,IACjD;AA0BA,aAAS,aAAa,OAAO;AAC3B,aAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AAAA,IACpC;AAmBA,aAAS,SAAS,OAAO;AACvB,aAAO,OAAO,SAAS,YACpB,aAAa,KAAK,KAAK,eAAe,KAAK,KAAK,KAAK;AAAA,IAC1D;AAuBA,aAAS,SAAS,OAAO;AACvB,aAAO,SAAS,OAAO,KAAK,aAAa,KAAK;AAAA,IAChD;AA2BA,aAAS,IAAI,QAAQ,MAAM,cAAc;AACvC,UAAI,SAAS,UAAU,OAAO,SAAY,QAAQ,QAAQ,IAAI;AAC9D,aAAO,WAAW,SAAY,eAAe;AAAA,IAC/C;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["string"]}