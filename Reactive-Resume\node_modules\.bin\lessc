#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/less@4.1.3/node_modules/less/bin/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/less@4.1.3/node_modules/less/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/less@4.1.3/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/less@4.1.3/node_modules/less/bin/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/less@4.1.3/node_modules/less/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/less@4.1.3/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/less@4.1.3/node_modules/less/bin/lessc" "$@"
else
  exec node  "$basedir/../.pnpm/less@4.1.3/node_modules/less/bin/lessc" "$@"
fi
