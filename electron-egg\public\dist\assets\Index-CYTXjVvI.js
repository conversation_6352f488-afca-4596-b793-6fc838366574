import{_ as e,c as a,a as s,o as t}from"./index-BEgl07FK.js";const o={id:"hero"},l=e({__name:"Index",setup:e=>(console.log("hello"),(e,l)=>(t(),a("section",o,l[0]||(l[0]=[s('<h1 class="tagline" data-v-e5111575><span class="accent" data-v-e5111575>Electron-Egg</span></h1><p class="description" data-v-e5111575> A fast, desktop software development framework </p><p class="actions" data-v-e5111575><a class="setup" href="https://www.kaka996.com/" target="_blank" data-v-e5111575>Get Started</a></p>',3)]))))},[["__scopeId","data-v-e5111575"],["__file","/Users/<USER>/www/gofile/src/ee/electron-egg/frontend/src/views/example/hello/Index.vue"]]);export{l as default};
