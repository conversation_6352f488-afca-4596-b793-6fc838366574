{"version": 3, "sources": ["../../../.pnpm/react-simple-code-editor@0._7d81c795da9bc2d704e27aa28ca486c6/node_modules/react-simple-code-editor/src/index.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype Padding<T> = T | { top?: T; right?: T; bottom?: T; left?: T };\n\ntype Props = React.HTMLAttributes<HTMLDivElement> & {\n  // Props for the component\n  highlight: (value: string) => string | React.ReactNode;\n  ignoreTabKey?: boolean;\n  insertSpaces?: boolean;\n  onValueChange: (value: string) => void;\n  padding?: Padding<number | string>;\n  style?: React.CSSProperties;\n  tabSize?: number;\n  value: string;\n\n  // Props for the textarea\n  autoFocus?: boolean;\n  disabled?: boolean;\n  form?: string;\n  maxLength?: number;\n  minLength?: number;\n  name?: string;\n  onBlur?: React.FocusEventHandler<HTMLTextAreaElement>;\n  onClick?: React.MouseEventHandler<HTMLTextAreaElement>;\n  onFocus?: React.FocusEventHandler<HTMLTextAreaElement>;\n  onKeyDown?: React.KeyboardEventHandler<HTMLTextAreaElement>;\n  onKeyUp?: React.KeyboardEventHandler<HTMLTextAreaElement>;\n  placeholder?: string;\n  readOnly?: boolean;\n  required?: boolean;\n  textareaClassName?: string;\n  textareaId?: string;\n\n  // Props for the hightlighted code’s pre element\n  preClassName?: string;\n};\n\ntype Record = {\n  value: string;\n  selectionStart: number;\n  selectionEnd: number;\n};\n\ntype History = {\n  stack: (Record & { timestamp: number })[];\n  offset: number;\n};\n\nconst KEYCODE_Y = 89;\nconst KEYCODE_Z = 90;\nconst KEYCODE_M = 77;\nconst KEYCODE_PARENS = 57;\nconst KEYCODE_BRACKETS = 219;\nconst KEYCODE_QUOTE = 222;\nconst KEYCODE_BACK_QUOTE = 192;\n\nconst HISTORY_LIMIT = 100;\nconst HISTORY_TIME_GAP = 3000;\n\nconst isWindows =\n  typeof window !== 'undefined' &&\n  'navigator' in window &&\n  /Win/i.test(navigator.platform);\nconst isMacLike =\n  typeof window !== 'undefined' &&\n  'navigator' in window &&\n  /(Mac|iPhone|iPod|iPad)/i.test(navigator.platform);\n\nconst className = 'npm__react-simple-code-editor__textarea';\n\nconst cssText = /* CSS */ `\n/**\n * Reset the text fill color so that placeholder is visible\n */\n.${className}:empty {\n  -webkit-text-fill-color: inherit !important;\n}\n\n/**\n * Hack to apply on some CSS on IE10 and IE11\n */\n@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n  /**\n    * IE doesn't support '-webkit-text-fill-color'\n    * So we use 'color: transparent' to make the text transparent on IE\n    * Unlike other browsers, it doesn't affect caret color in IE\n    */\n  .${className} {\n    color: transparent !important;\n  }\n\n  .${className}::selection {\n    background-color: #accef7 !important;\n    color: transparent !important;\n  }\n}\n`;\n\nconst Editor = React.forwardRef(function Editor(\n  props: Props,\n  ref: React.Ref<null | { session: { history: History } }>\n) {\n  const {\n    autoFocus,\n    disabled,\n    form,\n    highlight,\n    ignoreTabKey = false,\n    insertSpaces = true,\n    maxLength,\n    minLength,\n    name,\n    onBlur,\n    onClick,\n    onFocus,\n    onKeyDown,\n    onKeyUp,\n    onValueChange,\n    padding = 0,\n    placeholder,\n    preClassName,\n    readOnly,\n    required,\n    style,\n    tabSize = 2,\n    textareaClassName,\n    textareaId,\n    value,\n    ...rest\n  } = props;\n\n  const historyRef = React.useRef<History>({\n    stack: [],\n    offset: -1,\n  });\n  const inputRef = React.useRef<HTMLTextAreaElement | null>(null);\n  const [capture, setCapture] = React.useState(true);\n  const contentStyle = {\n    paddingTop: typeof padding === 'object' ? padding.top : padding,\n    paddingRight: typeof padding === 'object' ? padding.right : padding,\n    paddingBottom: typeof padding === 'object' ? padding.bottom : padding,\n    paddingLeft: typeof padding === 'object' ? padding.left : padding,\n  };\n  const highlighted = highlight(value);\n\n  const getLines = (text: string, position: number) =>\n    text.substring(0, position).split('\\n');\n\n  const recordChange = React.useCallback(\n    (record: Record, overwrite: boolean = false) => {\n      const { stack, offset } = historyRef.current;\n\n      if (stack.length && offset > -1) {\n        // When something updates, drop the redo operations\n        historyRef.current.stack = stack.slice(0, offset + 1);\n\n        // Limit the number of operations to 100\n        const count = historyRef.current.stack.length;\n\n        if (count > HISTORY_LIMIT) {\n          const extras = count - HISTORY_LIMIT;\n\n          historyRef.current.stack = stack.slice(extras, count);\n          historyRef.current.offset = Math.max(\n            historyRef.current.offset - extras,\n            0\n          );\n        }\n      }\n\n      const timestamp = Date.now();\n\n      if (overwrite) {\n        const last = historyRef.current.stack[historyRef.current.offset];\n\n        if (last && timestamp - last.timestamp < HISTORY_TIME_GAP) {\n          // A previous entry exists and was in short interval\n\n          // Match the last word in the line\n          const re = /[^a-z0-9]([a-z0-9]+)$/i;\n\n          // Get the previous line\n          const previous = getLines(last.value, last.selectionStart)\n            .pop()\n            ?.match(re);\n\n          // Get the current line\n          const current = getLines(record.value, record.selectionStart)\n            .pop()\n            ?.match(re);\n\n          if (previous?.[1] && current?.[1]?.startsWith(previous[1])) {\n            // The last word of the previous line and current line match\n            // Overwrite previous entry so that undo will remove whole word\n            historyRef.current.stack[historyRef.current.offset] = {\n              ...record,\n              timestamp,\n            };\n\n            return;\n          }\n        }\n      }\n\n      // Add the new operation to the stack\n      historyRef.current.stack.push({ ...record, timestamp });\n      historyRef.current.offset++;\n    },\n    []\n  );\n\n  const recordCurrentState = React.useCallback(() => {\n    const input = inputRef.current;\n\n    if (!input) return;\n\n    // Save current state of the input\n    const { value, selectionStart, selectionEnd } = input;\n\n    recordChange({\n      value,\n      selectionStart,\n      selectionEnd,\n    });\n  }, [recordChange]);\n\n  const updateInput = (record: Record) => {\n    const input = inputRef.current;\n\n    if (!input) return;\n\n    // Update values and selection state\n    input.value = record.value;\n    input.selectionStart = record.selectionStart;\n    input.selectionEnd = record.selectionEnd;\n\n    onValueChange?.(record.value);\n  };\n\n  const applyEdits = (record: Record) => {\n    // Save last selection state\n    const input = inputRef.current;\n    const last = historyRef.current.stack[historyRef.current.offset];\n\n    if (last && input) {\n      historyRef.current.stack[historyRef.current.offset] = {\n        ...last,\n        selectionStart: input.selectionStart,\n        selectionEnd: input.selectionEnd,\n      };\n    }\n\n    // Save the changes\n    recordChange(record);\n    updateInput(record);\n  };\n\n  const undoEdit = () => {\n    const { stack, offset } = historyRef.current;\n\n    // Get the previous edit\n    const record = stack[offset - 1];\n\n    if (record) {\n      // Apply the changes and update the offset\n      updateInput(record);\n      historyRef.current.offset = Math.max(offset - 1, 0);\n    }\n  };\n\n  const redoEdit = () => {\n    const { stack, offset } = historyRef.current;\n\n    // Get the next edit\n    const record = stack[offset + 1];\n\n    if (record) {\n      // Apply the changes and update the offset\n      updateInput(record);\n      historyRef.current.offset = Math.min(offset + 1, stack.length - 1);\n    }\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\n    if (onKeyDown) {\n      onKeyDown(e);\n\n      if (e.defaultPrevented) {\n        return;\n      }\n    }\n\n    if (e.key === 'Escape') {\n      e.currentTarget.blur();\n    }\n\n    const { value, selectionStart, selectionEnd } = e.currentTarget;\n\n    const tabCharacter = (insertSpaces ? ' ' : '\\t').repeat(tabSize);\n\n    if (e.key === 'Tab' && !ignoreTabKey && capture) {\n      // Prevent focus change\n      e.preventDefault();\n\n      if (e.shiftKey) {\n        // Unindent selected lines\n        const linesBeforeCaret = getLines(value, selectionStart);\n        const startLine = linesBeforeCaret.length - 1;\n        const endLine = getLines(value, selectionEnd).length - 1;\n        const nextValue = value\n          .split('\\n')\n          .map((line, i) => {\n            if (\n              i >= startLine &&\n              i <= endLine &&\n              line.startsWith(tabCharacter)\n            ) {\n              return line.substring(tabCharacter.length);\n            }\n\n            return line;\n          })\n          .join('\\n');\n\n        if (value !== nextValue) {\n          const startLineText = linesBeforeCaret[startLine];\n\n          applyEdits({\n            value: nextValue,\n            // Move the start cursor if first line in selection was modified\n            // It was modified only if it started with a tab\n            selectionStart: startLineText?.startsWith(tabCharacter)\n              ? selectionStart - tabCharacter.length\n              : selectionStart,\n            // Move the end cursor by total number of characters removed\n            selectionEnd: selectionEnd - (value.length - nextValue.length),\n          });\n        }\n      } else if (selectionStart !== selectionEnd) {\n        // Indent selected lines\n        const linesBeforeCaret = getLines(value, selectionStart);\n        const startLine = linesBeforeCaret.length - 1;\n        const endLine = getLines(value, selectionEnd).length - 1;\n        const startLineText = linesBeforeCaret[startLine];\n\n        applyEdits({\n          value: value\n            .split('\\n')\n            .map((line, i) => {\n              if (i >= startLine && i <= endLine) {\n                return tabCharacter + line;\n              }\n\n              return line;\n            })\n            .join('\\n'),\n          // Move the start cursor by number of characters added in first line of selection\n          // Don't move it if it there was no text before cursor\n          selectionStart:\n            startLineText && /\\S/.test(startLineText)\n              ? selectionStart + tabCharacter.length\n              : selectionStart,\n          // Move the end cursor by total number of characters added\n          selectionEnd:\n            selectionEnd + tabCharacter.length * (endLine - startLine + 1),\n        });\n      } else {\n        const updatedSelection = selectionStart + tabCharacter.length;\n\n        applyEdits({\n          // Insert tab character at caret\n          value:\n            value.substring(0, selectionStart) +\n            tabCharacter +\n            value.substring(selectionEnd),\n          // Update caret position\n          selectionStart: updatedSelection,\n          selectionEnd: updatedSelection,\n        });\n      }\n    } else if (e.key === 'Backspace') {\n      const hasSelection = selectionStart !== selectionEnd;\n      const textBeforeCaret = value.substring(0, selectionStart);\n\n      if (textBeforeCaret.endsWith(tabCharacter) && !hasSelection) {\n        // Prevent default delete behaviour\n        e.preventDefault();\n\n        const updatedSelection = selectionStart - tabCharacter.length;\n\n        applyEdits({\n          // Remove tab character at caret\n          value:\n            value.substring(0, selectionStart - tabCharacter.length) +\n            value.substring(selectionEnd),\n          // Update caret position\n          selectionStart: updatedSelection,\n          selectionEnd: updatedSelection,\n        });\n      }\n    } else if (e.key === 'Enter') {\n      // Ignore selections\n      if (selectionStart === selectionEnd) {\n        // Get the current line\n        const line = getLines(value, selectionStart).pop();\n        const matches = line?.match(/^\\s+/);\n\n        if (matches?.[0]) {\n          e.preventDefault();\n\n          // Preserve indentation on inserting a new line\n          const indent = '\\n' + matches[0];\n          const updatedSelection = selectionStart + indent.length;\n\n          applyEdits({\n            // Insert indentation character at caret\n            value:\n              value.substring(0, selectionStart) +\n              indent +\n              value.substring(selectionEnd),\n            // Update caret position\n            selectionStart: updatedSelection,\n            selectionEnd: updatedSelection,\n          });\n        }\n      }\n    } else if (\n      e.keyCode === KEYCODE_PARENS ||\n      e.keyCode === KEYCODE_BRACKETS ||\n      e.keyCode === KEYCODE_QUOTE ||\n      e.keyCode === KEYCODE_BACK_QUOTE\n    ) {\n      let chars;\n\n      if (e.keyCode === KEYCODE_PARENS && e.shiftKey) {\n        chars = ['(', ')'];\n      } else if (e.keyCode === KEYCODE_BRACKETS) {\n        if (e.shiftKey) {\n          chars = ['{', '}'];\n        } else {\n          chars = ['[', ']'];\n        }\n      } else if (e.keyCode === KEYCODE_QUOTE) {\n        if (e.shiftKey) {\n          chars = ['\"', '\"'];\n        } else {\n          chars = [\"'\", \"'\"];\n        }\n      } else if (e.keyCode === KEYCODE_BACK_QUOTE && !e.shiftKey) {\n        chars = ['`', '`'];\n      }\n\n      // If text is selected, wrap them in the characters\n      if (selectionStart !== selectionEnd && chars) {\n        e.preventDefault();\n\n        applyEdits({\n          value:\n            value.substring(0, selectionStart) +\n            chars[0] +\n            value.substring(selectionStart, selectionEnd) +\n            chars[1] +\n            value.substring(selectionEnd),\n          // Update caret position\n          selectionStart,\n          selectionEnd: selectionEnd + 2,\n        });\n      }\n    } else if (\n      (isMacLike\n        ? // Trigger undo with ⌘+Z on Mac\n          e.metaKey && e.keyCode === KEYCODE_Z\n        : // Trigger undo with Ctrl+Z on other platforms\n          e.ctrlKey && e.keyCode === KEYCODE_Z) &&\n      !e.shiftKey &&\n      !e.altKey\n    ) {\n      e.preventDefault();\n\n      undoEdit();\n    } else if (\n      (isMacLike\n        ? // Trigger redo with ⌘+Shift+Z on Mac\n          e.metaKey && e.keyCode === KEYCODE_Z && e.shiftKey\n        : isWindows\n        ? // Trigger redo with Ctrl+Y on Windows\n          e.ctrlKey && e.keyCode === KEYCODE_Y\n        : // Trigger redo with Ctrl+Shift+Z on other platforms\n          e.ctrlKey && e.keyCode === KEYCODE_Z && e.shiftKey) &&\n      !e.altKey\n    ) {\n      e.preventDefault();\n\n      redoEdit();\n    } else if (\n      e.keyCode === KEYCODE_M &&\n      e.ctrlKey &&\n      (isMacLike ? e.shiftKey : true)\n    ) {\n      e.preventDefault();\n\n      // Toggle capturing tab key so users can focus away\n      setCapture((prev) => !prev);\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    const { value, selectionStart, selectionEnd } = e.currentTarget;\n\n    recordChange(\n      {\n        value,\n        selectionStart,\n        selectionEnd,\n      },\n      true\n    );\n\n    onValueChange(value);\n  };\n\n  React.useEffect(() => {\n    recordCurrentState();\n  }, [recordCurrentState]);\n\n  React.useImperativeHandle(\n    ref,\n    () => {\n      return {\n        get session() {\n          return {\n            history: historyRef.current,\n          };\n        },\n        set session(session: { history: History }) {\n          historyRef.current = session.history;\n        },\n      };\n    },\n    []\n  );\n\n  return (\n    <div {...rest} style={{ ...styles.container, ...style }}>\n      <pre\n        className={preClassName}\n        aria-hidden=\"true\"\n        style={{ ...styles.editor, ...styles.highlight, ...contentStyle }}\n        {...(typeof highlighted === 'string'\n          ? { dangerouslySetInnerHTML: { __html: highlighted + '<br />' } }\n          : { children: highlighted })}\n      />\n      <textarea\n        ref={(c) => (inputRef.current = c)}\n        style={{\n          ...styles.editor,\n          ...styles.textarea,\n          ...contentStyle,\n        }}\n        className={\n          className + (textareaClassName ? ` ${textareaClassName}` : '')\n        }\n        id={textareaId}\n        value={value}\n        onChange={handleChange}\n        onKeyDown={handleKeyDown}\n        onClick={onClick}\n        onKeyUp={onKeyUp}\n        onFocus={onFocus}\n        onBlur={onBlur}\n        disabled={disabled}\n        form={form}\n        maxLength={maxLength}\n        minLength={minLength}\n        name={name}\n        placeholder={placeholder}\n        readOnly={readOnly}\n        required={required}\n        autoFocus={autoFocus}\n        autoCapitalize=\"off\"\n        autoComplete=\"off\"\n        autoCorrect=\"off\"\n        spellCheck={false}\n        data-gramm={false}\n      />\n      {/* eslint-disable-next-line react/no-danger */}\n      <style dangerouslySetInnerHTML={{ __html: cssText }} />\n    </div>\n  );\n});\n\nconst styles = {\n  container: {\n    position: 'relative',\n    textAlign: 'left',\n    boxSizing: 'border-box',\n    padding: 0,\n    overflow: 'hidden',\n  },\n  textarea: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    height: '100%',\n    width: '100%',\n    resize: 'none',\n    color: 'inherit',\n    overflow: 'hidden',\n    MozOsxFontSmoothing: 'grayscale',\n    WebkitFontSmoothing: 'antialiased',\n    WebkitTextFillColor: 'transparent',\n  },\n  highlight: {\n    position: 'relative',\n    pointerEvents: 'none',\n  },\n  editor: {\n    margin: 0,\n    border: 0,\n    background: 'none',\n    boxSizing: 'inherit',\n    display: 'inherit',\n    fontFamily: 'inherit',\n    fontSize: 'inherit',\n    fontStyle: 'inherit',\n    fontVariantLigatures: 'inherit',\n    fontWeight: 'inherit',\n    letterSpacing: 'inherit',\n    lineHeight: 'inherit',\n    tabSize: 'inherit',\n    textIndent: 'inherit',\n    textRendering: 'inherit',\n    textTransform: 'inherit',\n    whiteSpace: 'pre-wrap',\n    wordBreak: 'keep-all',\n    overflowWrap: 'break-word',\n  },\n} as const;\n\nexport default Editor;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,QAAA,QAAA,aAAA,eAAA;AAgDA,QAAM,YAAY;AAClB,QAAM,YAAY;AAClB,QAAM,YAAY;AAClB,QAAM,iBAAiB;AACvB,QAAM,mBAAmB;AACzB,QAAM,gBAAgB;AACtB,QAAM,qBAAqB;AAE3B,QAAM,gBAAgB;AACtB,QAAM,mBAAmB;AAEzB,QAAM,YACJ,OAAO,WAAW,eAClB,eAAe,UACf,OAAO,KAAK,UAAU,QAAQ;AAChC,QAAM,YACJ,OAAO,WAAW,eAClB,eAAe,UACf,0BAA0B,KAAK,UAAU,QAAQ;AAEnD,QAAM,YAAY;AAElB,QAAM;;MAAoB,6EAAA,OAIvB,WAAS,oZAAA,EAAA,OAaP,WAAS,oDAAA,EAAA,OAIT,WAAS,wGAAA;;AAOd,QAAM,SAAS,MAAM,WAAW,SAASA,QACvC,OACA,KAAwD;AAGtD,UAAA,YA0BE,MAAK,WAzBP,WAyBE,MAAK,UAxBP,OAwBE,MAAK,MAvBP,YAuBE,MAAK,WAtBP,KAsBE,MAAK,cAtBP,eAAY,OAAA,SAAG,QAAK,IACpB,KAqBE,MAAK,cArBP,eAAY,OAAA,SAAG,OAAI,IACnB,YAoBE,MAAK,WAnBP,YAmBE,MAAK,WAlBP,OAkBE,MAAK,MAjBP,SAiBE,MAAK,QAhBP,UAgBE,MAAK,SAfP,UAeE,MAAK,SAdP,YAcE,MAAK,WAbP,UAaE,MAAK,SAZP,gBAYE,MAAK,eAXP,KAWE,MAAK,SAXP,UAAO,OAAA,SAAG,IAAC,IACX,cAUE,MAAK,aATP,eASE,MAAK,cARP,WAQE,MAAK,UAPP,WAOE,MAAK,UANP,QAME,MAAK,OALP,KAKE,MAAK,SALP,UAAO,OAAA,SAAG,IAAC,IACX,oBAIE,MAAK,mBAHP,aAGE,MAAK,YAFP,QAEE,MAAK,OADJ,OAAI,OACL,OA3BE,CAAA,aAAA,YAAA,QAAA,aAAA,gBAAA,gBAAA,aAAA,aAAA,QAAA,UAAA,WAAA,WAAA,aAAA,WAAA,iBAAA,WAAA,eAAA,gBAAA,YAAA,YAAA,SAAA,WAAA,qBAAA,cAAA,OAAA,CA2BL;AAED,UAAM,aAAa,MAAM,OAAgB;QACvC,OAAO,CAAA;QACP,QAAQ;OACT;AACD,UAAM,WAAW,MAAM,OAAmC,IAAI;AACxD,UAAA,KAAwB,MAAM,SAAS,IAAI,GAA1C,UAAO,GAAA,CAAA,GAAE,aAAU,GAAA,CAAA;AAC1B,UAAM,eAAe;QACnB,YAAY,OAAO,YAAY,WAAW,QAAQ,MAAM;QACxD,cAAc,OAAO,YAAY,WAAW,QAAQ,QAAQ;QAC5D,eAAe,OAAO,YAAY,WAAW,QAAQ,SAAS;QAC9D,aAAa,OAAO,YAAY,WAAW,QAAQ,OAAO;;AAE5D,UAAM,cAAc,UAAU,KAAK;AAEnC,UAAM,WAAW,SAAC,MAAc,UAAgB;AAC9C,eAAA,KAAK,UAAU,GAAG,QAAQ,EAAE,MAAM,IAAI;MAAtC;AAEF,UAAM,eAAe,MAAM,YACzB,SAAC,QAAgB,WAA0B;;AAA1B,YAAA,cAAA,QAAA;AAAA,sBAAA;QAA0B;AACnC,YAAAC,MAAoB,WAAW,SAA7B,QAAKA,IAAA,OAAE,SAAMA,IAAA;AAErB,YAAI,MAAM,UAAU,SAAS,IAAI;AAE/B,qBAAW,QAAQ,QAAQ,MAAM,MAAM,GAAG,SAAS,CAAC;AAGpD,cAAM,QAAQ,WAAW,QAAQ,MAAM;AAEvC,cAAI,QAAQ,eAAe;AACzB,gBAAM,SAAS,QAAQ;AAEvB,uBAAW,QAAQ,QAAQ,MAAM,MAAM,QAAQ,KAAK;AACpD,uBAAW,QAAQ,SAAS,KAAK,IAC/B,WAAW,QAAQ,SAAS,QAC5B,CAAC;;;AAKP,YAAM,YAAY,KAAK,IAAG;AAE1B,YAAI,WAAW;AACb,cAAM,OAAO,WAAW,QAAQ,MAAM,WAAW,QAAQ,MAAM;AAE/D,cAAI,QAAQ,YAAY,KAAK,YAAY,kBAAkB;AAIzD,gBAAM,KAAK;AAGX,gBAAM,YAAWC,MAAA,SAAS,KAAK,OAAO,KAAK,cAAc,EACtD,IAAG,OAAE,QAAAA,QAAA,SAAA,SAAAA,IACJ,MAAM,EAAE;AAGZ,gBAAM,WAAUC,MAAA,SAAS,OAAO,OAAO,OAAO,cAAc,EACzD,IAAG,OAAE,QAAAA,QAAA,SAAA,SAAAA,IACJ,MAAM,EAAE;AAEZ,iBAAI,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAW,CAAC,QAAKC,MAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAU,CAAC,OAAC,QAAAA,QAAA,SAAA,SAAAA,IAAE,WAAW,SAAS,CAAC,CAAC,IAAG;AAG1D,yBAAW,QAAQ,MAAM,WAAW,QAAQ,MAAM,IAAC,SAAA,SAAA,CAAA,GAC9C,MAAM,GAAA,EACT,UAAS,CAAA;AAGX;;;;AAMN,mBAAW,QAAQ,MAAM,KAAI,SAAA,SAAA,CAAA,GAAM,MAAM,GAAA,EAAE,UAAS,CAAA,CAAA;AACpD,mBAAW,QAAQ;MACrB,GACA,CAAA,CAAE;AAGJ,UAAM,qBAAqB,MAAM,YAAY,WAAA;AAC3C,YAAM,QAAQ,SAAS;AAEvB,YAAI,CAAC;AAAO;AAGJ,YAAAC,SAAwC,MAAK,OAAtC,iBAAiC,MAAK,gBAAtB,eAAiB,MAAK;AAErD,qBAAa;UACX,OAAKA;UACL;UACA;SACD;MACH,GAAG,CAAC,YAAY,CAAC;AAEjB,UAAM,cAAc,SAAC,QAAc;AACjC,YAAM,QAAQ,SAAS;AAEvB,YAAI,CAAC;AAAO;AAGZ,cAAM,QAAQ,OAAO;AACrB,cAAM,iBAAiB,OAAO;AAC9B,cAAM,eAAe,OAAO;AAE5B,0BAAa,QAAb,kBAAa,SAAA,SAAb,cAAgB,OAAO,KAAK;MAC9B;AAEA,UAAM,aAAa,SAAC,QAAc;AAEhC,YAAM,QAAQ,SAAS;AACvB,YAAM,OAAO,WAAW,QAAQ,MAAM,WAAW,QAAQ,MAAM;AAE/D,YAAI,QAAQ,OAAO;AACjB,qBAAW,QAAQ,MAAM,WAAW,QAAQ,MAAM,IAAC,SAAA,SAAA,CAAA,GAC9C,IAAI,GAAA,EACP,gBAAgB,MAAM,gBACtB,cAAc,MAAM,aAAY,CAAA;;AAKpC,qBAAa,MAAM;AACnB,oBAAY,MAAM;MACpB;AAEA,UAAM,WAAW,WAAA;AACT,YAAAH,MAAoB,WAAW,SAA7B,QAAKA,IAAA,OAAE,SAAMA,IAAA;AAGrB,YAAM,SAAS,MAAM,SAAS,CAAC;AAE/B,YAAI,QAAQ;AAEV,sBAAY,MAAM;AAClB,qBAAW,QAAQ,SAAS,KAAK,IAAI,SAAS,GAAG,CAAC;;MAEtD;AAEA,UAAM,WAAW,WAAA;AACT,YAAAA,MAAoB,WAAW,SAA7B,QAAKA,IAAA,OAAE,SAAMA,IAAA;AAGrB,YAAM,SAAS,MAAM,SAAS,CAAC;AAE/B,YAAI,QAAQ;AAEV,sBAAY,MAAM;AAClB,qBAAW,QAAQ,SAAS,KAAK,IAAI,SAAS,GAAG,MAAM,SAAS,CAAC;;MAErE;AAEA,UAAM,gBAAgB,SAAC,GAA2C;AAChE,YAAI,WAAW;AACb,oBAAU,CAAC;AAEX,cAAI,EAAE,kBAAkB;AACtB;;;AAIJ,YAAI,EAAE,QAAQ,UAAU;AACtB,YAAE,cAAc,KAAI;;AAGhB,YAAAA,MAA0C,EAAE,eAA1CG,SAAKH,IAAA,OAAE,iBAAcA,IAAA,gBAAE,eAAYA,IAAA;AAE3C,YAAM,gBAAgB,eAAe,MAAM,KAAM,OAAO,OAAO;AAE/D,YAAI,EAAE,QAAQ,SAAS,CAAC,gBAAgB,SAAS;AAE/C,YAAE,eAAc;AAEhB,cAAI,EAAE,UAAU;AAEd,gBAAM,mBAAmB,SAASG,QAAO,cAAc;AACvD,gBAAM,cAAY,iBAAiB,SAAS;AAC5C,gBAAM,YAAU,SAASA,QAAO,YAAY,EAAE,SAAS;AACvD,gBAAM,YAAYA,OACf,MAAM,IAAI,EACV,IAAI,SAACC,OAAM,GAAC;AACX,kBACE,KAAK,eACL,KAAK,aACLA,MAAK,WAAW,YAAY,GAC5B;AACA,uBAAOA,MAAK,UAAU,aAAa,MAAM;;AAG3C,qBAAOA;YACT,CAAC,EACA,KAAK,IAAI;AAEZ,gBAAID,WAAU,WAAW;AACvB,kBAAM,gBAAgB,iBAAiB,WAAS;AAEhD,yBAAW;gBACT,OAAO;;;gBAGP,iBAAgB,kBAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,WAAW,YAAY,KAClD,iBAAiB,aAAa,SAC9B;;gBAEJ,cAAc,gBAAgBA,OAAM,SAAS,UAAU;eACxD;;qBAEM,mBAAmB,cAAc;AAE1C,gBAAM,mBAAmB,SAASA,QAAO,cAAc;AACvD,gBAAM,cAAY,iBAAiB,SAAS;AAC5C,gBAAM,YAAU,SAASA,QAAO,YAAY,EAAE,SAAS;AACvD,gBAAM,gBAAgB,iBAAiB,WAAS;AAEhD,uBAAW;cACT,OAAOA,OACJ,MAAM,IAAI,EACV,IAAI,SAACC,OAAM,GAAC;AACX,oBAAI,KAAK,eAAa,KAAK,WAAS;AAClC,yBAAO,eAAeA;;AAGxB,uBAAOA;cACT,CAAC,EACA,KAAK,IAAI;;;cAGZ,gBACE,iBAAiB,KAAK,KAAK,aAAa,IACpC,iBAAiB,aAAa,SAC9B;;cAEN,cACE,eAAe,aAAa,UAAU,YAAU,cAAY;aAC/D;iBACI;AACL,gBAAM,mBAAmB,iBAAiB,aAAa;AAEvD,uBAAW;;cAET,OACED,OAAM,UAAU,GAAG,cAAc,IACjC,eACAA,OAAM,UAAU,YAAY;;cAE9B,gBAAgB;cAChB,cAAc;aACf;;mBAEM,EAAE,QAAQ,aAAa;AAChC,cAAM,eAAe,mBAAmB;AACxC,cAAM,kBAAkBA,OAAM,UAAU,GAAG,cAAc;AAEzD,cAAI,gBAAgB,SAAS,YAAY,KAAK,CAAC,cAAc;AAE3D,cAAE,eAAc;AAEhB,gBAAM,mBAAmB,iBAAiB,aAAa;AAEvD,uBAAW;;cAET,OACEA,OAAM,UAAU,GAAG,iBAAiB,aAAa,MAAM,IACvDA,OAAM,UAAU,YAAY;;cAE9B,gBAAgB;cAChB,cAAc;aACf;;mBAEM,EAAE,QAAQ,SAAS;AAE5B,cAAI,mBAAmB,cAAc;AAEnC,gBAAM,OAAO,SAASA,QAAO,cAAc,EAAE,IAAG;AAChD,gBAAM,UAAU,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,MAAM,MAAM;AAElC,gBAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAU,CAAC,GAAG;AAChB,gBAAE,eAAc;AAGhB,kBAAM,SAAS,OAAO,QAAQ,CAAC;AAC/B,kBAAM,mBAAmB,iBAAiB,OAAO;AAEjD,yBAAW;;gBAET,OACEA,OAAM,UAAU,GAAG,cAAc,IACjC,SACAA,OAAM,UAAU,YAAY;;gBAE9B,gBAAgB;gBAChB,cAAc;eACf;;;mBAIL,EAAE,YAAY,kBACd,EAAE,YAAY,oBACd,EAAE,YAAY,iBACd,EAAE,YAAY,oBACd;AACA,cAAI,QAAK;AAET,cAAI,EAAE,YAAY,kBAAkB,EAAE,UAAU;AAC9C,oBAAQ,CAAC,KAAK,GAAG;qBACR,EAAE,YAAY,kBAAkB;AACzC,gBAAI,EAAE,UAAU;AACd,sBAAQ,CAAC,KAAK,GAAG;mBACZ;AACL,sBAAQ,CAAC,KAAK,GAAG;;qBAEV,EAAE,YAAY,eAAe;AACtC,gBAAI,EAAE,UAAU;AACd,sBAAQ,CAAC,KAAK,GAAG;mBACZ;AACL,sBAAQ,CAAC,KAAK,GAAG;;qBAEV,EAAE,YAAY,sBAAsB,CAAC,EAAE,UAAU;AAC1D,oBAAQ,CAAC,KAAK,GAAG;;AAInB,cAAI,mBAAmB,gBAAgB,OAAO;AAC5C,cAAE,eAAc;AAEhB,uBAAW;cACT,OACEA,OAAM,UAAU,GAAG,cAAc,IACjC,MAAM,CAAC,IACPA,OAAM,UAAU,gBAAgB,YAAY,IAC5C,MAAM,CAAC,IACPA,OAAM,UAAU,YAAY;;cAE9B;cACA,cAAc,eAAe;aAC9B;;oBAGF;;UAEG,EAAE,WAAW,EAAE,YAAY;;;UAE3B,EAAE,WAAW,EAAE,YAAY;cAC/B,CAAC,EAAE,YACH,CAAC,EAAE,QACH;AACA,YAAE,eAAc;AAEhB,mBAAQ;oBAEP;;UAEG,EAAE,WAAW,EAAE,YAAY,aAAa,EAAE;YAC1C;;UAEA,EAAE,WAAW,EAAE,YAAY;;;UAE3B,EAAE,WAAW,EAAE,YAAY,aAAa,EAAE;cAC9C,CAAC,EAAE,QACH;AACA,YAAE,eAAc;AAEhB,mBAAQ;mBAER,EAAE,YAAY,aACd,EAAE,YACD,YAAY,EAAE,WAAW,OAC1B;AACA,YAAE,eAAc;AAGhB,qBAAW,SAAC,MAAI;AAAK,mBAAA,CAAC;UAAD,CAAK;;MAE9B;AAEA,UAAM,eAAe,SAAC,GAAyC;AACvD,YAAAH,MAA0C,EAAE,eAA1CG,SAAKH,IAAA,OAAE,iBAAcA,IAAA,gBAAE,eAAYA,IAAA;AAE3C,qBACE;UACE,OAAKG;UACL;UACA;WAEF,IAAI;AAGN,sBAAcA,MAAK;MACrB;AAEA,YAAM,UAAU,WAAA;AACd,2BAAkB;MACpB,GAAG,CAAC,kBAAkB,CAAC;AAEvB,YAAM,oBACJ,KACA,WAAA;AACE,eAAO;UACL,IAAI,UAAO;AACT,mBAAO;cACL,SAAS,WAAW;;UAExB;UACA,IAAI,QAAQ,SAA6B;AACvC,uBAAW,UAAU,QAAQ;UAC/B;;MAEJ,GACA,CAAA,CAAE;AAGJ,aACE,MAAA;QAAA;QAAA,SAAA,CAAA,GAAS,MAAI,EAAE,OAAK,SAAA,SAAA,CAAA,GAAO,OAAO,SAAS,GAAK,KAAK,EAAA,CAAA;QACnD,MAAA,cAAA,OAAA,SAAA,EACE,WAAW,cAAY,eACX,QACZ,OAAK,SAAA,SAAA,SAAA,CAAA,GAAO,OAAO,MAAM,GAAK,OAAO,SAAS,GAAK,YAAY,EAAA,GAC1D,OAAO,gBAAgB,WACxB,EAAE,yBAAyB,EAAE,QAAQ,cAAc,SAAQ,EAAE,IAC7D,EAAE,UAAU,YAAW,CAAG,CAAA;QAEhC,MAAA,cAAA,YAAA,EACE,KAAK,SAAC,GAAC;AAAK,iBAAC,SAAS,UAAU;QAApB,GACZ,OAAK,SAAA,SAAA,SAAA,CAAA,GACA,OAAO,MAAM,GACb,OAAO,QAAQ,GACf,YAAY,GAEjB,WACE,aAAa,oBAAoB,IAAA,OAAI,iBAAiB,IAAK,KAE7D,IAAI,YACJ,OACA,UAAU,cACV,WAAW,eACX,SACA,SACA,SACA,QACA,UACA,MACA,WACA,WACA,MACA,aACA,UACA,UACA,WACA,gBAAe,OACf,cAAa,OACb,aAAY,OACZ,YAAY,OAAK,cACL,MAAK,CAAA;QAGnB,MAAA,cAAA,SAAA,EAAO,yBAAyB,EAAE,QAAQ,QAAO,EAAE,CAAA;MAAI;IAG7D,CAAC;AAED,QAAM,SAAS;MACb,WAAW;QACT,UAAU;QACV,WAAW;QACX,WAAW;QACX,SAAS;QACT,UAAU;;MAEZ,UAAU;QACR,UAAU;QACV,KAAK;QACL,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;QACV,qBAAqB;QACrB,qBAAqB;QACrB,qBAAqB;;MAEvB,WAAW;QACT,UAAU;QACV,eAAe;;MAEjB,QAAQ;QACN,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,SAAS;QACT,YAAY;QACZ,UAAU;QACV,WAAW;QACX,sBAAsB;QACtB,YAAY;QACZ,eAAe;QACf,YAAY;QACZ,SAAS;QACT,YAAY;QACZ,eAAe;QACf,eAAe;QACf,YAAY;QACZ,WAAW;QACX,cAAc;;;AAIlB,YAAA,UAAe;;;", "names": ["Editor", "_d", "_a", "_b", "_c", "value", "line"]}