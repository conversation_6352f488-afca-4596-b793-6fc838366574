@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d\node_modules\vite\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d\node_modules\vite\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d\node_modules\vite\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d\node_modules\vite\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\..\vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d\node_modules\vite\bin\vite.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\..\vite@5.4.14_@types+node@22._7d908d7454ac5e5bb091b05be9b38d5d\node_modules\vite\bin\vite.js" %*
)
