#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7/node_modules/webpack-dev-server/bin/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7/node_modules/webpack-dev-server/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7/node_modules/webpack-dev-server/bin/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7/node_modules/webpack-dev-server/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7/node_modules/webpack-dev-server/bin/webpack-dev-server.js" "$@"
else
  exec node  "$basedir/../../../../../../webpack-dev-server@5.2.0_we_b87806ba9bf7518d9bacc4eaf55f02d7/node_modules/webpack-dev-server/bin/webpack-dev-server.js" "$@"
fi
