@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\eslint-config-prettier@9.1.0_eslint@8.57.1\node_modules\eslint-config-prettier\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\eslint-config-prettier@9.1.0_eslint@8.57.1\node_modules\eslint-config-prettier\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\eslint-config-prettier@9.1.0_eslint@8.57.1\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\eslint-config-prettier@9.1.0_eslint@8.57.1\node_modules\eslint-config-prettier\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\eslint-config-prettier@9.1.0_eslint@8.57.1\node_modules\eslint-config-prettier\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\eslint-config-prettier@9.1.0_eslint@8.57.1\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\eslint-config-prettier@9.1.0_eslint@8.57.1\node_modules\eslint-config-prettier\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\eslint-config-prettier@9.1.0_eslint@8.57.1\node_modules\eslint-config-prettier\bin\cli.js" %*
)
