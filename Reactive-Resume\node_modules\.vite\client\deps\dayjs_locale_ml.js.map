{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/ml.js"], "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_ml=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"ml\",weekdays:\"ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച\".split(\"_\"),months:\"ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ\".split(\"_\"),weekdaysShort:\"ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി\".split(\"_\"),monthsShort:\"ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.\".split(\"_\"),weekdaysMin:\"ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"A h:mm -നു\",LTS:\"A h:mm:ss -നു\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, A h:mm -നു\",LLLL:\"dddd, D MMMM YYYY, A h:mm -നു\"},relativeTime:{future:\"%s കഴിഞ്ഞ്\",past:\"%s മുൻപ്\",s:\"അൽപ നിമിഷങ്ങൾ\",m:\"ഒരു മിനിറ്റ്\",mm:\"%d മിനിറ്റ്\",h:\"ഒരു മണിക്കൂർ\",hh:\"%d മണിക്കൂർ\",d:\"ഒരു ദിവസം\",dd:\"%d ദിവസം\",M:\"ഒരു മാസം\",MM:\"%d മാസം\",y:\"ഒരു വർഷം\",yy:\"%d വർഷം\"}};return t.default.locale(d,null,!0),d}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,wEAAwE,MAAM,GAAG,GAAE,QAAO,yFAAyF,MAAM,GAAG,GAAE,eAAc,2CAA2C,MAAM,GAAG,GAAE,aAAY,yEAAyE,MAAM,GAAG,GAAE,aAAY,wBAAwB,MAAM,GAAG,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA;AAAA,MAAC,GAAE,SAAQ,EAAC,IAAG,cAAa,KAAI,iBAAgB,GAAE,cAAa,IAAG,eAAc,KAAI,2BAA0B,MAAK,gCAA+B,GAAE,cAAa,EAAC,QAAO,cAAa,MAAK,YAAW,GAAE,iBAAgB,GAAE,gBAAe,IAAG,eAAc,GAAE,gBAAe,IAAG,eAAc,GAAE,aAAY,IAAG,YAAW,GAAE,YAAW,IAAG,WAAU,GAAE,YAAW,IAAG,UAAS,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["_"]}