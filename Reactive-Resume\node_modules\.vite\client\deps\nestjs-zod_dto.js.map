{"version": 3, "sources": ["../../../.pnpm/nestjs-zod@3.0.0_@nestjs+co_ff15043dbe9c004b45cb1419a483e175/node_modules/nestjs-zod/dist/dto.mjs"], "sourcesContent": ["function createZodDto(schema) {\n  class AugmentedZodDto {\n    static create(input) {\n      return this.schema.parse(input);\n    }\n  }\n  AugmentedZodDto.isZodDto = true;\n  AugmentedZodDto.schema = schema;\n  return AugmentedZodDto;\n}\nfunction isZodDto(metatype) {\n  return metatype == null ? void 0 : metatype.isZodDto;\n}\n\nexport { createZodDto, isZodDto };\n"], "mappings": ";;;AAAA,SAAS,aAAa,QAAQ;AAAA,EAC5B,MAAM,gBAAgB;AAAA,IACpB,OAAO,OAAO,OAAO;AACnB,aAAO,KAAK,OAAO,MAAM,KAAK;AAAA,IAChC;AAAA,EACF;AACA,kBAAgB,WAAW;AAC3B,kBAAgB,SAAS;AACzB,SAAO;AACT;AACA,SAAS,SAAS,UAAU;AAC1B,SAAO,YAAY,OAAO,SAAS,SAAS;AAC9C;", "names": []}