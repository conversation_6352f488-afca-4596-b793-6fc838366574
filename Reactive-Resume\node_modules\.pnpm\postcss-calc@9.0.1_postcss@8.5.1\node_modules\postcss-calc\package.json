{"name": "postcss-calc", "version": "9.0.1", "description": "PostCSS plugin to reduce calc()", "keywords": ["css", "postcss", "postcss-plugin", "calculation", "calc"], "homepage": "https://github.com/postcss/postcss-calc", "repository": {"type": "git", "url": "https://github.com/postcss/postcss-calc.git"}, "main": "src/index.js", "types": "types/index.d.ts", "files": ["src", "types", "LICENSE"], "scripts": {"prepare": "pnpm run build && tsc", "build": "jison ./parser.jison -o src/parser.js", "lint": "eslint . && tsc", "test": "uvu test"}, "author": "<PERSON>", "license": "MIT", "eslintConfig": {"extends": ["eslint:recommended", "prettier"], "env": {"node": true, "es2017": true}, "ignorePatterns": ["src/parser.js"], "rules": {"curly": "error"}}, "engines": {"node": "^14 || ^16 || >=18.0"}, "devDependencies": {"@types/node": "^18.16.2", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "jison-gho": "^0.6.1-216", "postcss": "^8.2.2", "prettier": "^2.8.8", "typescript": "~5.0.4", "uvu": "^0.5.6"}, "dependencies": {"postcss-selector-parser": "^6.0.11", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.2.2"}}