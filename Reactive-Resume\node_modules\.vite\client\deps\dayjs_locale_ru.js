import {
  require_dayjs_min
} from "./chunk-M2A4HNR4.js";
import {
  __commonJS
} from "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/ru.js
var require_ru = __commonJS({
  "node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/ru.js"(exports, module) {
    !function(_, t) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t(require_dayjs_min()) : "function" == typeof define && define.amd ? define(["dayjs"], t) : (_ = "undefined" != typeof globalThis ? globalThis : _ || self).dayjs_locale_ru = t(_.dayjs);
    }(exports, function(_) {
      "use strict";
      function t(_2) {
        return _2 && "object" == typeof _2 && "default" in _2 ? _2 : { default: _2 };
      }
      var e = t(_), n = "января_февраля_марта_апреля_мая_июня_июля_августа_сентября_октября_ноября_декабря".split("_"), s = "январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split("_"), r = "янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.".split("_"), o = "янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.".split("_"), i = /D[oD]?(\[[^[\]]*\]|\s)+MMMM?/;
      function d(_2, t2, e2) {
        var n2, s2;
        return "m" === e2 ? t2 ? "минута" : "минуту" : _2 + " " + (n2 = +_2, s2 = { mm: t2 ? "минута_минуты_минут" : "минуту_минуты_минут", hh: "час_часа_часов", dd: "день_дня_дней", MM: "месяц_месяца_месяцев", yy: "год_года_лет" }[e2].split("_"), n2 % 10 == 1 && n2 % 100 != 11 ? s2[0] : n2 % 10 >= 2 && n2 % 10 <= 4 && (n2 % 100 < 10 || n2 % 100 >= 20) ? s2[1] : s2[2]);
      }
      var u = function(_2, t2) {
        return i.test(t2) ? n[_2.month()] : s[_2.month()];
      };
      u.s = s, u.f = n;
      var a = function(_2, t2) {
        return i.test(t2) ? r[_2.month()] : o[_2.month()];
      };
      a.s = o, a.f = r;
      var m = { name: "ru", weekdays: "воскресенье_понедельник_вторник_среда_четверг_пятница_суббота".split("_"), weekdaysShort: "вск_пнд_втр_срд_чтв_птн_сбт".split("_"), weekdaysMin: "вс_пн_вт_ср_чт_пт_сб".split("_"), months: u, monthsShort: a, weekStart: 1, yearStart: 4, formats: { LT: "H:mm", LTS: "H:mm:ss", L: "DD.MM.YYYY", LL: "D MMMM YYYY г.", LLL: "D MMMM YYYY г., H:mm", LLLL: "dddd, D MMMM YYYY г., H:mm" }, relativeTime: { future: "через %s", past: "%s назад", s: "несколько секунд", m: d, mm: d, h: "час", hh: d, d: "день", dd: d, M: "месяц", MM: d, y: "год", yy: d }, ordinal: function(_2) {
        return _2;
      }, meridiem: function(_2) {
        return _2 < 4 ? "ночи" : _2 < 12 ? "утра" : _2 < 17 ? "дня" : "вечера";
      } };
      return e.default.locale(m, null, true), m;
    });
  }
});
export default require_ru();
//# sourceMappingURL=dayjs_locale_ru.js.map
