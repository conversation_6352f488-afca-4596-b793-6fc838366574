import {
  require_react
} from "./chunk-4ALVB3Y3.js";
import {
  __toESM
} from "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/use-breakpoint@4.0.6_react-_d6f9684c5fc81e6917d1df0ad24612f4/node_modules/use-breakpoint/dist/esm/createMediaQueries.js
var createMediaQueries = (breakpoints) => {
  const sortedBreakpoints = Object.keys(breakpoints).sort((a, b) => breakpoints[b] - breakpoints[a]);
  return sortedBreakpoints.map((breakpoint, index) => {
    let query = "";
    const minWidth = breakpoints[breakpoint];
    const nextBreakpoint = sortedBreakpoints[index - 1];
    const maxWidth = nextBreakpoint ? breakpoints[nextBreakpoint] : null;
    if (minWidth >= 0) {
      query = `(min-width: ${minWidth}px)`;
    }
    if (maxWidth !== null) {
      if (query) {
        query += " and ";
      }
      query += `(max-width: ${maxWidth - 1}px)`;
    }
    const mediaQuery = {
      breakpoint,
      maxWidth: maxWidth ? maxWidth - 1 : null,
      minWidth,
      query
    };
    return mediaQuery;
  });
};
var createMediaQueries_default = createMediaQueries;

// node_modules/.pnpm/use-breakpoint@4.0.6_react-_d6f9684c5fc81e6917d1df0ad24612f4/node_modules/use-breakpoint/dist/esm/getCSSMediaQueries.js
var getCSSMediaQueries = (breakpoints, type) => {
  const typePrefix = type ? `only ${type} and ` : "";
  const queries = createMediaQueries_default(breakpoints);
  return queries.reduce((queries2, { breakpoint, query }) => ({
    ...queries2,
    [breakpoint]: `@media ${typePrefix}${query}`
  }), {});
};
var getCSSMediaQueries_default = getCSSMediaQueries;

// node_modules/.pnpm/use-breakpoint@4.0.6_react-_d6f9684c5fc81e6917d1df0ad24612f4/node_modules/use-breakpoint/dist/esm/useBreakpoint.js
var import_react = __toESM(require_react(), 1);
var EMPTY_BREAKPOINT = {
  breakpoint: null,
  minWidth: null,
  maxWidth: null,
  query: null
};
var useBreakpoint = (config, defaultBreakpoint) => {
  const mediaQueries = (0, import_react.useMemo)(() => createMediaQueries_default(config), [config]);
  const subscribe = (0, import_react.useCallback)((callback) => {
    const unsubscribers = [];
    mediaQueries.forEach(({ query }) => {
      const list = window.matchMedia(query);
      const supportsNewEventListeners = "addEventListener" in list && "removeEventListener" in list;
      if (supportsNewEventListeners) {
        list.addEventListener("change", callback);
      } else {
        ;
        list.addListener(callback);
      }
      unsubscribers.push(supportsNewEventListeners ? () => list.removeEventListener("change", callback) : () => list.removeListener(callback));
    });
    return () => unsubscribers.forEach((unsubscriber) => unsubscriber());
  }, [mediaQueries]);
  const getSnapshot = (0, import_react.useCallback)(() => {
    const mediaMatch = mediaQueries.find((mediaQuery) => window.matchMedia(mediaQuery.query).matches);
    if (mediaMatch)
      return mediaMatch;
    if (defaultBreakpoint) {
      const defaultMatch = mediaQueries.find((mediaQuery) => mediaQuery.breakpoint === defaultBreakpoint);
      if (defaultMatch)
        return defaultMatch;
    }
    return EMPTY_BREAKPOINT;
  }, [defaultBreakpoint, mediaQueries]);
  const getServerSnapshot = (0, import_react.useCallback)(() => {
    const defaultMatch = mediaQueries.find((mediaQuery) => mediaQuery.breakpoint === defaultBreakpoint);
    return defaultMatch ?? EMPTY_BREAKPOINT;
  }, [defaultBreakpoint, mediaQueries]);
  const currentBreakpoint = (0, import_react.useSyncExternalStore)(subscribe, getSnapshot, getServerSnapshot);
  (0, import_react.useDebugValue)(currentBreakpoint, (c) => typeof c.breakpoint === "string" ? `${c.breakpoint} (${c.minWidth} ≤ x${c.maxWidth ? ` < ${c.maxWidth + 1}` : ""})` : "");
  return currentBreakpoint;
};
var useBreakpoint_default = useBreakpoint;
export {
  useBreakpoint_default as default,
  getCSSMediaQueries_default as getCSSMediaQueries,
  useBreakpoint_default as useBreakpoint
};
//# sourceMappingURL=use-breakpoint.js.map
