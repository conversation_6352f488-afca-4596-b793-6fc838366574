{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/lt.js"], "sourcesContent": ["!function(e,s){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=s(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],s):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_lt=s(e.dayjs)}(this,(function(e){\"use strict\";function s(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var i=s(e),d=\"sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio\".split(\"_\"),a=\"sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis\".split(\"_\"),l=/D[oD]?(\\[[^\\[\\]]*\\]|\\s)+MMMM?|MMMM?(\\[[^\\[\\]]*\\]|\\s)+D[oD]?/,M=function(e,s){return l.test(s)?d[e.month()]:a[e.month()]};M.s=a,M.f=d;var t={name:\"lt\",weekdays:\"sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis\".split(\"_\"),weekdaysShort:\"sek_pir_ant_tre_ket_pen_šeš\".split(\"_\"),weekdaysMin:\"s_p_a_t_k_pn_š\".split(\"_\"),months:M,monthsShort:\"sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd\".split(\"_\"),ordinal:function(e){return e+\".\"},weekStart:1,relativeTime:{future:\"už %s\",past:\"prieš %s\",s:\"kelias sekundes\",m:\"minutę\",mm:\"%d minutes\",h:\"valandą\",hh:\"%d valandas\",d:\"dieną\",dd:\"%d dienas\",M:\"mėnesį\",MM:\"%d mėnesius\",y:\"metus\",yy:\"%d metus\"},format:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"YYYY [m.] MMMM D [d.]\",LLL:\"YYYY [m.] MMMM D [d.], HH:mm [val.]\",LLLL:\"YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]\",l:\"YYYY-MM-DD\",ll:\"YYYY [m.] MMMM D [d.]\",lll:\"YYYY [m.] MMMM D [d.], HH:mm [val.]\",llll:\"YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"YYYY [m.] MMMM D [d.]\",LLL:\"YYYY [m.] MMMM D [d.], HH:mm [val.]\",LLLL:\"YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]\",l:\"YYYY-MM-DD\",ll:\"YYYY [m.] MMMM D [d.]\",lll:\"YYYY [m.] MMMM D [d.], HH:mm [val.]\",llll:\"YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]\"}};return i.default.locale(t,null,!0),t}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,oGAAoG,MAAM,GAAG,GAAE,IAAE,kGAAkG,MAAM,GAAG,GAAE,IAAE,+DAA8D,IAAE,SAASA,IAAEC,IAAE;AAAC,eAAO,EAAE,KAAKA,EAAC,IAAE,EAAED,GAAE,MAAM,CAAC,IAAE,EAAEA,GAAE,MAAM,CAAC;AAAA,MAAC;AAAE,QAAE,IAAE,GAAE,EAAE,IAAE;AAAE,UAAI,IAAE,EAAC,MAAK,MAAK,UAAS,2FAA2F,MAAM,GAAG,GAAE,eAAc,8BAA8B,MAAM,GAAG,GAAE,aAAY,iBAAiB,MAAM,GAAG,GAAE,QAAO,GAAE,aAAY,kDAAkD,MAAM,GAAG,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA,KAAE;AAAA,MAAG,GAAE,WAAU,GAAE,cAAa,EAAC,QAAO,SAAQ,MAAK,YAAW,GAAE,mBAAkB,GAAE,UAAS,IAAG,cAAa,GAAE,WAAU,IAAG,eAAc,GAAE,SAAQ,IAAG,aAAY,GAAE,UAAS,IAAG,eAAc,GAAE,SAAQ,IAAG,WAAU,GAAE,QAAO,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,yBAAwB,KAAI,uCAAsC,MAAK,6CAA4C,GAAE,cAAa,IAAG,yBAAwB,KAAI,uCAAsC,MAAK,2CAA0C,GAAE,SAAQ,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,yBAAwB,KAAI,uCAAsC,MAAK,6CAA4C,GAAE,cAAa,IAAG,yBAAwB,KAAI,uCAAsC,MAAK,2CAA0C,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e", "s"]}