{"version": "6.0", "nxVersion": "19.8.14", "deps": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@fontsource/ibm-plex-sans": "^5.1.1", "@hookform/resolvers": "^3.10.0", "@lingui/core": "^4.14.1", "@lingui/detect-locale": "^4.14.1", "@lingui/macro": "^4.14.1", "@lingui/react": "^4.14.1", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^3.1.3", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^10.3.0", "@paralleldrive/cuid2": "^2.2.2", "@phosphor-icons/react": "^2.1.7", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.5", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-hover-card": "^1.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-portal": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.7", "@radix-ui/react-visually-hidden": "^1.1.1", "@sindresorhus/slugify": "^1.1.2", "@swc/helpers": "^0.5.15", "@tanstack/react-query": "^5.66.0", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/passport-jwt": "^4.0.1", "async-retry": "^1.3.3", "axios": "^1.7.9", "axios-auth-refresh": "^3.3.6", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cookie-parser": "^1.4.7", "dayjs": "^1.11.13", "deepmerge": "^4.3.1", "express-session": "^1.18.1", "file-saver": "^2.0.5", "framer-motion": "^11.18.2", "fuzzy": "^0.1.3", "helmet": "^7.2.0", "immer": "^10.1.1", "jszip": "^3.10.1", "lodash.debounce": "^4.0.8", "lodash.get": "^4.4.2", "lodash.set": "^4.3.2", "minio": "^8.0.4", "nest-raven": "^10.1.0", "nestjs-minio-client": "^2.2.0", "nestjs-prisma": "^0.24.0", "nestjs-zod": "^3.0.0", "nodemailer": "^6.10.0", "openai": "^4.82.0", "otplib": "^12.0.1", "papaparse": "^5.5.2", "passport": "^0.7.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-openidconnect": "^0.1.2", "pdf-lib": "^1.17.1", "prisma": "^5.22.0", "prismjs": "^1.29.0", "puppeteer": "^23.11.1", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-parallax-tilt": "^1.7.277", "react-resizable-panels": "^2.1.7", "react-router": "^7.1.5", "react-simple-code-editor": "^0.14.1", "react-zoom-pan-pinch": "^3.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sanitize-html": "^2.14.0", "sharp": "^0.33.5", "tailwind-merge": "^2.6.0", "tslib": "^2.8.1", "unique-names-generator": "^4.7.1", "use-breakpoint": "^4.0.6", "use-keyboard-shortcut": "^1.1.6", "usehooks-ts": "^3.1.0", "webfontloader": "^1.6.28", "zod": "^3.24.1", "zod-to-json-schema": "^3.24.1", "zundo": "^2.3.0", "zustand": "^4.5.6", "@babel/core": "^7.26.7", "@babel/preset-react": "^7.26.3", "@lingui/cli": "^4.14.1", "@lingui/conf": "^4.14.1", "@lingui/swc-plugin": "^4.1.0", "@lingui/vite-plugin": "^4.14.1", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.15", "@nx/eslint": "^19.8.14", "@nx/eslint-plugin": "^19.8.14", "@nx/jest": "^19.8.14", "@nx/js": "^19.8.14", "@nx/nest": "^19.8.14", "@nx/node": "^19.8.14", "@nx/react": "^19.8.14", "@nx/vite": "^19.8.14", "@nx/web": "^19.8.14", "@nx/webpack": "^19.8.14", "@nx/workspace": "^19.8.14", "@swc-node/register": "^1.10.9", "@swc/cli": "^0.4.0", "@swc/core": "^1.10.12", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-plugin-query": "^5.66.0", "@testing-library/react": "^16.2.0", "@tiptap/core": "^2.11.5", "@types/async-retry": "^1.4.9", "@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/express": "^4.17.21", "@types/express-session": "^1.18.1", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/lodash.debounce": "^4.0.9", "@types/lodash.get": "^4.4.9", "@types/lodash.set": "^4.3.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.0", "@types/nodemailer": "^6.4.17", "@types/papaparse": "^5.3.15", "@types/passport": "^1.0.17", "@types/passport-github2": "^1.2.9", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-local": "^1.0.38", "@types/passport-openidconnect": "^0.1.3", "@types/prismjs": "^1.26.5", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-is": "^18.3.1", "@types/retry": "^0.12.5", "@types/sanitize-html": "^2.13.0", "@types/webfontloader": "^1.6.38", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.7.2", "@vitest/coverage-v8": "^2.1.9", "@vitest/ui": "^2.1.9", "autoprefixer": "^10.4.20", "babel-plugin-macros": "^3.1.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-lingui": "^0.9.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-plugin-unused-imports": "^3.2.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jsdom": "^25.0.1", "nx": "^19.8.14", "postcss": "^8.5.1", "postcss-import": "^16.1.0", "postcss-nested": "^6.2.0", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.3", "vite": "^5.4.14", "vite-plugin-dts": "^4.5.0", "vitest": "^2.1.9"}, "pathMappings": {"@/artboard/*": ["apps/artboard/src/*"], "@/client/*": ["apps/client/src/*"], "@/server/*": ["apps/server/src/*"], "@reactive-resume/dto": ["libs/dto/src/index.ts"], "@reactive-resume/hooks": ["libs/hooks/src/index.ts"], "@reactive-resume/parser": ["libs/parser/src/index.ts"], "@reactive-resume/schema": ["libs/schema/src/index.ts"], "@reactive-resume/ui": ["libs/ui/src/index.ts"], "@reactive-resume/utils": ["libs/utils/src/index.ts"]}, "nxJsonPlugins": [], "fileMap": {"nonProjectFiles": [{"file": ".github/workflows/lint-test-build.yml", "hash": "8418917363117502375"}, {"file": ".giti<PERSON>re", "hash": "4117233908818551111"}, {"file": ".vscode/extensions.json", "hash": "12445822510582588631"}, {"file": "tools/prisma/migrations/migration_lock.toml", "hash": "5278839983338653787"}, {"file": "crowdin.yml", "hash": "15901768653768057054"}, {"file": ".github/workflows/publish-docker-image.yml", "hash": "7809655296868985192"}, {"file": "compose.dev.yml", "hash": "15216111348143703403"}, {"file": "Dockerfile", "hash": "15475753122066081520"}, {"file": ".env.example", "hash": "4165741585049935841"}, {"file": ".github/workflows/sync-crowdin-translations.yml", "hash": "3853594830290135576"}, {"file": ".editorconfig", "hash": "8624325672896566142"}, {"file": "CHANGELOG.md", "hash": "8187883505443328368"}, {"file": ".ncurc.json", "hash": "5525786917425845911"}, {"file": "LICENSE.md", "hash": "18047873851266146727"}, {"file": "tailwind.config.js", "hash": "11508948306749532828"}, {"file": "tools/compose/swarm.yml", "hash": "3144728483096744760"}, {"file": "compose.yml", "hash": "7522214389287187174"}, {"file": "CODE_OF_CONDUCT.md", "hash": "2988786534876899087"}, {"file": "SECURITY.md", "hash": "17532638134816234240"}, {"file": ".dockerignore", "hash": "7091881303333373662"}, {"file": "nx.json", "hash": "18106012389938599829"}, {"file": "tools/compose/development.yml", "hash": "15216111348143703403"}, {"file": "tools/prisma/migrations/20240505101746_add_statistics_table/migration.sql", "hash": "7884979152348776664"}, {"file": "tools/prisma/migrations/20240507090221_make_last_signed_in_non_null/migration.sql", "hash": "13903247350205626058"}, {"file": "tools/compose/traefik-secure.yml", "hash": "16043189497527784244"}, {"file": "tsconfig.base.json", "hash": "12911521182624281815"}, {"file": ".github/FUNDING.yml", "hash": "15949859120548266321"}, {"file": "jest.config.ts", "hash": "6885938622845352160"}, {"file": "pnpm-lock.yaml", "hash": "8579882106002062367"}, {"file": ".eslintrc.json", "hash": "17066857045496902639"}, {"file": ".npmrc", "hash": "12022249659923024381"}, {"file": ".prettier<PERSON>", "hash": "16883549397108925975"}, {"file": ".prettieri<PERSON>re", "hash": "2815325643299139771"}, {"file": "tools/compose/traefik.yml", "hash": "11081643561023851148"}, {"file": "package.json", "hash": "17990749483405173467"}, {"file": "lingui.config.ts", "hash": "8916976920202346816"}, {"file": "tools/compose/nginx-proxy-manager.yml", "hash": "10098164301497700070"}, {"file": "tools/prisma/migrations/20250112140257_normalize_user_email_fields/migration.sql", "hash": "15569130958319110260"}, {"file": "tools/prisma/migrations/20231121234455_initialize_tables/migration.sql", "hash": "7984150389411230733"}, {"file": "tools/prisma/migrations/20250113145008_add_openid_provider_to_enums/migration.sql", "hash": "4734991519708621677"}, {"file": "tools/tsconfig.tools.json", "hash": "6103084648839996614"}, {"file": ".github/ISSUE_TEMPLATE/feature-request.yml", "hash": "9297977546282393228"}, {"file": ".vscode/settings.json", "hash": "9084328940087864850"}, {"file": "README.md", "hash": "10190031546622020575"}, {"file": "jest.preset.js", "hash": "18369472350730580350"}, {"file": ".es<PERSON><PERSON><PERSON>", "hash": "13404745306227114823"}, {"file": "CONTRIBUTING.md", "hash": "15975182059998067115"}, {"file": "tools/compose/simple.yml", "hash": "7522214389287187174"}, {"file": "tools/prisma/schema.prisma", "hash": "10466974015801469681"}, {"file": ".github/ISSUE_TEMPLATE/bug-report.yml", "hash": "15852441358477689990"}], "projectFileMap": {"artboard": [{"file": "apps/artboard/.eslintrc.json", "hash": "6907599557095751444"}, {"file": "apps/artboard/index.html", "hash": "4480485154100537369"}, {"file": "apps/artboard/postcss.config.js", "hash": "**********373114554"}, {"file": "apps/artboard/project.json", "hash": "18085139102087264210"}, {"file": "apps/artboard/public/favicon.ico", "hash": "14341031277693941069"}, {"file": "apps/artboard/public/favicon.png", "hash": "11547571018533841104"}, {"file": "apps/artboard/public/favicon.svg", "hash": "10014684485528885083"}, {"file": "apps/artboard/public/icon/dark.svg", "hash": "4230213572436627847"}, {"file": "apps/artboard/public/icon/light.svg", "hash": "17751395884775645948"}, {"file": "apps/artboard/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/artboard/src/components/brand-icon.tsx", "hash": "16199894912422617612", "deps": ["npm:react"]}, {"file": "apps/artboard/src/components/page.tsx", "hash": "14392781318766673006", "deps": ["hooks", "utils"]}, {"file": "apps/artboard/src/components/picture.tsx", "hash": "6911698790160751698", "deps": ["utils"]}, {"file": "apps/artboard/src/constants/helmet.ts", "hash": "6319203782807263569", "deps": ["npm:react-helmet-async"]}, {"file": "apps/artboard/src/main.tsx", "hash": "12424327738257983281", "deps": ["npm:react", "npm:react-dom", "npm:react-router"]}, {"file": "apps/artboard/src/pages/artboard.tsx", "hash": "12427414127263324575", "deps": ["npm:react", "npm:react-helmet-async", "npm:react-router", "npm:webfontloader"]}, {"file": "apps/artboard/src/pages/builder.tsx", "hash": "6322255870373224941", "deps": ["schema", "utils", "npm:framer-motion", "npm:react", "npm:react-zoom-pan-pinch"]}, {"file": "apps/artboard/src/pages/preview.tsx", "hash": "9107431786171992077", "deps": ["schema", "utils", "npm:react"]}, {"file": "apps/artboard/src/providers/index.tsx", "hash": "1035104959229765521", "deps": ["npm:react", "npm:react-helmet-async", "npm:react-router"]}, {"file": "apps/artboard/src/router/index.tsx", "hash": "3974416850187309387", "deps": ["npm:react-router"]}, {"file": "apps/artboard/src/store/artboard.ts", "hash": "11295057114613483219", "deps": ["schema", "npm:zu<PERSON>"]}, {"file": "apps/artboard/src/styles/main.css", "hash": "16109051207757082179"}, {"file": "apps/artboard/src/templates/azurill.tsx", "hash": "17663838123966092150", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/templates/bronzor.tsx", "hash": "413438734680844571", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/templates/chikorita.tsx", "hash": "12526313698991918490", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/templates/ditto.tsx", "hash": "14983397996758662664", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/templates/gengar.tsx", "hash": "2400982726806278789", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/templates/glalie.tsx", "hash": "7984030587607112474", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/templates/index.tsx", "hash": "18308351825113715918", "deps": ["utils"]}, {"file": "apps/artboard/src/templates/kakuna.tsx", "hash": "14187690781447731411", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/templates/leafish.tsx", "hash": "13670357625601625093", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/templates/nosepass.tsx", "hash": "12414933959453776703", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/templates/onyx.tsx", "hash": "13440550330433710003", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/templates/pikachu.tsx", "hash": "11782021044550782222", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/templates/rhyhorn.tsx", "hash": "15819026340447505890", "deps": ["schema", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/artboard/src/types/template.ts", "hash": "17478891275715259414", "deps": ["schema"]}, {"file": "apps/artboard/tailwind.config.js", "hash": "7351598186773484346", "deps": ["npm:@nx/react", "npm:@tailwindcss/typography"]}, {"file": "apps/artboard/tsconfig.app.json", "hash": "9014817974750934731"}, {"file": "apps/artboard/tsconfig.json", "hash": "1172371253974053326"}, {"file": "apps/artboard/vite.config.ts", "hash": "15221590367146192278", "deps": ["npm:@nx/vite", "npm:@vitejs/plugin-react-swc", "npm:vite"]}], "server": [{"file": "apps/server/.eslintrc.json", "hash": "5824739060344899063"}, {"file": "apps/server/jest.config.ts", "hash": "6196893383995066148"}, {"file": "apps/server/project.json", "hash": "4704835078952727381"}, {"file": "apps/server/src/app.module.ts", "hash": "5136916588753124614", "deps": ["npm:@nestjs/common", "npm:@nestjs/core", "npm:@nestjs/serve-static", "npm:nest-raven", "npm:nestjs-zod"]}, {"file": "apps/server/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/server/src/auth/auth.controller.ts", "hash": "4189036645243011535", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs/swagger", "dto", "utils", "npm:express"]}, {"file": "apps/server/src/auth/auth.module.ts", "hash": "14767306764537311485", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs/jwt", "npm:@nestjs/passport"]}, {"file": "apps/server/src/auth/auth.service.ts", "hash": "4299254029490354577", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs/jwt", "npm:@prisma/client", "dto", "utils", "npm:bcryptjs", "npm:otplib"]}, {"file": "apps/server/src/auth/guards/github.guard.ts", "hash": "7844719035012424615", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport"]}, {"file": "apps/server/src/auth/guards/google.guard.ts", "hash": "2368318072548243604", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport"]}, {"file": "apps/server/src/auth/guards/jwt.guard.ts", "hash": "129536141443289436", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport"]}, {"file": "apps/server/src/auth/guards/local.guard.ts", "hash": "3581466749257908460", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport"]}, {"file": "apps/server/src/auth/guards/openid.guard.ts", "hash": "4428252709705873980", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport"]}, {"file": "apps/server/src/auth/guards/optional.guard.ts", "hash": "6322800551367061239", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "dto"]}, {"file": "apps/server/src/auth/guards/refresh.guard.ts", "hash": "7472738727354131192", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport"]}, {"file": "apps/server/src/auth/guards/two-factor.guard.ts", "hash": "17639313957327561828", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport"]}, {"file": "apps/server/src/auth/strategy/dummy.strategy.ts", "hash": "921388682905402255", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "npm:passport"]}, {"file": "apps/server/src/auth/strategy/github.strategy.ts", "hash": "5649338502678008822", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "npm:@paralleldrive/cuid2", "npm:@prisma/client", "utils", "npm:passport-github2"]}, {"file": "apps/server/src/auth/strategy/google.strategy.ts", "hash": "8125325695301484403", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "npm:@paralleldrive/cuid2", "npm:@prisma/client", "utils", "npm:passport-google-oauth20"]}, {"file": "apps/server/src/auth/strategy/jwt.strategy.ts", "hash": "3508935599798408024", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs/passport", "npm:express", "npm:passport-jwt"]}, {"file": "apps/server/src/auth/strategy/local.strategy.ts", "hash": "475108539908780468", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "utils", "npm:passport-local"]}, {"file": "apps/server/src/auth/strategy/openid.strategy.ts", "hash": "3974043953843562288", "deps": ["npm:@nestjs/common", "npm:@nestjs/passport", "npm:@prisma/client", "utils", "npm:passport-openidconnect"]}, {"file": "apps/server/src/auth/strategy/refresh.strategy.ts", "hash": "9579945603408230799", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs/passport", "npm:express", "npm:passport-jwt"]}, {"file": "apps/server/src/auth/strategy/two-factor.strategy.ts", "hash": "15509366194278690987", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs/passport", "npm:express", "npm:passport-jwt"]}, {"file": "apps/server/src/auth/utils/cookie.ts", "hash": "8681049792899825638", "deps": ["npm:express"]}, {"file": "apps/server/src/auth/utils/payload.ts", "hash": "11365272957434914867", "deps": ["schema", "npm:zod"]}, {"file": "apps/server/src/config/config.module.ts", "hash": "9661147666966316934", "deps": ["npm:@nestjs/common", "npm:@nestjs/config"]}, {"file": "apps/server/src/config/schema.ts", "hash": "5263475237152116874", "deps": ["npm:zod"]}, {"file": "apps/server/src/contributors/contributors.controller.ts", "hash": "7705491564741974165", "deps": ["npm:@nestjs/common"]}, {"file": "apps/server/src/contributors/contributors.module.ts", "hash": "768419995634538149", "deps": ["npm:@nestjs/axios", "npm:@nestjs/common"]}, {"file": "apps/server/src/contributors/contributors.service.ts", "hash": "12664347603573046730", "deps": ["npm:@nestjs/axios", "npm:@nestjs/common", "npm:@nestjs/config", "dto"]}, {"file": "apps/server/src/database/database.module.ts", "hash": "934891986364568802", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:nestjs-prisma"]}, {"file": "apps/server/src/feature/feature.controller.ts", "hash": "5069639156716818609", "deps": ["npm:@nestjs/common"]}, {"file": "apps/server/src/feature/feature.module.ts", "hash": "1068061838498759951", "deps": ["npm:@nestjs/common"]}, {"file": "apps/server/src/feature/feature.service.ts", "hash": "10742726924842056817", "deps": ["npm:@nestjs/common", "npm:@nestjs/config"]}, {"file": "apps/server/src/health/browser.health.ts", "hash": "1052750254738450135", "deps": ["npm:@nestjs/common", "npm:@nestjs/terminus"]}, {"file": "apps/server/src/health/database.health.ts", "hash": "8854524490599485648", "deps": ["npm:@nestjs/common", "npm:@nestjs/terminus", "npm:nestjs-prisma"]}, {"file": "apps/server/src/health/health.controller.ts", "hash": "11449182559226159001", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "npm:@nestjs/terminus"]}, {"file": "apps/server/src/health/health.module.ts", "hash": "12258782137625594927", "deps": ["npm:@nestjs/common", "npm:@nestjs/terminus"]}, {"file": "apps/server/src/health/storage.health.ts", "hash": "2133064753839433703", "deps": ["npm:@nestjs/common", "npm:@nestjs/terminus"]}, {"file": "apps/server/src/mail/mail.module.ts", "hash": "9074977132734099732", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs-modules/mailer", "npm:nodemailer"]}, {"file": "apps/server/src/mail/mail.service.ts", "hash": "7733482321173225259", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs-modules/mailer"]}, {"file": "apps/server/src/main.ts", "hash": "1420763697303657158", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@nestjs/core", "npm:@nestjs/platform-express", "npm:@nestjs/swagger", "npm:cookie-parser", "npm:express-session", "npm:helmet", "npm:nestjs-zod"]}, {"file": "apps/server/src/printer/printer.module.ts", "hash": "16676379362219971799", "deps": ["npm:@nestjs/axios", "npm:@nestjs/common"]}, {"file": "apps/server/src/printer/printer.service.ts", "hash": "7859310390674415886", "deps": ["npm:@nestjs/axios", "npm:@nestjs/common", "npm:@nestjs/config", "dto", "utils", "npm:async-retry", "npm:pdf-lib", "npm:puppeteer"]}, {"file": "apps/server/src/resume/decorators/resume.decorator.ts", "hash": "2667033496773935491", "deps": ["npm:@nestjs/common", "dto"]}, {"file": "apps/server/src/resume/guards/resume.guard.ts", "hash": "3938351948540725770", "deps": ["npm:@nestjs/common", "dto", "utils", "npm:express"]}, {"file": "apps/server/src/resume/resume.controller.ts", "hash": "9430610496396869139", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "npm:@prisma/client", "dto", "schema", "utils", "npm:zod-to-json-schema"]}, {"file": "apps/server/src/resume/resume.module.ts", "hash": "1963136412513703929", "deps": ["npm:@nestjs/common"]}, {"file": "apps/server/src/resume/resume.service.ts", "hash": "16179064921980745603", "deps": ["npm:@nestjs/common", "npm:@prisma/client", "dto", "schema", "utils", "npm:@sindresorhus/slugify", "npm:deepmerge", "npm:nestjs-prisma"]}, {"file": "apps/server/src/storage/storage.controller.ts", "hash": "16464224911825068408", "deps": ["npm:@nestjs/common", "npm:@nestjs/platform-express", "npm:@nestjs/swagger"]}, {"file": "apps/server/src/storage/storage.module.ts", "hash": "14638954489959402388", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:multer", "npm:nestjs-minio-client"]}, {"file": "apps/server/src/storage/storage.service.ts", "hash": "14675418880332405929", "deps": ["npm:@nestjs/common", "npm:@nestjs/config", "npm:@paralleldrive/cuid2", "npm:@sindresorhus/slugify", "npm:nestjs-minio-client", "npm:sharp"]}, {"file": "apps/server/src/translation/translation.controller.ts", "hash": "234429366770058018", "deps": ["npm:@nestjs/common"]}, {"file": "apps/server/src/translation/translation.module.ts", "hash": "7933345445650765838", "deps": ["npm:@nestjs/axios", "npm:@nestjs/common"]}, {"file": "apps/server/src/translation/translation.service.ts", "hash": "886226234871040976", "deps": ["npm:@nestjs/axios", "npm:@nestjs/common", "npm:@nestjs/config", "utils"]}, {"file": "apps/server/src/types/express.d.ts", "hash": "6726631629786033310", "deps": ["npm:@prisma/client"]}, {"file": "apps/server/src/user/decorators/user.decorator.ts", "hash": "16459426851865527814", "deps": ["npm:@nestjs/common", "dto"]}, {"file": "apps/server/src/user/user.controller.ts", "hash": "1120256781693140493", "deps": ["npm:@nestjs/common", "npm:@nestjs/swagger", "npm:@prisma/client", "dto", "utils", "npm:express"]}, {"file": "apps/server/src/user/user.module.ts", "hash": "2235627294370119044", "deps": ["npm:@nestjs/common"]}, {"file": "apps/server/src/user/user.service.ts", "hash": "10180885999938808186", "deps": ["npm:@nestjs/common", "npm:@prisma/client", "dto", "utils", "npm:nestjs-prisma"]}, {"file": "apps/server/tsconfig.app.json", "hash": "1859940065711976726"}, {"file": "apps/server/tsconfig.json", "hash": "7462419380158041776"}, {"file": "apps/server/tsconfig.spec.json", "hash": "12478527556459153090"}, {"file": "apps/server/webpack.config.js", "hash": "9339712088161700688", "deps": ["npm:@nx/webpack"]}], "utils": [{"file": "libs/utils/.eslintrc.json", "hash": "9156967004283214833"}, {"file": "libs/utils/.swcrc", "hash": "1208819521669582604"}, {"file": "libs/utils/package.json", "hash": "13905763299205501865", "deps": ["npm:@swc/helpers", "npm:clsx", "npm:dayjs", "npm:papa<PERSON><PERSON>", "npm:sanitize-html", "npm:tailwind-merge", "npm:unique-names-generator", "npm:zod"]}, {"file": "libs/utils/pnpm-lock.yaml", "hash": "10252064088375334350"}, {"file": "libs/utils/project.json", "hash": "16868820806796167981"}, {"file": "libs/utils/src/index.ts", "hash": "17902901916558011374"}, {"file": "libs/utils/src/namespaces/array.ts", "hash": "4218306402357554847"}, {"file": "libs/utils/src/namespaces/color.ts", "hash": "8216128079918640570"}, {"file": "libs/utils/src/namespaces/csv.ts", "hash": "7310419118236707298", "deps": ["npm:papa<PERSON><PERSON>"]}, {"file": "libs/utils/src/namespaces/date.ts", "hash": "13844331881829703886", "deps": ["npm:dayjs", "npm:zod"]}, {"file": "libs/utils/src/namespaces/error.ts", "hash": "8322014681603664696"}, {"file": "libs/utils/src/namespaces/fonts.ts", "hash": "14873150891100199074"}, {"file": "libs/utils/src/namespaces/language.ts", "hash": "15881211882881824610"}, {"file": "libs/utils/src/namespaces/number.ts", "hash": "18231005117588372074"}, {"file": "libs/utils/src/namespaces/object.ts", "hash": "4155170841859869234"}, {"file": "libs/utils/src/namespaces/page.ts", "hash": "17806955854038644144"}, {"file": "libs/utils/src/namespaces/promise.ts", "hash": "44930153568130414"}, {"file": "libs/utils/src/namespaces/string.ts", "hash": "8912508165959995384", "deps": ["npm:sanitize-html", "npm:unique-names-generator"]}, {"file": "libs/utils/src/namespaces/style.ts", "hash": "11378301717886338227", "deps": ["npm:clsx", "npm:tailwind-merge"]}, {"file": "libs/utils/src/namespaces/template.ts", "hash": "17158936347739845892"}, {"file": "libs/utils/src/namespaces/tests/array.test.ts", "hash": "12506194506256666205", "deps": ["npm:vitest"]}, {"file": "libs/utils/src/namespaces/tests/date.test.ts", "hash": "11546342181619552386", "deps": ["npm:vitest"]}, {"file": "libs/utils/src/namespaces/tests/number.test.ts", "hash": "8520223221063060099", "deps": ["npm:vitest"]}, {"file": "libs/utils/src/namespaces/tests/object.test.ts", "hash": "3432301101978976044", "deps": ["npm:vitest"]}, {"file": "libs/utils/src/namespaces/tests/string.test.ts", "hash": "3975316899499373607", "deps": ["npm:vitest"]}, {"file": "libs/utils/src/namespaces/types.ts", "hash": "6979706763345872095"}, {"file": "libs/utils/tsconfig.json", "hash": "5811367476459230978"}, {"file": "libs/utils/tsconfig.lib.json", "hash": "17512648156706774563"}, {"file": "libs/utils/tsconfig.spec.json", "hash": "12280283830498555287"}, {"file": "libs/utils/vite.config.ts", "hash": "13819580799612525181", "deps": ["npm:@nx/vite", "npm:vite"]}], "client": [{"file": "apps/client/.eslintrc.json", "hash": "5045919862577717839"}, {"file": "apps/client/index.html", "hash": "7594171899760483901"}, {"file": "apps/client/postcss.config.js", "hash": "4493299088900523348"}, {"file": "apps/client/project.json", "hash": "2481624981887193933"}, {"file": "apps/client/proxy.conf.json", "hash": "2967232776507346686"}, {"file": "apps/client/public/assets/europass.png", "hash": "4677991339838022052"}, {"file": "apps/client/public/backgrounds/patrick-tomasso-Oaqk7qqNh_c-unsplash.jpg", "hash": "11711628661673414553"}, {"file": "apps/client/public/brand-logos/dark/amazon.svg", "hash": "11596267205022637242"}, {"file": "apps/client/public/brand-logos/dark/google.svg", "hash": "16186690917909864299"}, {"file": "apps/client/public/brand-logos/dark/postman.svg", "hash": "17721587472164074097"}, {"file": "apps/client/public/brand-logos/dark/twilio.svg", "hash": "9622610589484303723"}, {"file": "apps/client/public/brand-logos/dark/zalando.svg", "hash": "1692005471237663499"}, {"file": "apps/client/public/brand-logos/light/amazon.svg", "hash": "2400313128738030427"}, {"file": "apps/client/public/brand-logos/light/google.svg", "hash": "17194238013254768901"}, {"file": "apps/client/public/brand-logos/light/postman.svg", "hash": "5364078719090546773"}, {"file": "apps/client/public/brand-logos/light/twilio.svg", "hash": "7288741156371538014"}, {"file": "apps/client/public/brand-logos/light/zalando.svg", "hash": "18208680378674290526"}, {"file": "apps/client/public/favicon.ico", "hash": "12180179618011713793"}, {"file": "apps/client/public/icon/dark.svg", "hash": "1587675384825253477"}, {"file": "apps/client/public/icon/light.svg", "hash": "7780800781747632373"}, {"file": "apps/client/public/logo/dark.svg", "hash": "1222130249852448906"}, {"file": "apps/client/public/logo/light.svg", "hash": "373569968035725182"}, {"file": "apps/client/public/sample-resumes/ditto.jpg", "hash": "8788598441128088897"}, {"file": "apps/client/public/sample-resumes/ditto.pdf", "hash": "9423639834737088588"}, {"file": "apps/client/public/screenshots/builder.jpg", "hash": "12333087289887847947"}, {"file": "apps/client/public/scripts/initialize-theme.js", "hash": "11841300215210120567"}, {"file": "apps/client/public/styles/prism-dark.css", "hash": "3674397853951819014"}, {"file": "apps/client/public/styles/prism-light.css", "hash": "1337610134276663637"}, {"file": "apps/client/public/support-logos/crowdin-dark.svg", "hash": "803684593407007245"}, {"file": "apps/client/public/support-logos/crowdin-light.svg", "hash": "3888544587761874214"}, {"file": "apps/client/public/support-logos/github-sponsors-dark.svg", "hash": "239283627532546842"}, {"file": "apps/client/public/support-logos/github-sponsors-light.svg", "hash": "16561002997918992654"}, {"file": "apps/client/public/support-logos/linkedin.svg", "hash": "8872448266895882985"}, {"file": "apps/client/public/support-logos/open-collective-dark.svg", "hash": "9461177456094182564"}, {"file": "apps/client/public/support-logos/open-collective-light.svg", "hash": "14470073990095757408"}, {"file": "apps/client/public/support-logos/paypal.svg", "hash": "4638514208797353227"}, {"file": "apps/client/public/templates/jpg/azurill.jpg", "hash": "9561795201956308418"}, {"file": "apps/client/public/templates/jpg/bronzor.jpg", "hash": "116705833889945689"}, {"file": "apps/client/public/templates/jpg/chikorita.jpg", "hash": "15759183298635613217"}, {"file": "apps/client/public/templates/jpg/ditto.jpg", "hash": "10728254170766498326"}, {"file": "apps/client/public/templates/jpg/gengar.jpg", "hash": "9286114844000313525"}, {"file": "apps/client/public/templates/jpg/glalie.jpg", "hash": "11444228179906826895"}, {"file": "apps/client/public/templates/jpg/kakuna.jpg", "hash": "4072920315784572111"}, {"file": "apps/client/public/templates/jpg/leafish.jpg", "hash": "18097755615900782586"}, {"file": "apps/client/public/templates/jpg/nosepass.jpg", "hash": "758715878951546158"}, {"file": "apps/client/public/templates/jpg/onyx.jpg", "hash": "13884959573929481360"}, {"file": "apps/client/public/templates/jpg/pikachu.jpg", "hash": "5451143391802010142"}, {"file": "apps/client/public/templates/jpg/rhyhorn.jpg", "hash": "5914873659944771439"}, {"file": "apps/client/public/templates/json/azurill.json", "hash": "12590575501592955973"}, {"file": "apps/client/public/templates/json/bronzor.json", "hash": "17218632653597806299"}, {"file": "apps/client/public/templates/json/chikorita.json", "hash": "16394127848826481940"}, {"file": "apps/client/public/templates/json/ditto.json", "hash": "11153493350623070540"}, {"file": "apps/client/public/templates/json/gengar.json", "hash": "12546414812466056208"}, {"file": "apps/client/public/templates/json/glalie.json", "hash": "9523313327080276011"}, {"file": "apps/client/public/templates/json/kakuna.json", "hash": "15397742730343262752"}, {"file": "apps/client/public/templates/json/leafish.json", "hash": "12670502809527509591"}, {"file": "apps/client/public/templates/json/nosepass.json", "hash": "8677851859320234324"}, {"file": "apps/client/public/templates/json/onyx.json", "hash": "17461457055673273079"}, {"file": "apps/client/public/templates/json/pikachu.json", "hash": "11108495569875662658"}, {"file": "apps/client/public/templates/json/rhyhorn.json", "hash": "10772708306030746583"}, {"file": "apps/client/public/templates/pdf/azurill.pdf", "hash": "12994624866666609847"}, {"file": "apps/client/public/templates/pdf/bronzor.pdf", "hash": "5476924105490872148"}, {"file": "apps/client/public/templates/pdf/chikorita.pdf", "hash": "6927752227034179079"}, {"file": "apps/client/public/templates/pdf/ditto.pdf", "hash": "4082607546600672260"}, {"file": "apps/client/public/templates/pdf/gengar.pdf", "hash": "6644785849125691034"}, {"file": "apps/client/public/templates/pdf/glalie.pdf", "hash": "15352051405864363162"}, {"file": "apps/client/public/templates/pdf/kakuna.pdf", "hash": "4272112704387373949"}, {"file": "apps/client/public/templates/pdf/leafish.pdf", "hash": "9084496103442490599"}, {"file": "apps/client/public/templates/pdf/nosepass.pdf", "hash": "2138599879318970470"}, {"file": "apps/client/public/templates/pdf/onyx.pdf", "hash": "6067268198902932403"}, {"file": "apps/client/public/templates/pdf/pikachu.pdf", "hash": "2695012496188610360"}, {"file": "apps/client/public/templates/pdf/rhyhorn.pdf", "hash": "6711823437493406806"}, {"file": "apps/client/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/client/src/components/ai-actions.tsx", "hash": "9737799675413329411", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils", "npm:react"]}, {"file": "apps/client/src/components/brand-icon.tsx", "hash": "6773160615568868309", "deps": ["npm:react", "npm:usehooks-ts"]}, {"file": "apps/client/src/components/copyright.tsx", "hash": "16231298229853092472", "deps": ["npm:@lingui/macro", "utils"]}, {"file": "apps/client/src/components/icon.tsx", "hash": "15811989268513049632", "deps": ["hooks", "utils"]}, {"file": "apps/client/src/components/locale-combobox.tsx", "hash": "2142204326315059347", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils", "npm:fuzzy", "npm:react"]}, {"file": "apps/client/src/components/locale-switch.tsx", "hash": "17770560230304861802", "deps": ["npm:@lingui/react", "npm:@phosphor-icons/react", "ui", "npm:react"]}, {"file": "apps/client/src/components/logo.tsx", "hash": "14059050954146446008", "deps": ["hooks", "utils"]}, {"file": "apps/client/src/components/theme-switch.tsx", "hash": "9106974358828231747", "deps": ["npm:@phosphor-icons/react", "hooks", "ui", "npm:framer-motion", "npm:react"]}, {"file": "apps/client/src/components/user-avatar.tsx", "hash": "15758972250890593719", "deps": ["utils"]}, {"file": "apps/client/src/components/user-options.tsx", "hash": "11000053161474773530", "deps": ["npm:@lingui/macro", "ui", "npm:react-router"]}, {"file": "apps/client/src/constants/colors.ts", "hash": "9242102306442209444"}, {"file": "apps/client/src/constants/helmet.ts", "hash": "6319203782807263569", "deps": ["npm:react-helmet-async"]}, {"file": "apps/client/src/constants/llm.ts", "hash": "1681980387613464628"}, {"file": "apps/client/src/constants/parallax-tilt.ts", "hash": "17532881606379868949", "deps": ["npm:react-parallax-tilt"]}, {"file": "apps/client/src/constants/query-keys.ts", "hash": "13058560706132693269", "deps": ["npm:@tanstack/react-query"]}, {"file": "apps/client/src/hooks/use-toast.ts", "hash": "8642328419826330154", "deps": ["npm:@paralleldrive/cuid2", "ui", "npm:react"]}, {"file": "apps/client/src/libs/axios.ts", "hash": "7114994254075971266", "deps": ["npm:@lingui/macro", "utils", "npm:axios", "npm:axios-auth-refresh", "npm:react-router"]}, {"file": "apps/client/src/libs/dayjs.ts", "hash": "17252283105990880931", "deps": ["npm:dayjs", ["npm:dayjs", "dynamic"]]}, {"file": "apps/client/src/libs/lingui.ts", "hash": "10656134006965620277", "deps": ["npm:@lingui/core", "npm:dayjs"]}, {"file": "apps/client/src/libs/query-client.ts", "hash": "15724402779926169660", "deps": ["npm:@tanstack/react-query"]}, {"file": "apps/client/src/locales/af-ZA/messages.po", "hash": "206527354809936124"}, {"file": "apps/client/src/locales/am-ET/messages.po", "hash": "12703806909998791500"}, {"file": "apps/client/src/locales/ar-SA/messages.po", "hash": "12247726259937395520"}, {"file": "apps/client/src/locales/bg-BG/messages.po", "hash": "10608907817309755585"}, {"file": "apps/client/src/locales/bn-BD/messages.po", "hash": "2686464538584202147"}, {"file": "apps/client/src/locales/ca-ES/messages.po", "hash": "13372433433316571635"}, {"file": "apps/client/src/locales/cs-CZ/messages.po", "hash": "221081002381575063"}, {"file": "apps/client/src/locales/da-DK/messages.po", "hash": "5354975877741928889"}, {"file": "apps/client/src/locales/de-DE/messages.po", "hash": "6008578582045633054"}, {"file": "apps/client/src/locales/el-GR/messages.po", "hash": "9092694284135625102"}, {"file": "apps/client/src/locales/en-US/messages.po", "hash": "12337536495060045967"}, {"file": "apps/client/src/locales/es-ES/messages.po", "hash": "11498000948203079308"}, {"file": "apps/client/src/locales/fa-IR/messages.po", "hash": "7912452346961425017"}, {"file": "apps/client/src/locales/fi-FI/messages.po", "hash": "5856412781791115128"}, {"file": "apps/client/src/locales/fr-FR/messages.po", "hash": "171440838746917372"}, {"file": "apps/client/src/locales/he-IL/messages.po", "hash": "18417002571969457628"}, {"file": "apps/client/src/locales/hi-IN/messages.po", "hash": "14184009762264891460"}, {"file": "apps/client/src/locales/hu-HU/messages.po", "hash": "3833010011175450616"}, {"file": "apps/client/src/locales/id-ID/messages.po", "hash": "12014300276877767779"}, {"file": "apps/client/src/locales/it-IT/messages.po", "hash": "660729835313099950"}, {"file": "apps/client/src/locales/ja-JP/messages.po", "hash": "3713654218546156147"}, {"file": "apps/client/src/locales/km-KH/messages.po", "hash": "10653402939294173210"}, {"file": "apps/client/src/locales/kn-IN/messages.po", "hash": "16609528350456181753"}, {"file": "apps/client/src/locales/ko-KR/messages.po", "hash": "7694387668838350237"}, {"file": "apps/client/src/locales/lt-LT/messages.po", "hash": "14334781784236062057"}, {"file": "apps/client/src/locales/lv-LV/messages.po", "hash": "12356713232975078596"}, {"file": "apps/client/src/locales/ml-IN/messages.po", "hash": "17003832198016840959"}, {"file": "apps/client/src/locales/mr-IN/messages.po", "hash": "928186598264031821"}, {"file": "apps/client/src/locales/ms-MY/messages.po", "hash": "420188232543113306"}, {"file": "apps/client/src/locales/ne-NP/messages.po", "hash": "18240803221949599898"}, {"file": "apps/client/src/locales/nl-NL/messages.po", "hash": "4319270385347879793"}, {"file": "apps/client/src/locales/no-NO/messages.po", "hash": "9690315259460340943"}, {"file": "apps/client/src/locales/or-IN/messages.po", "hash": "16293304325755355658"}, {"file": "apps/client/src/locales/pl-PL/messages.po", "hash": "1207672504099319780"}, {"file": "apps/client/src/locales/pt-BR/messages.po", "hash": "15109642103099073168"}, {"file": "apps/client/src/locales/pt-PT/messages.po", "hash": "6374812637130463898"}, {"file": "apps/client/src/locales/ro-RO/messages.po", "hash": "17759843110467037885"}, {"file": "apps/client/src/locales/ru-RU/messages.po", "hash": "726769940691510795"}, {"file": "apps/client/src/locales/sk-SK/messages.po", "hash": "9309860480424911303"}, {"file": "apps/client/src/locales/sq-AL/messages.po", "hash": "13194848955300954739"}, {"file": "apps/client/src/locales/sr-SP/messages.po", "hash": "1586450013119275946"}, {"file": "apps/client/src/locales/sv-SE/messages.po", "hash": "18345545801105114819"}, {"file": "apps/client/src/locales/ta-IN/messages.po", "hash": "7201232778751460094"}, {"file": "apps/client/src/locales/te-IN/messages.po", "hash": "4795474839180588874"}, {"file": "apps/client/src/locales/th-TH/messages.po", "hash": "12366855901624080784"}, {"file": "apps/client/src/locales/tr-TR/messages.po", "hash": "7090634499127476749"}, {"file": "apps/client/src/locales/uk-UA/messages.po", "hash": "8280184084156310375"}, {"file": "apps/client/src/locales/uz-UZ/messages.po", "hash": "17818290495199126924"}, {"file": "apps/client/src/locales/vi-VN/messages.po", "hash": "7631375827802030217"}, {"file": "apps/client/src/locales/zh-CN/messages.po", "hash": "12315355841317476773"}, {"file": "apps/client/src/locales/zh-TW/messages.po", "hash": "15340667791389252079"}, {"file": "apps/client/src/main.tsx", "hash": "12424327738257983281", "deps": ["npm:react", "npm:react-dom", "npm:react-router"]}, {"file": "apps/client/src/pages/auth/_components/social-auth.tsx", "hash": "15369640116034755160", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui"]}, {"file": "apps/client/src/pages/auth/backup-otp/page.tsx", "hash": "2891829743561171753", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "dto", "hooks", "ui", "npm:react", "npm:react-helmet-async", "npm:react-hook-form", "npm:react-router", "npm:zod"]}, {"file": "apps/client/src/pages/auth/forgot-password/page.tsx", "hash": "17498515155833277250", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "dto", "ui", "npm:react", "npm:react-helmet-async", "npm:react-hook-form", "npm:react-router", "npm:zod"]}, {"file": "apps/client/src/pages/auth/layout.tsx", "hash": "13920217796165988183", "deps": ["npm:@lingui/macro", "utils", "npm:react", "npm:react-router"]}, {"file": "apps/client/src/pages/auth/login/page.tsx", "hash": "8872837030248415363", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "dto", "hooks", "ui", "utils", "npm:react", "npm:react-helmet-async", "npm:react-hook-form", "npm:react-router", "npm:zod"]}, {"file": "apps/client/src/pages/auth/register/page.tsx", "hash": "17416986463693684744", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "dto", "hooks", "ui", "utils", "npm:react", "npm:react-helmet-async", "npm:react-hook-form", "npm:react-router", "npm:zod"]}, {"file": "apps/client/src/pages/auth/reset-password/page.tsx", "hash": "12084400107417863950", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "dto", "hooks", "ui", "npm:react", "npm:react-helmet-async", "npm:react-hook-form", "npm:react-router", "npm:zod"]}, {"file": "apps/client/src/pages/auth/verify-email/page.tsx", "hash": "3644883511854893418", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "npm:react", "npm:react-helmet-async", "npm:react-router"]}, {"file": "apps/client/src/pages/auth/verify-otp/page.tsx", "hash": "9474671547226156452", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "dto", "hooks", "ui", "npm:react", "npm:react-helmet-async", "npm:react-hook-form", "npm:react-router", "npm:zod"]}, {"file": "apps/client/src/pages/builder/_components/header.tsx", "hash": "14949329524740630053", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils", "npm:react-router"]}, {"file": "apps/client/src/pages/builder/_components/toolbar.tsx", "hash": "11421150660077664529", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "npm:framer-motion", "npm:react"]}, {"file": "apps/client/src/pages/builder/layout.tsx", "hash": "15982653541619658133", "deps": ["hooks", "ui", "utils", "npm:react-router"]}, {"file": "apps/client/src/pages/builder/page.tsx", "hash": "7741865075256630403", "deps": ["npm:@lingui/macro", "dto", "npm:react", "npm:react-helmet-async", "npm:react-router"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/awards.tsx", "hash": "4449207752907400155", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "schema", "ui", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/certifications.tsx", "hash": "9310009685789782038", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "schema", "ui", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/custom-section.tsx", "hash": "14854675610816419634", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "schema", "ui", "npm:framer-motion", "npm:react", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/education.tsx", "hash": "334849558975298849", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "schema", "ui", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/experience.tsx", "hash": "13720338705350789693", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "schema", "ui", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/interests.tsx", "hash": "4009019557807137470", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "schema", "ui", "npm:framer-motion", "npm:react", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/languages.tsx", "hash": "1320866804164383480", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "schema", "ui", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/profiles.tsx", "hash": "11458371678267497621", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "schema", "ui", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/projects.tsx", "hash": "7898706697467166830", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "schema", "ui", "npm:framer-motion", "npm:react", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/publications.tsx", "hash": "1133845909218783869", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "schema", "ui", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/references.tsx", "hash": "1772492649106448874", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "schema", "ui", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/skills.tsx", "hash": "6039109854318286809", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "schema", "ui", "npm:framer-motion", "npm:react", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/dialogs/volunteer.tsx", "hash": "16126713564324266798", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "schema", "ui", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/index.tsx", "hash": "6078659511642487692", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "schema", "ui", "npm:react", "npm:react-router"]}, {"file": "apps/client/src/pages/builder/sidebars/left/sections/basics.tsx", "hash": "12103945182221160883", "deps": ["npm:@lingui/macro", "schema", "ui"]}, {"file": "apps/client/src/pages/builder/sidebars/left/sections/custom/section.tsx", "hash": "12875431369732270497", "deps": ["npm:@lingui/macro", "npm:@paralleldrive/cuid2", "npm:@phosphor-icons/react", "schema", "ui", "utils", "npm:framer-motion"]}, {"file": "apps/client/src/pages/builder/sidebars/left/sections/picture/options.tsx", "hash": "10679684960207993159", "deps": ["npm:@lingui/macro", "ui", "npm:react"]}, {"file": "apps/client/src/pages/builder/sidebars/left/sections/picture/section.tsx", "hash": "16442136403638320334", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils", "npm:framer-motion", "npm:react", "npm:zod"]}, {"file": "apps/client/src/pages/builder/sidebars/left/sections/shared/section-base.tsx", "hash": "13901754975664023204", "deps": ["npm:@dnd-kit/core", "npm:@dnd-kit/modifiers", "npm:@dnd-kit/sortable", "npm:@lingui/macro", "npm:@phosphor-icons/react", "schema", "ui", "utils", "npm:framer-motion", "npm:lodash.get"]}, {"file": "apps/client/src/pages/builder/sidebars/left/sections/shared/section-dialog.tsx", "hash": "6891770562440082674", "deps": ["npm:@lingui/macro", "npm:@paralleldrive/cuid2", "npm:@phosphor-icons/react", "npm:@radix-ui/react-visually-hidden", "schema", "ui", "npm:immer", "npm:lodash.get", "npm:react", "npm:react-hook-form"]}, {"file": "apps/client/src/pages/builder/sidebars/left/sections/shared/section-icon.tsx", "hash": "18378841283452840550", "deps": ["npm:@phosphor-icons/react", "schema", "ui", "npm:lodash.get"]}, {"file": "apps/client/src/pages/builder/sidebars/left/sections/shared/section-list-item.tsx", "hash": "12098987910951790337", "deps": ["npm:@dnd-kit/sortable", "npm:@dnd-kit/utilities", "npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils", "npm:framer-motion"]}, {"file": "apps/client/src/pages/builder/sidebars/left/sections/shared/section-options.tsx", "hash": "123750469158385660", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "schema", "ui", "npm:lodash.get", "npm:react"]}, {"file": "apps/client/src/pages/builder/sidebars/left/sections/shared/url-input.tsx", "hash": "14538288964472667962", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "schema", "ui", "npm:react"]}, {"file": "apps/client/src/pages/builder/sidebars/left/sections/summary.tsx", "hash": "16969071873254651527", "deps": ["schema", "ui", "utils"]}, {"file": "apps/client/src/pages/builder/sidebars/right/index.tsx", "hash": "13848234268174018947", "deps": ["npm:@lingui/macro", "ui", "npm:react"]}, {"file": "apps/client/src/pages/builder/sidebars/right/sections/css.tsx", "hash": "7594672155143751816", "deps": ["npm:@lingui/macro", "hooks", "ui", "npm:prismjs", "npm:react-helmet-async", "npm:react-simple-code-editor"]}, {"file": "apps/client/src/pages/builder/sidebars/right/sections/export.tsx", "hash": "8505619259983281893", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils", "npm:file-saver"]}, {"file": "apps/client/src/pages/builder/sidebars/right/sections/information.tsx", "hash": "6661329884548051275", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils"]}, {"file": "apps/client/src/pages/builder/sidebars/right/sections/layout.tsx", "hash": "2094562185659496284", "deps": ["npm:@dnd-kit/core", "npm:@dnd-kit/sortable", "npm:@dnd-kit/utilities", "npm:@lingui/macro", "npm:@phosphor-icons/react", "schema", "ui", "utils", "npm:lodash.get", "npm:react"]}, {"file": "apps/client/src/pages/builder/sidebars/right/sections/notes.tsx", "hash": "11406202830629416380", "deps": ["npm:@lingui/macro", "ui"]}, {"file": "apps/client/src/pages/builder/sidebars/right/sections/page.tsx", "hash": "14108288844902678034", "deps": ["npm:@lingui/macro", "ui"]}, {"file": "apps/client/src/pages/builder/sidebars/right/sections/sharing.tsx", "hash": "273798097946265519", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "npm:framer-motion"]}, {"file": "apps/client/src/pages/builder/sidebars/right/sections/statistics.tsx", "hash": "17646114582079832959", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils", "npm:framer-motion"]}, {"file": "apps/client/src/pages/builder/sidebars/right/sections/template.tsx", "hash": "12992084110346651795", "deps": ["npm:@lingui/macro", "ui", "utils", "npm:framer-motion"]}, {"file": "apps/client/src/pages/builder/sidebars/right/sections/theme.tsx", "hash": "6996851201986021745", "deps": ["npm:@lingui/macro", "ui", "utils", "npm:react-colorful"]}, {"file": "apps/client/src/pages/builder/sidebars/right/sections/typography.tsx", "hash": "15997419297274325390", "deps": ["npm:@lingui/macro", "ui", "utils", "npm:react", "npm:webfontloader"]}, {"file": "apps/client/src/pages/builder/sidebars/right/shared/section-icon.tsx", "hash": "7169778055369597197", "deps": ["npm:@phosphor-icons/react", "ui"]}, {"file": "apps/client/src/pages/dashboard/_components/sidebar.tsx", "hash": "1547058362349776076", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils", "npm:framer-motion", "npm:react-router", "npm:use-keyboard-shortcut"]}, {"file": "apps/client/src/pages/dashboard/layout.tsx", "hash": "892090231569201759", "deps": ["npm:@phosphor-icons/react", "ui", "npm:framer-motion", "npm:react", "npm:react-router"]}, {"file": "apps/client/src/pages/dashboard/resumes/_dialogs/import.tsx", "hash": "13688830869903884668", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "parser", "schema", "ui", "npm:framer-motion", "npm:react", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/dashboard/resumes/_dialogs/lock.tsx", "hash": "11810616070533646727", "deps": ["npm:@lingui/macro", "dto", "ui"]}, {"file": "apps/client/src/pages/dashboard/resumes/_dialogs/resume.tsx", "hash": "18153052187471594965", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "dto", "schema", "ui", "utils", "npm:@sindresorhus/slugify", "npm:react", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/base-card.tsx", "hash": "10356976403969605225", "deps": ["ui", "utils", "npm:react", "npm:react-parallax-tilt"]}, {"file": "apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/create-card.tsx", "hash": "2751293712528620564", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils"]}, {"file": "apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/import-card.tsx", "hash": "*******************", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils"]}, {"file": "apps/client/src/pages/dashboard/resumes/_layouts/grid/_components/resume-card.tsx", "hash": "10568078853471772432", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "dto", "ui", "utils", "npm:dayjs", "npm:framer-motion", "npm:react-router"]}, {"file": "apps/client/src/pages/dashboard/resumes/_layouts/grid/index.tsx", "hash": "16004596509717127340", "deps": ["utils", "npm:framer-motion"]}, {"file": "apps/client/src/pages/dashboard/resumes/_layouts/list/_components/base-item.tsx", "hash": "9495336043270019487", "deps": ["utils"]}, {"file": "apps/client/src/pages/dashboard/resumes/_layouts/list/_components/create-item.tsx", "hash": "15252838401228347527", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "dto", "ui"]}, {"file": "apps/client/src/pages/dashboard/resumes/_layouts/list/_components/import-item.tsx", "hash": "*******************", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui"]}, {"file": "apps/client/src/pages/dashboard/resumes/_layouts/list/_components/resume-item.tsx", "hash": "11638976582126995358", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "dto", "ui", "npm:dayjs", "npm:react-router"]}, {"file": "apps/client/src/pages/dashboard/resumes/_layouts/list/index.tsx", "hash": "638984999012565295", "deps": ["utils", "npm:framer-motion"]}, {"file": "apps/client/src/pages/dashboard/resumes/page.tsx", "hash": "2571052883456616251", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "npm:framer-motion", "npm:react", "npm:react-helmet-async"]}, {"file": "apps/client/src/pages/dashboard/settings/_dialogs/two-factor.tsx", "hash": "2417170128683361822", "deps": ["npm:@hookform/resolvers", "npm:@lingui/core", "npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "npm:qrcode.react", "npm:react", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/dashboard/settings/_sections/account.tsx", "hash": "6175008894538062302", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "dto", "ui", "utils", "npm:framer-motion", "npm:react", "npm:react-hook-form"]}, {"file": "apps/client/src/pages/dashboard/settings/_sections/danger.tsx", "hash": "9279849335577863751", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "ui", "npm:react-hook-form", "npm:react-router", "npm:usehooks-ts", "npm:zod"]}, {"file": "apps/client/src/pages/dashboard/settings/_sections/openai.tsx", "hash": "2405271297151017672", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/dashboard/settings/_sections/profile.tsx", "hash": "11526530548328902211", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "hooks", "ui", "utils", "npm:react", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/dashboard/settings/_sections/security.tsx", "hash": "9922450218288777633", "deps": ["npm:@hookform/resolvers", "npm:@lingui/macro", "ui", "npm:framer-motion", "npm:react-hook-form", "npm:zod"]}, {"file": "apps/client/src/pages/dashboard/settings/page.tsx", "hash": "18318204607028859581", "deps": ["npm:@lingui/macro", "ui", "npm:framer-motion", "npm:react-helmet-async"]}, {"file": "apps/client/src/pages/home/<USER>/donation-banner.tsx", "hash": "9250915722224227035", "deps": ["npm:@phosphor-icons/react", "npm:framer-motion"]}, {"file": "apps/client/src/pages/home/<USER>/footer.tsx", "hash": "3282354407116410661", "deps": ["npm:@lingui/macro", "ui", "npm:react-router"]}, {"file": "apps/client/src/pages/home/<USER>/header.tsx", "hash": "12390730422678116268", "deps": ["npm:framer-motion", "npm:react-router"]}, {"file": "apps/client/src/pages/home/<USER>", "hash": "11547064154383484829", "deps": ["ui", "npm:react-router"]}, {"file": "apps/client/src/pages/home/<USER>", "hash": "15645423743665744416", "deps": ["npm:@lingui/macro", "npm:@lingui/react", "npm:react-helmet-async"]}, {"file": "apps/client/src/pages/home/<USER>/contributors/index.tsx", "hash": "3545936772297729044", "deps": ["npm:@lingui/macro", "ui", "utils", "npm:framer-motion", "npm:react"]}, {"file": "apps/client/src/pages/home/<USER>/faq/index.tsx", "hash": "18316811091318402300", "deps": ["ui", "utils"]}, {"file": "apps/client/src/pages/home/<USER>/features/index.tsx", "hash": "9559959373830276184", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "utils", "npm:framer-motion"]}, {"file": "apps/client/src/pages/home/<USER>/hero/call-to-action.tsx", "hash": "12606781107328172004", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "npm:react-router"]}, {"file": "apps/client/src/pages/home/<USER>/hero/decoration.tsx", "hash": "13544595778172323942"}, {"file": "apps/client/src/pages/home/<USER>/hero/index.tsx", "hash": "18094737981393004033", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "ui", "utils", "npm:framer-motion", "npm:react-parallax-tilt"]}, {"file": "apps/client/src/pages/home/<USER>/logo-cloud/index.tsx", "hash": "53437530881633711", "deps": ["npm:@lingui/macro", "ui", "utils"]}, {"file": "apps/client/src/pages/home/<USER>/statistics/counter.tsx", "hash": "6242676353089620128", "deps": ["npm:framer-motion", "npm:react"]}, {"file": "apps/client/src/pages/home/<USER>/statistics/index.tsx", "hash": "15707857807017480955", "deps": ["npm:@lingui/macro"]}, {"file": "apps/client/src/pages/home/<USER>/support/index.tsx", "hash": "12032468831075197258", "deps": ["npm:@lingui/macro"]}, {"file": "apps/client/src/pages/home/<USER>/templates/index.tsx", "hash": "11707481706773100508", "deps": ["npm:@lingui/macro", "utils", "npm:framer-motion"]}, {"file": "apps/client/src/pages/home/<USER>/testimonials/index.tsx", "hash": "13144519273220455505", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "utils", "npm:framer-motion"]}, {"file": "apps/client/src/pages/public/error.tsx", "hash": "3802319297029200704", "deps": ["npm:@lingui/macro", "ui", "npm:react-router"]}, {"file": "apps/client/src/pages/public/page.tsx", "hash": "1418814870735177136", "deps": ["npm:@lingui/macro", "npm:@phosphor-icons/react", "dto", "ui", "utils", "npm:react", "npm:react-helmet-async", "npm:react-router"]}, {"file": "apps/client/src/providers/auth-refresh.tsx", "hash": "12643051817137780528", "deps": ["npm:react"]}, {"file": "apps/client/src/providers/dialog.tsx", "hash": "743001830639837609"}, {"file": "apps/client/src/providers/index.tsx", "hash": "10083465481805384851", "deps": ["ui", "npm:@tanstack/react-query", "npm:react-helmet-async", "npm:react-router"]}, {"file": "apps/client/src/providers/locale.tsx", "hash": "9065162962690043008", "deps": ["npm:@lingui/core", "npm:@lingui/detect-locale", "npm:@lingui/react", "utils", "npm:react"]}, {"file": "apps/client/src/providers/theme.tsx", "hash": "1074903264545158259", "deps": ["hooks", "npm:react"]}, {"file": "apps/client/src/providers/toaster.tsx", "hash": "12087815291706603703", "deps": ["ui"]}, {"file": "apps/client/src/router/guards/auth.tsx", "hash": "11857254850644424689", "deps": ["npm:react-router"]}, {"file": "apps/client/src/router/guards/guest.tsx", "hash": "8135999193714858812", "deps": ["npm:react-router"]}, {"file": "apps/client/src/router/index.tsx", "hash": "7645104887004865939", "deps": ["npm:react-router"]}, {"file": "apps/client/src/router/loaders/auth.ts", "hash": "12660930371171754962", "deps": ["dto", "npm:react-router"]}, {"file": "apps/client/src/services/auth/email-verification/resend-verify-email.ts", "hash": "8063568945684821748", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/auth/email-verification/verify-email.ts", "hash": "2021732227396891233", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/auth/index.ts", "hash": "6830680286338782064"}, {"file": "apps/client/src/services/auth/login.ts", "hash": "2693340991245970796", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios", "npm:react-router"]}, {"file": "apps/client/src/services/auth/logout.ts", "hash": "9024825142871345556", "deps": ["npm:@tanstack/react-query"]}, {"file": "apps/client/src/services/auth/password-recovery/forgot-password.ts", "hash": "5437499254604957814", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/auth/password-recovery/reset-password.ts", "hash": "12454417533025436159", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/auth/providers.ts", "hash": "14316732740225518337", "deps": ["dto", "npm:@tanstack/react-query"]}, {"file": "apps/client/src/services/auth/refresh.ts", "hash": "94099879776315962", "deps": ["dto", "npm:axios"]}, {"file": "apps/client/src/services/auth/register.ts", "hash": "11622123309989783474", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/auth/two-factor-authentication/backup-otp.tsx", "hash": "13622783962570256673", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/auth/two-factor-authentication/disable.ts", "hash": "1667022959187818550", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/auth/two-factor-authentication/enable.ts", "hash": "12184100920042594920", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/auth/two-factor-authentication/setup.ts", "hash": "16112441605573671349", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/auth/two-factor-authentication/verify-otp.tsx", "hash": "10374374912443304173", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/auth/update-password.ts", "hash": "13927989792826349660", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/errors/translate-error.ts", "hash": "17020756372293252410", "deps": ["npm:@lingui/macro", "utils"]}, {"file": "apps/client/src/services/feature/flags.ts", "hash": "12599091119590741965", "deps": ["dto", "npm:@tanstack/react-query"]}, {"file": "apps/client/src/services/feature/index.ts", "hash": "16856707372954911680"}, {"file": "apps/client/src/services/openai/change-tone.ts", "hash": "3727739062717375743", "deps": ["npm:@lingui/macro"]}, {"file": "apps/client/src/services/openai/client.ts", "hash": "8835482551939936715", "deps": ["npm:@lingui/macro", "npm:openai"]}, {"file": "apps/client/src/services/openai/fix-grammar.ts", "hash": "4055669374181046760", "deps": ["npm:@lingui/macro"]}, {"file": "apps/client/src/services/openai/improve-writing.ts", "hash": "8888680663619069009", "deps": ["npm:@lingui/macro"]}, {"file": "apps/client/src/services/resume/contributors.ts", "hash": "18273778663150677187", "deps": ["dto", "npm:@tanstack/react-query"]}, {"file": "apps/client/src/services/resume/create.ts", "hash": "14419514400504298673", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/resume/delete.ts", "hash": "1008244398761661403", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/resume/import.ts", "hash": "15022529753406755912", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/resume/index.ts", "hash": "12531166285108447495"}, {"file": "apps/client/src/services/resume/lock.ts", "hash": "9978737886228364126", "deps": ["dto", "npm:@tanstack/react-query"]}, {"file": "apps/client/src/services/resume/preview.ts", "hash": "2135045089933712609", "deps": ["dto", "npm:@tanstack/react-query"]}, {"file": "apps/client/src/services/resume/print.tsx", "hash": "14166809646359024215", "deps": ["npm:@lingui/macro", "dto", "npm:@tanstack/react-query"]}, {"file": "apps/client/src/services/resume/resume.ts", "hash": "17924633213514459618", "deps": ["dto"]}, {"file": "apps/client/src/services/resume/resumes.ts", "hash": "12573701990576228735", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/resume/statistics.ts", "hash": "1860283656629321572", "deps": ["dto", "npm:@tanstack/react-query"]}, {"file": "apps/client/src/services/resume/translation.ts", "hash": "1322122890795595460", "deps": ["utils", "npm:@tanstack/react-query"]}, {"file": "apps/client/src/services/resume/update.tsx", "hash": "803967267990226357", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios", "npm:lodash.debounce"]}, {"file": "apps/client/src/services/storage/index.ts", "hash": "15679925118146169129"}, {"file": "apps/client/src/services/storage/upload-image.ts", "hash": "11351431385449997513", "deps": ["npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/user/delete-user.ts", "hash": "8655710546463016048", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/user/index.ts", "hash": "9661641980368762850"}, {"file": "apps/client/src/services/user/update-user.ts", "hash": "5491551226901590731", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios"]}, {"file": "apps/client/src/services/user/user.ts", "hash": "8357953399557909808", "deps": ["dto", "npm:@tanstack/react-query", "npm:axios", "npm:react"]}, {"file": "apps/client/src/stores/auth.ts", "hash": "7463184832071279765", "deps": ["dto", "npm:zu<PERSON>"]}, {"file": "apps/client/src/stores/builder.ts", "hash": "17502805865441541665", "deps": ["npm:zu<PERSON>"]}, {"file": "apps/client/src/stores/dialog.ts", "hash": "12144475857555762439", "deps": ["schema", "npm:zu<PERSON>"]}, {"file": "apps/client/src/stores/openai.ts", "hash": "4835462717495627145", "deps": ["npm:zu<PERSON>"]}, {"file": "apps/client/src/stores/resume.ts", "hash": "10582672875534869894", "deps": ["npm:@lingui/macro", "npm:@paralleldrive/cuid2", "dto", "schema", "utils", "npm:lodash.set", "npm:zundo", "npm:zu<PERSON>"]}, {"file": "apps/client/src/styles/_colors.css", "hash": "6651135543638558920"}, {"file": "apps/client/src/styles/_fonts.css", "hash": "15054174361816990104"}, {"file": "apps/client/src/styles/_tailwind.css", "hash": "4274920580434231623"}, {"file": "apps/client/src/styles/_theme.css", "hash": "14793057920523016141"}, {"file": "apps/client/src/styles/main.css", "hash": "9200297481917980404"}, {"file": "apps/client/src/vite-env.d.ts", "hash": "12538739807416445744"}, {"file": "apps/client/tailwind.config.js", "hash": "13364404085132207990", "deps": ["npm:@nx/react"]}, {"file": "apps/client/tsconfig.app.json", "hash": "9014817974750934731"}, {"file": "apps/client/tsconfig.json", "hash": "8792423286157740794"}, {"file": "apps/client/tsconfig.spec.json", "hash": "17670606036390895781"}, {"file": "apps/client/vite.config.ts", "hash": "11385809952841858847", "deps": ["npm:@lingui/vite-plugin", "npm:@nx/vite", "npm:@vitejs/plugin-react", "npm:vite"]}], "schema": [{"file": "libs/schema/.eslintrc.json", "hash": "9156967004283214833"}, {"file": "libs/schema/.swcrc", "hash": "1208819521669582604"}, {"file": "libs/schema/package.json", "hash": "5122964430980608117", "deps": ["npm:@paralleldrive/cuid2", "npm:@swc/helpers", "npm:zod"]}, {"file": "libs/schema/pnpm-lock.yaml", "hash": "12280755585349698449"}, {"file": "libs/schema/project.json", "hash": "991357460404620181"}, {"file": "libs/schema/src/basics/custom.ts", "hash": "4080689263758949387", "deps": ["npm:zod"]}, {"file": "libs/schema/src/basics/index.ts", "hash": "7923462661923567216", "deps": ["npm:zod"]}, {"file": "libs/schema/src/index.ts", "hash": "4411065442403875289", "deps": ["npm:zod"]}, {"file": "libs/schema/src/metadata/index.ts", "hash": "2203593905167016215", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sample.ts", "hash": "927747342734290379"}, {"file": "libs/schema/src/sections/award.ts", "hash": "17850881603352574248", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/certification.ts", "hash": "9635281843097787162", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/custom-section.ts", "hash": "5677396886418251957", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/education.ts", "hash": "4068745528254143279", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/experience.ts", "hash": "11311706407964064477", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/index.ts", "hash": "5778392703458471420", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/interest.ts", "hash": "8681259980692931394", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/language.ts", "hash": "12461232642193175692", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/profile.ts", "hash": "2226765328253029457", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/project.ts", "hash": "10422538036207262585", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/publication.ts", "hash": "858009015074616150", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/reference.ts", "hash": "12717911457170622714", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/skill.ts", "hash": "9514192933162291282", "deps": ["npm:zod"]}, {"file": "libs/schema/src/sections/volunteer.ts", "hash": "6181835726695532037", "deps": ["npm:zod"]}, {"file": "libs/schema/src/shared/id.ts", "hash": "4105862171369590290", "deps": ["npm:@paralleldrive/cuid2", "npm:zod"]}, {"file": "libs/schema/src/shared/index.ts", "hash": "13021272053825383873"}, {"file": "libs/schema/src/shared/item.ts", "hash": "2170305012371213153", "deps": ["npm:zod"]}, {"file": "libs/schema/src/shared/types.ts", "hash": "871669965107948339"}, {"file": "libs/schema/src/shared/url.ts", "hash": "18346770732724297688", "deps": ["npm:zod"]}, {"file": "libs/schema/tsconfig.json", "hash": "10294729341131714718"}, {"file": "libs/schema/tsconfig.lib.json", "hash": "17512648156706774563"}, {"file": "libs/schema/tsconfig.spec.json", "hash": "12280283830498555287"}, {"file": "libs/schema/vite.config.ts", "hash": "12627453699148834622", "deps": ["npm:@nx/vite", "npm:vite"]}], "ui": [{"file": "libs/ui/.eslintrc.json", "hash": "9018106610306350043"}, {"file": "libs/ui/package.json", "hash": "2840741859134602556"}, {"file": "libs/ui/pnpm-lock.yaml", "hash": "12108480229900642482"}, {"file": "libs/ui/postcss.config.js", "hash": "4493299088900523348"}, {"file": "libs/ui/project.json", "hash": "5327040715736051011"}, {"file": "libs/ui/src/components/accordion.tsx", "hash": "3065251786541785435", "deps": ["npm:@phosphor-icons/react", "npm:@radix-ui/react-accordion", "utils", "npm:react"]}, {"file": "libs/ui/src/components/alert-dialog.tsx", "hash": "8418862485312697119", "deps": ["npm:@radix-ui/react-alert-dialog", "utils", "npm:react"]}, {"file": "libs/ui/src/components/alert.tsx", "hash": "18086284981958119690", "deps": ["utils", "npm:class-variance-authority", "npm:react"]}, {"file": "libs/ui/src/components/aspect-ratio.tsx", "hash": "18392546598639232116", "deps": ["npm:@radix-ui/react-aspect-ratio"]}, {"file": "libs/ui/src/components/avatar.tsx", "hash": "12735807599973946383", "deps": ["npm:@radix-ui/react-avatar", "utils", "npm:react"]}, {"file": "libs/ui/src/components/badge-input.tsx", "hash": "10091330406189160331", "deps": ["npm:react"]}, {"file": "libs/ui/src/components/badge.tsx", "hash": "6114337496914023667", "deps": ["utils", "npm:class-variance-authority"]}, {"file": "libs/ui/src/components/button.tsx", "hash": "15541445124852052267", "deps": ["npm:@radix-ui/react-slot", "utils", "npm:class-variance-authority", "npm:react"]}, {"file": "libs/ui/src/components/card.tsx", "hash": "16227060925019596727", "deps": ["utils", "npm:react"]}, {"file": "libs/ui/src/components/checkbox.tsx", "hash": "18073232777002522346", "deps": ["npm:@phosphor-icons/react", "npm:@radix-ui/react-checkbox", "utils", "npm:react"]}, {"file": "libs/ui/src/components/combobox.tsx", "hash": "14176097336852322382", "deps": ["npm:@phosphor-icons/react", "utils", "npm:react"]}, {"file": "libs/ui/src/components/command.tsx", "hash": "6491642482306081342", "deps": ["npm:@phosphor-icons/react", "utils", "npm:cmdk", "npm:react"]}, {"file": "libs/ui/src/components/context-menu.tsx", "hash": "5709246659496405233", "deps": ["npm:@phosphor-icons/react", "npm:@radix-ui/react-context-menu", "utils", "npm:react"]}, {"file": "libs/ui/src/components/dialog.tsx", "hash": "15491121607442345836", "deps": ["npm:@phosphor-icons/react", "npm:@radix-ui/react-dialog", "utils", "npm:react"]}, {"file": "libs/ui/src/components/dropdown-menu.tsx", "hash": "5110004576665460939", "deps": ["npm:@phosphor-icons/react", "npm:@radix-ui/react-dropdown-menu", "utils", "npm:react"]}, {"file": "libs/ui/src/components/form.tsx", "hash": "16811925648209935431", "deps": ["npm:@radix-ui/react-label", "npm:@radix-ui/react-slot", "hooks", "utils", "npm:react", "npm:react-hook-form"]}, {"file": "libs/ui/src/components/hover-card.tsx", "hash": "10842715945109942625", "deps": ["npm:@radix-ui/react-hover-card", "utils", "npm:react"]}, {"file": "libs/ui/src/components/index.ts", "hash": "3053000390609912929"}, {"file": "libs/ui/src/components/input.tsx", "hash": "8302473063990894530", "deps": ["utils", "npm:react"]}, {"file": "libs/ui/src/components/label.tsx", "hash": "655834849282039695", "deps": ["npm:@radix-ui/react-label", "utils", "npm:class-variance-authority", "npm:react"]}, {"file": "libs/ui/src/components/popover.tsx", "hash": "12414348637376037028", "deps": ["npm:@radix-ui/react-popover", "utils", "npm:react"]}, {"file": "libs/ui/src/components/portal.tsx", "hash": "15672315324774401311", "deps": ["npm:@radix-ui/react-portal"]}, {"file": "libs/ui/src/components/resizable-panel.tsx", "hash": "6074390571806340136", "deps": ["npm:@phosphor-icons/react", "utils", "npm:react-resizable-panels"]}, {"file": "libs/ui/src/components/rich-input.tsx", "hash": "16346678810757344537", "deps": ["npm:@hookform/resolvers", "npm:@phosphor-icons/react", "npm:@radix-ui/react-popover", "utils", "npm:@tiptap/extension-highlight", "npm:@tiptap/extension-image", "npm:@tiptap/extension-link", "npm:@tiptap/extension-text-align", "npm:@tiptap/extension-underline", "npm:@tiptap/react", "npm:@tiptap/starter-kit", "npm:react", "npm:react-hook-form", "npm:zod"]}, {"file": "libs/ui/src/components/scroll-area.tsx", "hash": "12282450063244623687", "deps": ["npm:@radix-ui/react-scroll-area", "utils", "npm:react"]}, {"file": "libs/ui/src/components/select.tsx", "hash": "10033463242220376335", "deps": ["npm:@phosphor-icons/react", "npm:@radix-ui/react-select", "utils", "npm:react"]}, {"file": "libs/ui/src/components/separator.tsx", "hash": "11452440639452159292", "deps": ["npm:@radix-ui/react-separator", "utils", "npm:react"]}, {"file": "libs/ui/src/components/sheet.tsx", "hash": "15682842330657181855", "deps": ["npm:@phosphor-icons/react", "npm:@radix-ui/react-dialog", "utils", "npm:class-variance-authority", "npm:react"]}, {"file": "libs/ui/src/components/shortcut.tsx", "hash": "1297954414389738024", "deps": ["utils", "npm:react", "npm:usehooks-ts"]}, {"file": "libs/ui/src/components/skeleton.tsx", "hash": "12424215694691797364", "deps": ["utils"]}, {"file": "libs/ui/src/components/slider.tsx", "hash": "1945213318258110071", "deps": ["npm:@radix-ui/react-slider", "utils", "npm:react"]}, {"file": "libs/ui/src/components/switch.tsx", "hash": "334424924279981338", "deps": ["npm:@radix-ui/react-switch", "utils", "npm:react"]}, {"file": "libs/ui/src/components/tabs.tsx", "hash": "8106995763097504396", "deps": ["npm:@radix-ui/react-tabs", "utils", "npm:react"]}, {"file": "libs/ui/src/components/toast.tsx", "hash": "1417566894465495864", "deps": ["npm:@phosphor-icons/react", "npm:@radix-ui/react-toast", "utils", "npm:class-variance-authority", "npm:react"]}, {"file": "libs/ui/src/components/toggle-group.tsx", "hash": "12346674486781698896", "deps": ["npm:@radix-ui/react-toggle-group", "utils", "npm:class-variance-authority", "npm:react"]}, {"file": "libs/ui/src/components/toggle.tsx", "hash": "16481143111847449348", "deps": ["npm:@radix-ui/react-toggle", "utils", "npm:class-variance-authority", "npm:react"]}, {"file": "libs/ui/src/components/tooltip.tsx", "hash": "13103363949072541852", "deps": ["npm:@radix-ui/react-tooltip", "utils", "npm:react"]}, {"file": "libs/ui/src/components/visually-hidden.tsx", "hash": "2814963721875054576", "deps": ["npm:@radix-ui/react-visually-hidden"]}, {"file": "libs/ui/src/index.ts", "hash": "10467873360405331838"}, {"file": "libs/ui/src/variants/alert.ts", "hash": "15881213672222412838", "deps": ["npm:class-variance-authority"]}, {"file": "libs/ui/src/variants/badge.ts", "hash": "15128659438311569687", "deps": ["npm:class-variance-authority"]}, {"file": "libs/ui/src/variants/button.ts", "hash": "5825922355362890310", "deps": ["npm:class-variance-authority"]}, {"file": "libs/ui/src/variants/index.ts", "hash": "4301349280890358706"}, {"file": "libs/ui/src/variants/sheet.ts", "hash": "5047421120177618547", "deps": ["npm:class-variance-authority"]}, {"file": "libs/ui/src/variants/toast.ts", "hash": "15541428925630317704", "deps": ["npm:class-variance-authority"]}, {"file": "libs/ui/src/variants/toggle.ts", "hash": "15674269460015610408", "deps": ["npm:class-variance-authority"]}, {"file": "libs/ui/tailwind.config.js", "hash": "7846024680554245663", "deps": ["npm:@nx/react"]}, {"file": "libs/ui/tsconfig.json", "hash": "11468858494619402915"}, {"file": "libs/ui/tsconfig.lib.json", "hash": "1901183383364490034"}, {"file": "libs/ui/tsconfig.spec.json", "hash": "12280283830498555287"}, {"file": "libs/ui/vite.config.ts", "hash": "1999530731069797154", "deps": ["npm:@nx/vite", "npm:@vitejs/plugin-react-swc", "npm:vite", "npm:vite-plugin-dts"]}], "parser": [{"file": "libs/parser/.eslintrc.json", "hash": "9156967004283214833"}, {"file": "libs/parser/.swcrc", "hash": "1208819521669582604"}, {"file": "libs/parser/package.json", "hash": "5131613621891820786", "deps": ["schema", "utils", "npm:nestjs-zod", "npm:zod", "npm:@paralleldrive/cuid2", "npm:jszip", "npm:@swc/helpers"]}, {"file": "libs/parser/pnpm-lock.yaml", "hash": "13918407463574181707"}, {"file": "libs/parser/project.json", "hash": "712990037463603542"}, {"file": "libs/parser/src/index.ts", "hash": "778770840889331130"}, {"file": "libs/parser/src/interfaces/parser.ts", "hash": "8589740411870697631", "deps": ["schema", "npm:nestjs-zod", "npm:zod"]}, {"file": "libs/parser/src/json-resume/index.ts", "hash": "10733872827332576428", "deps": ["npm:@paralleldrive/cuid2", "schema", "utils", "npm:zod"]}, {"file": "libs/parser/src/json-resume/schema.ts", "hash": "13542960494839827988", "deps": ["npm:zod"]}, {"file": "libs/parser/src/linkedin/index.ts", "hash": "17674032799838437824", "deps": ["npm:@paralleldrive/cuid2", "schema", "utils", "npm:jszip", "npm:zod"]}, {"file": "libs/parser/src/linkedin/schema/certification.ts", "hash": "4528163201310033387", "deps": ["npm:zod"]}, {"file": "libs/parser/src/linkedin/schema/education.ts", "hash": "17169848657013923078", "deps": ["npm:zod"]}, {"file": "libs/parser/src/linkedin/schema/email.ts", "hash": "4842383920832868164", "deps": ["npm:zod"]}, {"file": "libs/parser/src/linkedin/schema/index.ts", "hash": "12156966947546023570", "deps": ["npm:zod"]}, {"file": "libs/parser/src/linkedin/schema/language.ts", "hash": "14798347093939456228", "deps": ["npm:zod"]}, {"file": "libs/parser/src/linkedin/schema/position.ts", "hash": "7128006934262618133", "deps": ["npm:zod"]}, {"file": "libs/parser/src/linkedin/schema/profile.ts", "hash": "7070733228541569790", "deps": ["npm:zod"]}, {"file": "libs/parser/src/linkedin/schema/project.ts", "hash": "14754238742328045636", "deps": ["npm:zod"]}, {"file": "libs/parser/src/linkedin/schema/skill.ts", "hash": "12825356865591279308", "deps": ["npm:zod"]}, {"file": "libs/parser/src/reactive-resume-v3/index.ts", "hash": "7514761422644459047", "deps": ["npm:@paralleldrive/cuid2", "schema", "utils", "npm:zod"]}, {"file": "libs/parser/src/reactive-resume-v3/schema.ts", "hash": "11023305820091966814", "deps": ["npm:zod"]}, {"file": "libs/parser/src/reactive-resume/index.ts", "hash": "103129716055685066", "deps": ["schema", "utils", "npm:zod"]}, {"file": "libs/parser/tsconfig.json", "hash": "10294729341131714718"}, {"file": "libs/parser/tsconfig.lib.json", "hash": "17512648156706774563"}, {"file": "libs/parser/tsconfig.spec.json", "hash": "12280283830498555287"}, {"file": "libs/parser/vite.config.ts", "hash": "12102854883295599057", "deps": ["npm:@nx/vite", "npm:vite"]}], "dto": [{"file": "libs/dto/.eslintrc.json", "hash": "9156967004283214833"}, {"file": "libs/dto/.swcrc", "hash": "1208819521669582604"}, {"file": "libs/dto/package.json", "hash": "11368252613559197169", "deps": ["utils", "schema", "npm:@sindresorhus/slugify", "npm:nestjs-zod", "npm:@swc/helpers", "npm:zod", "npm:@paralleldrive/cuid2"]}, {"file": "libs/dto/pnpm-lock.yaml", "hash": "18390227942322866877"}, {"file": "libs/dto/project.json", "hash": "1777565834847766675"}, {"file": "libs/dto/src/auth/forgot-password.ts", "hash": "10082673744408386769", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/auth/index.ts", "hash": "4671220419552486742"}, {"file": "libs/dto/src/auth/login.ts", "hash": "6553095043801461263", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/auth/message.ts", "hash": "13192457274123705529", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/auth/providers.ts", "hash": "14053487726455804392", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/auth/register.ts", "hash": "16844928197450076418", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/auth/reset-password.ts", "hash": "7215010256759668238", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/auth/response.ts", "hash": "11603880744140666424", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/auth/two-factor.ts", "hash": "8042489459577846474", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/auth/update-password.ts", "hash": "4519522821899517690", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/contributors/index.ts", "hash": "12406590180954635046", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/feature/index.ts", "hash": "4137856720736515982", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/index.ts", "hash": "8717693930632473913"}, {"file": "libs/dto/src/resume/create.ts", "hash": "15176655906128333340", "deps": ["npm:@paralleldrive/cuid2", "npm:@sindresorhus/slugify", "npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/resume/delete.ts", "hash": "6320418128017720406", "deps": ["schema", "npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/resume/import.ts", "hash": "17220544303999678469", "deps": ["npm:@paralleldrive/cuid2", "schema", "npm:@sindresorhus/slugify", "npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/resume/index.ts", "hash": "8181934824984798458"}, {"file": "libs/dto/src/resume/resume.ts", "hash": "14552773140301921998", "deps": ["schema", "utils", "npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/resume/update.ts", "hash": "1626598448632260219", "deps": ["npm:nestjs-zod"]}, {"file": "libs/dto/src/resume/url.ts", "hash": "12453480977428467954", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/secrets/index.ts", "hash": "15798419553744285779"}, {"file": "libs/dto/src/secrets/secrets.ts", "hash": "2022468633630057456", "deps": ["schema", "npm:zod"]}, {"file": "libs/dto/src/statistics/index.ts", "hash": "7955688111662991773", "deps": ["npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/src/user/index.ts", "hash": "6156705920193290804"}, {"file": "libs/dto/src/user/update-user.ts", "hash": "1253609040589849839", "deps": ["npm:nestjs-zod"]}, {"file": "libs/dto/src/user/user.ts", "hash": "2584386265357605407", "deps": ["schema", "utils", "npm:nestjs-zod", "npm:zod"]}, {"file": "libs/dto/tsconfig.json", "hash": "10294729341131714718"}, {"file": "libs/dto/tsconfig.lib.json", "hash": "17512648156706774563"}, {"file": "libs/dto/tsconfig.spec.json", "hash": "12280283830498555287"}, {"file": "libs/dto/vite.config.ts", "hash": "365039337158045813", "deps": ["npm:@nx/vite", "npm:vite"]}], "hooks": [{"file": "libs/hooks/.eslintrc.json", "hash": "7241148587014803204"}, {"file": "libs/hooks/package.json", "hash": "16382502097476151391"}, {"file": "libs/hooks/pnpm-lock.yaml", "hash": "12108480229900642482"}, {"file": "libs/hooks/project.json", "hash": "858789202391311688"}, {"file": "libs/hooks/src/hooks/use-breakpoint.ts", "hash": "11213631287756456886", "deps": ["utils", "npm:react", "npm:use-breakpoint"]}, {"file": "libs/hooks/src/hooks/use-form-field.ts", "hash": "11517688024091890027", "deps": ["npm:react", "npm:react-hook-form"]}, {"file": "libs/hooks/src/hooks/use-password-toggle.ts", "hash": "5073171677929871684", "deps": ["npm:react"]}, {"file": "libs/hooks/src/hooks/use-theme.ts", "hash": "13995396875903939973", "deps": ["npm:react", "npm:usehooks-ts"]}, {"file": "libs/hooks/src/index.ts", "hash": "18251319714586948062"}, {"file": "libs/hooks/tsconfig.json", "hash": "11468858494619402915"}, {"file": "libs/hooks/tsconfig.lib.json", "hash": "1852704845435708393"}, {"file": "libs/hooks/tsconfig.spec.json", "hash": "12280283830498555287"}, {"file": "libs/hooks/vite.config.ts", "hash": "16696038041424632811", "deps": ["npm:@nx/vite", "npm:@vitejs/plugin-react-swc", "npm:vite", "npm:vite-plugin-dts"]}]}}}