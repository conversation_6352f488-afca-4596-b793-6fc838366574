{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/ca.js"], "sourcesContent": ["!function(e,s){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=s(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],s):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_ca=s(e.dayjs)}(this,(function(e){\"use strict\";function s(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=s(e),_={name:\"ca\",weekdays:\"Diumenge_Dilluns_Dimarts_Dimecres_Dijous_Divendres_Dissabte\".split(\"_\"),weekdaysShort:\"Dg._Dl._Dt._Dc._Dj._Dv._Ds.\".split(\"_\"),weekdaysMin:\"Dg_Dl_Dt_Dc_Dj_Dv_Ds\".split(\"_\"),months:\"Gener_Febrer_Març_Abril_Maig_Juny_Juliol_Agost_Setembre_Octubre_Novembre_Desembre\".split(\"_\"),monthsShort:\"Gen._Febr._Març_Abr._Maig_Juny_Jul._Ag._Set._Oct._Nov._Des.\".split(\"_\"),weekStart:1,formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM [de] YYYY\",LLL:\"D MMMM [de] YYYY [a les] H:mm\",LLLL:\"dddd D MMMM [de] YYYY [a les] H:mm\",ll:\"D MMM YYYY\",lll:\"D MMM YYYY, H:mm\",llll:\"ddd D MMM YYYY, H:mm\"},relativeTime:{future:\"d'aquí %s\",past:\"fa %s\",s:\"uns segons\",m:\"un minut\",mm:\"%d minuts\",h:\"una hora\",hh:\"%d hores\",d:\"un dia\",dd:\"%d dies\",M:\"un mes\",MM:\"%d mesos\",y:\"un any\",yy:\"%d anys\"},ordinal:function(e){return\"\"+e+(1===e||3===e?\"r\":2===e?\"n\":4===e?\"t\":\"è\")}};return t.default.locale(_,null,!0),_}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,8DAA8D,MAAM,GAAG,GAAE,eAAc,8BAA8B,MAAM,GAAG,GAAE,aAAY,uBAAuB,MAAM,GAAG,GAAE,QAAO,oFAAoF,MAAM,GAAG,GAAE,aAAY,8DAA8D,MAAM,GAAG,GAAE,WAAU,GAAE,SAAQ,EAAC,IAAG,QAAO,KAAI,WAAU,GAAE,cAAa,IAAG,oBAAmB,KAAI,iCAAgC,MAAK,sCAAqC,IAAG,cAAa,KAAI,oBAAmB,MAAK,uBAAsB,GAAE,cAAa,EAAC,QAAO,aAAY,MAAK,SAAQ,GAAE,cAAa,GAAE,YAAW,IAAG,aAAY,GAAE,YAAW,IAAG,YAAW,GAAE,UAAS,IAAG,WAAU,GAAE,UAAS,IAAG,YAAW,GAAE,UAAS,IAAG,UAAS,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAM,KAAGA,MAAG,MAAIA,MAAG,MAAIA,KAAE,MAAI,MAAIA,KAAE,MAAI,MAAIA,KAAE,MAAI;AAAA,MAAI,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e"]}