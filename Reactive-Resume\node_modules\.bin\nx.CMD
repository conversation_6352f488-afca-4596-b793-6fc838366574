@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules\nx\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules\nx\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules\nx\bin\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules\nx\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules\nx\bin\nx.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules\nx\bin\nx.js" %*
)
