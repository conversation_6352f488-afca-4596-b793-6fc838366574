{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/pl.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_pl=t(e.dayjs)}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var i=t(e);function a(e){return e%10<5&&e%10>1&&~~(e/10)%10!=1}function n(e,t,i){var n=e+\" \";switch(i){case\"m\":return t?\"minuta\":\"minutę\";case\"mm\":return n+(a(e)?\"minuty\":\"minut\");case\"h\":return t?\"godzina\":\"godzinę\";case\"hh\":return n+(a(e)?\"godziny\":\"godzin\");case\"MM\":return n+(a(e)?\"miesiące\":\"miesięcy\");case\"yy\":return n+(a(e)?\"lata\":\"lat\")}}var r=\"stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_września_października_listopada_grudnia\".split(\"_\"),_=\"styczeń_luty_marzec_kwiecień_maj_czerwiec_lipiec_sierpień_wrzesień_październik_listopad_grudzień\".split(\"_\"),s=/D MMMM/,d=function(e,t){return s.test(t)?r[e.month()]:_[e.month()]};d.s=_,d.f=r;var o={name:\"pl\",weekdays:\"niedziela_poniedziałek_wtorek_środa_czwartek_piątek_sobota\".split(\"_\"),weekdaysShort:\"ndz_pon_wt_śr_czw_pt_sob\".split(\"_\"),weekdaysMin:\"Nd_Pn_Wt_Śr_Cz_Pt_So\".split(\"_\"),months:d,monthsShort:\"sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paź_lis_gru\".split(\"_\"),ordinal:function(e){return e+\".\"},weekStart:1,yearStart:4,relativeTime:{future:\"za %s\",past:\"%s temu\",s:\"kilka sekund\",m:n,mm:n,h:n,hh:n,d:\"1 dzień\",dd:\"%d dni\",M:\"miesiąc\",MM:n,y:\"rok\",yy:n},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"}};return i.default.locale(o,null,!0),o}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC;AAAE,eAAS,EAAEA,IAAE;AAAC,eAAOA,KAAE,KAAG,KAAGA,KAAE,KAAG,KAAG,CAAC,EAAEA,KAAE,MAAI,MAAI;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,YAAIC,KAAEH,KAAE;AAAI,gBAAOE,IAAE;AAAA,UAAC,KAAI;AAAI,mBAAOD,KAAE,WAAS;AAAA,UAAS,KAAI;AAAK,mBAAOE,MAAG,EAAEH,EAAC,IAAE,WAAS;AAAA,UAAS,KAAI;AAAI,mBAAOC,KAAE,YAAU;AAAA,UAAU,KAAI;AAAK,mBAAOE,MAAG,EAAEH,EAAC,IAAE,YAAU;AAAA,UAAU,KAAI;AAAK,mBAAOG,MAAG,EAAEH,EAAC,IAAE,aAAW;AAAA,UAAY,KAAI;AAAK,mBAAOG,MAAG,EAAEH,EAAC,IAAE,SAAO;AAAA,QAAM;AAAA,MAAC;AAAC,UAAI,IAAE,qGAAqG,MAAM,GAAG,GAAE,IAAE,mGAAmG,MAAM,GAAG,GAAE,IAAE,UAAS,IAAE,SAASA,IAAEC,IAAE;AAAC,eAAO,EAAE,KAAKA,EAAC,IAAE,EAAED,GAAE,MAAM,CAAC,IAAE,EAAEA,GAAE,MAAM,CAAC;AAAA,MAAC;AAAE,QAAE,IAAE,GAAE,EAAE,IAAE;AAAE,UAAI,IAAE,EAAC,MAAK,MAAK,UAAS,6DAA6D,MAAM,GAAG,GAAE,eAAc,2BAA2B,MAAM,GAAG,GAAE,aAAY,uBAAuB,MAAM,GAAG,GAAE,QAAO,GAAE,aAAY,kDAAkD,MAAM,GAAG,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA,KAAE;AAAA,MAAG,GAAE,WAAU,GAAE,WAAU,GAAE,cAAa,EAAC,QAAO,SAAQ,MAAK,WAAU,GAAE,gBAAe,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,WAAU,IAAG,UAAS,GAAE,WAAU,IAAG,GAAE,GAAE,OAAM,IAAG,EAAC,GAAE,SAAQ,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,eAAc,KAAI,qBAAoB,MAAK,0BAAyB,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e", "t", "i", "n"]}