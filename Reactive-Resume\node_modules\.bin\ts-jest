#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770/node_modules/ts-jest/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770/node_modules/ts-jest/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770/node_modules/ts-jest/cli.js" "$@"
else
  exec node  "$basedir/../.pnpm/ts-jest@29.2.5_@babel+core@_8fd43dc212c191fff52e9a78ea62a770/node_modules/ts-jest/cli.js" "$@"
fi
