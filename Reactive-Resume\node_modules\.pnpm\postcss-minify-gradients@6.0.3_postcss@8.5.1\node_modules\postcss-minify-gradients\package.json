{"name": "postcss-minify-gradients", "version": "6.0.3", "description": "Minify gradient parameters with PostCSS.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["LICENSE-MIT", "src", "types"], "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"colord": "^2.9.3", "postcss-value-parser": "^4.2.0", "cssnano-utils": "^4.0.2"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "devDependencies": {"postcss": "^8.4.35"}, "peerDependencies": {"postcss": "^8.4.31"}}