hoistPattern:
  - '*'
hoistedDependencies:
  '@adobe/css-tools@4.3.3':
    '@adobe/css-tools': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@angular-devkit/core@17.3.11(chokidar@3.6.0)':
    '@angular-devkit/core': private
  '@angular-devkit/schematics@17.3.11(chokidar@3.6.0)':
    '@angular-devkit/schematics': private
  '@asamuzakjp/css-color@2.8.3':
    '@asamuzakjp/css-color': private
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': private
  '@babel/compat-data@7.26.5':
    '@babel/compat-data': private
  '@babel/generator@7.26.5':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.25.9':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.26.5':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.25.9(@babel/core@7.26.7)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.26.3(@babel/core@7.26.7)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.3(@babel/core@7.26.7)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-member-expression-to-functions@7.25.9':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.25.9':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.7)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.25.9':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.26.5':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.25.9(@babel/core@7.26.7)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.26.5(@babel/core@7.26.7)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.25.9':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.25.9':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.25.9':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.26.7':
    '@babel/helpers': private
  '@babel/parser@7.26.7':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-decorators@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-proposal-decorators': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.26.7)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.26.7)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.26.7)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.26.7)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.26.7)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-decorators@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-import-assertions@7.26.0(@babel/core@7.26.7)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.26.0(@babel/core@7.26.7)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.26.7)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.26.7)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.26.7)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.26.7)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.26.7)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.26.7)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.26.7)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.26.7)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.26.7)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.26.7)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.26.7)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.26.5(@babel/core@7.26.7)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.26.0(@babel/core@7.26.7)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-exponentiation-operator@7.26.3(@babel/core@7.26.7)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.26.3(@babel/core@7.26.7)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.26.6(@babel/core@7.26.7)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-react-constant-elements@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-react-constant-elements': private
  '@babel/plugin-transform-react-display-name@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-react-display-name': private
  '@babel/plugin-transform-react-jsx-development@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-react-jsx-development': private
  '@babel/plugin-transform-react-jsx-self@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/plugin-transform-react-jsx@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-react-jsx': private
  '@babel/plugin-transform-react-pure-annotations@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-react-pure-annotations': private
  '@babel/plugin-transform-regenerator@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.26.0(@babel/core@7.26.7)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-runtime@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-runtime': private
  '@babel/plugin-transform-shorthand-properties@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.26.7(@babel/core@7.26.7)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-typescript@7.26.7(@babel/core@7.26.7)':
    '@babel/plugin-transform-typescript': private
  '@babel/plugin-transform-unicode-escapes@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.25.9(@babel/core@7.26.7)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.26.7(@babel/core@7.26.7)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.26.7)':
    '@babel/preset-modules': private
  '@babel/preset-typescript@7.26.0(@babel/core@7.26.7)':
    '@babel/preset-typescript': private
  '@babel/runtime@7.26.7':
    '@babel/runtime': private
  '@babel/template@7.25.9':
    '@babel/template': private
  '@babel/traverse@7.26.7':
    '@babel/traverse': private
  '@babel/types@7.26.7':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@css-inline/css-inline-android-arm-eabi@0.14.1':
    '@css-inline/css-inline-android-arm-eabi': private
  '@css-inline/css-inline-android-arm64@0.14.1':
    '@css-inline/css-inline-android-arm64': private
  '@css-inline/css-inline-darwin-arm64@0.14.1':
    '@css-inline/css-inline-darwin-arm64': private
  '@css-inline/css-inline-darwin-x64@0.14.1':
    '@css-inline/css-inline-darwin-x64': private
  '@css-inline/css-inline-linux-arm-gnueabihf@0.14.1':
    '@css-inline/css-inline-linux-arm-gnueabihf': private
  '@css-inline/css-inline-linux-arm64-gnu@0.14.1':
    '@css-inline/css-inline-linux-arm64-gnu': private
  '@css-inline/css-inline-linux-arm64-musl@0.14.1':
    '@css-inline/css-inline-linux-arm64-musl': private
  '@css-inline/css-inline-linux-x64-gnu@0.14.1':
    '@css-inline/css-inline-linux-x64-gnu': private
  '@css-inline/css-inline-linux-x64-musl@0.14.1':
    '@css-inline/css-inline-linux-x64-musl': private
  '@css-inline/css-inline-win32-x64-msvc@0.14.1':
    '@css-inline/css-inline-win32-x64-msvc': private
  '@css-inline/css-inline@0.14.1':
    '@css-inline/css-inline': private
  '@csstools/color-helpers@5.0.1':
    '@csstools/color-helpers': private
  '@csstools/css-calc@2.1.1(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    '@csstools/css-calc': private
  '@csstools/css-color-parser@3.0.7(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    '@csstools/css-color-parser': private
  '@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.3':
    '@csstools/css-tokenizer': private
  '@dnd-kit/accessibility@3.1.1(react@18.3.1)':
    '@dnd-kit/accessibility': private
  '@emnapi/core@1.3.1':
    '@emnapi/core': private
  '@emnapi/runtime@1.3.1':
    '@emnapi/runtime': private
  '@emnapi/wasi-threads@1.0.1':
    '@emnapi/wasi-threads': private
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.17.19':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.17.19':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.17.19':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.17.19':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.17.19':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.17.19':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.17.19':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.17.19':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.17.19':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.17.19':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.17.19':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.17.19':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.17.19':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.17.19':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.17.19':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.17.19':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.17.19':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.17.19':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.17.19':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.17.19':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.17.19':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.17.19':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.4.1(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/compat@1.2.5(eslint@8.57.1)':
    '@eslint/compat': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@floating-ui/core@1.6.9':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.13':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@graphql-tools/merge@9.0.11(graphql@16.8.1)':
    '@graphql-tools/merge': private
  '@graphql-tools/schema@10.0.10(graphql@16.8.1)':
    '@graphql-tools/schema': private
  '@graphql-tools/utils@10.6.1(graphql@16.8.1)':
    '@graphql-tools/utils': private
  '@graphql-typed-document-node/core@3.2.0(graphql@16.8.1)':
    '@graphql-typed-document-node/core': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.0.4':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.0.5':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-s390x@1.0.4':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.0.4':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.0.4':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.0.4':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.33.5':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.33.5':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.33.5':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.33.5':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.33.5':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.33.5':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.33.5':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.33.5':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0(babel-plugin-macros@3.1.0)(ts-node@10.9.2(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(typescript@5.7.3))':
    '@jest/core': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@29.7.0':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@jsonjoy.com/base64@1.1.2(tslib@2.8.1)':
    '@jsonjoy.com/base64': private
  '@jsonjoy.com/json-pack@1.1.1(tslib@2.8.1)':
    '@jsonjoy.com/json-pack': private
  '@jsonjoy.com/util@1.5.0(tslib@2.8.1)':
    '@jsonjoy.com/util': private
  '@leichtgewicht/ip-codec@2.0.5':
    '@leichtgewicht/ip-codec': private
  '@lingui/babel-plugin-extract-messages@4.14.1':
    '@lingui/babel-plugin-extract-messages': private
  '@lingui/format-po@4.14.1(typescript@5.7.3)':
    '@lingui/format-po': private
  '@lingui/message-utils@4.14.1':
    '@lingui/message-utils': private
  '@lukeed/csprng@1.1.0':
    '@lukeed/csprng': private
  '@messageformat/parser@5.1.1':
    '@messageformat/parser': private
  '@microsoft/api-extractor-model@7.30.3(@types/node@22.13.0)':
    '@microsoft/api-extractor-model': private
  '@microsoft/api-extractor@7.49.2(@types/node@22.13.0)':
    '@microsoft/api-extractor': private
  '@microsoft/tsdoc-config@0.17.1':
    '@microsoft/tsdoc-config': private
  '@microsoft/tsdoc@0.15.1':
    '@microsoft/tsdoc': private
  '@module-federation/bridge-react-webpack-plugin@0.6.16':
    '@module-federation/bridge-react-webpack-plugin': private
  '@module-federation/data-prefetch@0.6.16(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@module-federation/data-prefetch': private
  '@module-federation/dts-plugin@0.6.16(typescript@5.7.3)(vue-tsc@2.0.29(typescript@5.7.3))':
    '@module-federation/dts-plugin': private
  '@module-federation/enhanced@0.6.16(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(typescript@5.7.3)(vue-tsc@2.0.29(typescript@5.7.3))(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15)))':
    '@module-federation/enhanced': private
  '@module-federation/error-codes@0.6.14':
    '@module-federation/error-codes': private
  '@module-federation/managers@0.6.16':
    '@module-federation/managers': private
  '@module-federation/manifest@0.6.16(typescript@5.7.3)(vue-tsc@2.0.29(typescript@5.7.3))':
    '@module-federation/manifest': private
  '@module-federation/rspack@0.6.16(typescript@5.7.3)(vue-tsc@2.0.29(typescript@5.7.3))':
    '@module-federation/rspack': private
  '@module-federation/runtime-tools@0.6.16':
    '@module-federation/runtime-tools': private
  '@module-federation/runtime@0.6.16':
    '@module-federation/runtime': private
  '@module-federation/sdk@0.6.16':
    '@module-federation/sdk': private
  '@module-federation/third-party-dts-extractor@0.6.16':
    '@module-federation/third-party-dts-extractor': private
  '@module-federation/webpack-bundler-runtime@0.6.16':
    '@module-federation/webpack-bundler-runtime': private
  '@mole-inc/bin-wrapper@8.0.1':
    '@mole-inc/bin-wrapper': private
  '@napi-rs/nice-android-arm-eabi@1.0.1':
    '@napi-rs/nice-android-arm-eabi': private
  '@napi-rs/nice-android-arm64@1.0.1':
    '@napi-rs/nice-android-arm64': private
  '@napi-rs/nice-darwin-arm64@1.0.1':
    '@napi-rs/nice-darwin-arm64': private
  '@napi-rs/nice-darwin-x64@1.0.1':
    '@napi-rs/nice-darwin-x64': private
  '@napi-rs/nice-freebsd-x64@1.0.1':
    '@napi-rs/nice-freebsd-x64': private
  '@napi-rs/nice-linux-arm-gnueabihf@1.0.1':
    '@napi-rs/nice-linux-arm-gnueabihf': private
  '@napi-rs/nice-linux-arm64-gnu@1.0.1':
    '@napi-rs/nice-linux-arm64-gnu': private
  '@napi-rs/nice-linux-arm64-musl@1.0.1':
    '@napi-rs/nice-linux-arm64-musl': private
  '@napi-rs/nice-linux-ppc64-gnu@1.0.1':
    '@napi-rs/nice-linux-ppc64-gnu': private
  '@napi-rs/nice-linux-riscv64-gnu@1.0.1':
    '@napi-rs/nice-linux-riscv64-gnu': private
  '@napi-rs/nice-linux-s390x-gnu@1.0.1':
    '@napi-rs/nice-linux-s390x-gnu': private
  '@napi-rs/nice-linux-x64-gnu@1.0.1':
    '@napi-rs/nice-linux-x64-gnu': private
  '@napi-rs/nice-linux-x64-musl@1.0.1':
    '@napi-rs/nice-linux-x64-musl': private
  '@napi-rs/nice-win32-arm64-msvc@1.0.1':
    '@napi-rs/nice-win32-arm64-msvc': private
  '@napi-rs/nice-win32-ia32-msvc@1.0.1':
    '@napi-rs/nice-win32-ia32-msvc': private
  '@napi-rs/nice-win32-x64-msvc@1.0.1':
    '@napi-rs/nice-win32-x64-msvc': private
  '@napi-rs/nice@1.0.1':
    '@napi-rs/nice': private
  '@napi-rs/wasm-runtime@0.2.4':
    '@napi-rs/wasm-runtime': private
  '@nestjs/graphql@12.2.2(@nestjs/common@10.4.15(reflect-metadata@0.2.2)(rxjs@7.8.1))(@nestjs/core@10.4.15)(graphql@16.8.1)(reflect-metadata@0.2.2)':
    '@nestjs/graphql': private
  '@nestjs/mapped-types@2.0.5(@nestjs/common@10.4.15(reflect-metadata@0.2.2)(rxjs@7.8.1))(reflect-metadata@0.2.2)':
    '@nestjs/mapped-types': private
  '@noble/hashes@1.7.1':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nrwl/devkit@19.8.14(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))':
    '@nrwl/devkit': private
  '@nrwl/eslint-plugin-nx@19.8.14(@babel/traverse@7.26.7)(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(@typescript-eslint/parser@8.23.0(eslint@8.57.1)(typescript@5.7.3))(eslint-config-prettier@9.1.0(eslint@8.57.1))(eslint@8.57.1)(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))(typescript@5.7.3)':
    '@nrwl/eslint-plugin-nx': private
  '@nrwl/jest@19.8.14(@babel/traverse@7.26.7)(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(babel-plugin-macros@3.1.0)(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))(ts-node@10.9.2(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(typescript@5.7.3))(typescript@5.7.3)':
    '@nrwl/jest': private
  '@nrwl/js@19.8.14(@babel/traverse@7.26.7)(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))(typescript@5.7.3)':
    '@nrwl/js': private
  '@nrwl/nest@19.8.14(@babel/traverse@7.26.7)(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(@zkochan/js-yaml@0.0.7)(babel-plugin-macros@3.1.0)(chokidar@3.6.0)(eslint@8.57.1)(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))(ts-node@10.9.2(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(typescript@5.7.3))(typescript@5.7.3)':
    '@nrwl/nest': private
  '@nrwl/node@19.8.14(@babel/traverse@7.26.7)(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(@zkochan/js-yaml@0.0.7)(babel-plugin-macros@3.1.0)(eslint@8.57.1)(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))(ts-node@10.9.2(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(typescript@5.7.3))(typescript@5.7.3)':
    '@nrwl/node': private
  '@nrwl/react@19.8.14(@babel/traverse@7.26.7)(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(@zkochan/js-yaml@0.0.7)(eslint@8.57.1)(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(typescript@5.7.3)(vue-tsc@2.0.29(typescript@5.7.3))(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15)))':
    '@nrwl/react': private
  '@nrwl/tao@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))':
    '@nrwl/tao': private
  '@nrwl/vite@19.8.14(@babel/traverse@7.26.7)(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))(typescript@5.7.3)(vite@5.4.14(@types/node@22.13.0)(less@4.1.3)(sass@1.83.4)(stylus@0.64.0)(terser@5.37.0))(vitest@2.1.9)':
    '@nrwl/vite': private
  '@nrwl/web@19.8.14(@babel/traverse@7.26.7)(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))(typescript@5.7.3)':
    '@nrwl/web': private
  '@nrwl/webpack@19.8.14(@babel/traverse@7.26.7)(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(typescript@5.7.3)(vue-template-compiler@2.7.16)(vue-tsc@2.0.29(typescript@5.7.3))':
    '@nrwl/webpack': private
  '@nrwl/workspace@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))':
    '@nrwl/workspace': private
  '@nuxtjs/opencollective@0.3.2':
    '@nuxtjs/opencollective': private
  '@nx/devkit@19.8.14(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))':
    '@nx/devkit': private
  '@nx/linter@19.8.14(@babel/traverse@7.26.7)(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(@zkochan/js-yaml@0.0.7)(eslint@8.57.1)(nx@19.8.14(@swc-node/register@1.10.9(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)(typescript@5.7.3))(@swc/core@1.10.12(@swc/helpers@0.5.15)))':
    '@nx/linter': private
  '@nx/nx-darwin-arm64@19.8.14':
    '@nx/nx-darwin-arm64': private
  '@nx/nx-darwin-x64@19.8.14':
    '@nx/nx-darwin-x64': private
  '@nx/nx-freebsd-x64@19.8.14':
    '@nx/nx-freebsd-x64': private
  '@nx/nx-linux-arm-gnueabihf@19.8.14':
    '@nx/nx-linux-arm-gnueabihf': private
  '@nx/nx-linux-arm64-gnu@19.8.14':
    '@nx/nx-linux-arm64-gnu': private
  '@nx/nx-linux-arm64-musl@19.8.14':
    '@nx/nx-linux-arm64-musl': private
  '@nx/nx-linux-x64-gnu@19.8.14':
    '@nx/nx-linux-x64-gnu': private
  '@nx/nx-linux-x64-musl@19.8.14':
    '@nx/nx-linux-x64-musl': private
  '@nx/nx-win32-arm64-msvc@19.8.14':
    '@nx/nx-win32-arm64-msvc': private
  '@nx/nx-win32-x64-msvc@19.8.14':
    '@nx/nx-win32-x64-msvc': private
  '@one-ini/wasm@0.1.1':
    '@one-ini/wasm': private
  '@opentelemetry/api-logs@0.51.1':
    '@opentelemetry/api-logs': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/context-async-hooks': private
  '@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/core': private
  '@opentelemetry/instrumentation-connect@0.36.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-connect': private
  '@opentelemetry/instrumentation-express@0.38.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-express': private
  '@opentelemetry/instrumentation-fastify@0.36.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-fastify': private
  '@opentelemetry/instrumentation-graphql@0.40.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-graphql': private
  '@opentelemetry/instrumentation-hapi@0.38.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-hapi': private
  '@opentelemetry/instrumentation-http@0.51.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-http': private
  '@opentelemetry/instrumentation-ioredis@0.40.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-ioredis': private
  '@opentelemetry/instrumentation-koa@0.40.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-koa': private
  '@opentelemetry/instrumentation-mongodb@0.43.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongodb': private
  '@opentelemetry/instrumentation-mongoose@0.38.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongoose': private
  '@opentelemetry/instrumentation-mysql2@0.38.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql2': private
  '@opentelemetry/instrumentation-mysql@0.38.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql': private
  '@opentelemetry/instrumentation-nestjs-core@0.37.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-nestjs-core': private
  '@opentelemetry/instrumentation-pg@0.41.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-pg': private
  '@opentelemetry/instrumentation@0.51.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation': private
  '@opentelemetry/redis-common@0.36.2':
    '@opentelemetry/redis-common': private
  '@opentelemetry/resources@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/resources': private
  '@opentelemetry/sdk-metrics@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-metrics': private
  '@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-trace-base': private
  '@opentelemetry/semantic-conventions@1.28.0':
    '@opentelemetry/semantic-conventions': private
  '@opentelemetry/sql-common@0.40.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sql-common': private
  '@otplib/core@12.0.1':
    '@otplib/core': private
  '@otplib/plugin-crypto@12.0.1':
    '@otplib/plugin-crypto': private
  '@otplib/plugin-thirty-two@12.0.1':
    '@otplib/plugin-thirty-two': private
  '@otplib/preset-default@12.0.1':
    '@otplib/preset-default': private
  '@otplib/preset-v11@12.0.1':
    '@otplib/preset-v11': private
  '@oxc-resolver/binding-darwin-arm64@1.12.0':
    '@oxc-resolver/binding-darwin-arm64': private
  '@oxc-resolver/binding-darwin-x64@1.12.0':
    '@oxc-resolver/binding-darwin-x64': private
  '@oxc-resolver/binding-freebsd-x64@1.12.0':
    '@oxc-resolver/binding-freebsd-x64': private
  '@oxc-resolver/binding-linux-arm-gnueabihf@1.12.0':
    '@oxc-resolver/binding-linux-arm-gnueabihf': private
  '@oxc-resolver/binding-linux-arm64-gnu@1.12.0':
    '@oxc-resolver/binding-linux-arm64-gnu': private
  '@oxc-resolver/binding-linux-arm64-musl@1.12.0':
    '@oxc-resolver/binding-linux-arm64-musl': private
  '@oxc-resolver/binding-linux-x64-gnu@1.12.0':
    '@oxc-resolver/binding-linux-x64-gnu': private
  '@oxc-resolver/binding-linux-x64-musl@1.12.0':
    '@oxc-resolver/binding-linux-x64-musl': private
  '@oxc-resolver/binding-wasm32-wasi@1.12.0':
    '@oxc-resolver/binding-wasm32-wasi': private
  '@oxc-resolver/binding-win32-arm64-msvc@1.12.0':
    '@oxc-resolver/binding-win32-arm64-msvc': private
  '@oxc-resolver/binding-win32-x64-msvc@1.12.0':
    '@oxc-resolver/binding-win32-x64-msvc': private
  '@parcel/watcher-android-arm64@2.5.1':
    '@parcel/watcher-android-arm64': private
  '@parcel/watcher-darwin-arm64@2.5.1':
    '@parcel/watcher-darwin-arm64': private
  '@parcel/watcher-darwin-x64@2.5.1':
    '@parcel/watcher-darwin-x64': private
  '@parcel/watcher-freebsd-x64@2.5.1':
    '@parcel/watcher-freebsd-x64': private
  '@parcel/watcher-linux-arm-glibc@2.5.1':
    '@parcel/watcher-linux-arm-glibc': private
  '@parcel/watcher-linux-arm-musl@2.5.1':
    '@parcel/watcher-linux-arm-musl': private
  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    '@parcel/watcher-linux-arm64-glibc': private
  '@parcel/watcher-linux-arm64-musl@2.5.1':
    '@parcel/watcher-linux-arm64-musl': private
  '@parcel/watcher-linux-x64-glibc@2.5.1':
    '@parcel/watcher-linux-x64-glibc': private
  '@parcel/watcher-linux-x64-musl@2.5.1':
    '@parcel/watcher-linux-x64-musl': private
  '@parcel/watcher-win32-arm64@2.5.1':
    '@parcel/watcher-win32-arm64': private
  '@parcel/watcher-win32-ia32@2.5.1':
    '@parcel/watcher-win32-ia32': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@pdf-lib/standard-fonts@1.0.0':
    '@pdf-lib/standard-fonts': private
  '@pdf-lib/upng@1.0.1':
    '@pdf-lib/upng': private
  '@phenomnomnominal/tsquery@5.0.1(typescript@5.7.3)':
    '@phenomnomnominal/tsquery': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.1.1':
    '@pkgr/core': private
  '@polka/url@1.0.0-next.28':
    '@polka/url': private
  '@popperjs/core@2.11.8':
    '@popperjs/core': private
  '@prisma/debug@5.22.0':
    '@prisma/debug': private
  '@prisma/engines-version@5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2':
    '@prisma/engines-version': private
  '@prisma/engines@5.22.0':
    '@prisma/engines': private
  '@prisma/fetch-engine@5.22.0':
    '@prisma/fetch-engine': private
  '@prisma/get-platform@5.22.0':
    '@prisma/get-platform': private
  '@prisma/instrumentation@5.13.0':
    '@prisma/instrumentation': private
  '@puppeteer/browsers@2.6.1':
    '@puppeteer/browsers': private
  '@radix-ui/number@1.1.0':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.1':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collapsible@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collapsible': private
  '@radix-ui/react-collection@1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.1(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.1(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.0(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.4(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.1(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.0(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.5(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-presence@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.0.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.1(@types/react-dom@18.3.5(@types/react@18.3.18))(@types/react@18.3.18)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.0(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.1.0(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-escape-keydown@1.1.0(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.0(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.0(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.0(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.0(@types/react@18.3.18)(react@18.3.1)':
    '@radix-ui/react-use-size': private
  '@radix-ui/rect@1.1.0':
    '@radix-ui/rect': private
  '@remirror/core-constants@3.0.0':
    '@remirror/core-constants': private
  '@rollup/pluginutils@5.1.4(rollup@4.32.1)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.32.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.32.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.32.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.32.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.32.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.32.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.32.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.32.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.32.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.32.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.32.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.32.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.32.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-s390x-gnu@4.32.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.32.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.32.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.32.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.32.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.32.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/node-core-library@5.11.0(@types/node@22.13.0)':
    '@rushstack/node-core-library': private
  '@rushstack/rig-package@0.5.3':
    '@rushstack/rig-package': private
  '@rushstack/terminal@0.14.6(@types/node@22.13.0)':
    '@rushstack/terminal': private
  '@rushstack/ts-command-line@4.23.4(@types/node@22.13.0)':
    '@rushstack/ts-command-line': private
  '@schematics/angular@13.3.11(chokidar@3.6.0)':
    '@schematics/angular': private
  '@selderee/plugin-htmlparser2@0.11.0':
    '@selderee/plugin-htmlparser2': private
  '@sentry/core@8.2.1':
    '@sentry/core': private
  '@sentry/node@8.2.1':
    '@sentry/node': private
  '@sentry/opentelemetry@8.2.1(@opentelemetry/api@1.9.0)(@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.51.1(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.28.0)':
    '@sentry/opentelemetry': private
  '@sentry/types@8.2.1':
    '@sentry/types': private
  '@sentry/utils@8.2.1':
    '@sentry/utils': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sindresorhus/is@4.6.0':
    '@sindresorhus/is': private
  '@sindresorhus/transliterate@0.1.2':
    '@sindresorhus/transliterate': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@svgr/babel-plugin-add-jsx-attribute@8.0.0(@babel/core@7.26.7)':
    '@svgr/babel-plugin-add-jsx-attribute': private
  '@svgr/babel-plugin-remove-jsx-attribute@8.0.0(@babel/core@7.26.7)':
    '@svgr/babel-plugin-remove-jsx-attribute': private
  '@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0(@babel/core@7.26.7)':
    '@svgr/babel-plugin-remove-jsx-empty-expression': private
  '@svgr/babel-plugin-replace-jsx-attribute-value@8.0.0(@babel/core@7.26.7)':
    '@svgr/babel-plugin-replace-jsx-attribute-value': private
  '@svgr/babel-plugin-svg-dynamic-title@8.0.0(@babel/core@7.26.7)':
    '@svgr/babel-plugin-svg-dynamic-title': private
  '@svgr/babel-plugin-svg-em-dimensions@8.0.0(@babel/core@7.26.7)':
    '@svgr/babel-plugin-svg-em-dimensions': private
  '@svgr/babel-plugin-transform-react-native-svg@8.1.0(@babel/core@7.26.7)':
    '@svgr/babel-plugin-transform-react-native-svg': private
  '@svgr/babel-plugin-transform-svg-component@8.0.0(@babel/core@7.26.7)':
    '@svgr/babel-plugin-transform-svg-component': private
  '@svgr/babel-preset@8.1.0(@babel/core@7.26.7)':
    '@svgr/babel-preset': private
  '@svgr/core@8.1.0(typescript@5.7.3)':
    '@svgr/core': private
  '@svgr/hast-util-to-babel-ast@8.0.0':
    '@svgr/hast-util-to-babel-ast': private
  '@svgr/plugin-jsx@8.1.0(@svgr/core@8.1.0(typescript@5.7.3))':
    '@svgr/plugin-jsx': private
  '@svgr/plugin-svgo@8.1.0(@svgr/core@8.1.0(typescript@5.7.3))(typescript@5.7.3)':
    '@svgr/plugin-svgo': private
  '@svgr/webpack@8.1.0(typescript@5.7.3)':
    '@svgr/webpack': private
  '@swc-node/core@1.13.3(@swc/core@1.10.12(@swc/helpers@0.5.15))(@swc/types@0.1.17)':
    '@swc-node/core': private
  '@swc-node/sourcemap-support@0.5.1':
    '@swc-node/sourcemap-support': private
  '@swc/core-darwin-arm64@1.10.12':
    '@swc/core-darwin-arm64': private
  '@swc/core-darwin-x64@1.10.12':
    '@swc/core-darwin-x64': private
  '@swc/core-linux-arm-gnueabihf@1.10.12':
    '@swc/core-linux-arm-gnueabihf': private
  '@swc/core-linux-arm64-gnu@1.10.12':
    '@swc/core-linux-arm64-gnu': private
  '@swc/core-linux-arm64-musl@1.10.12':
    '@swc/core-linux-arm64-musl': private
  '@swc/core-linux-x64-gnu@1.10.12':
    '@swc/core-linux-x64-gnu': private
  '@swc/core-linux-x64-musl@1.10.12':
    '@swc/core-linux-x64-musl': private
  '@swc/core-win32-arm64-msvc@1.10.12':
    '@swc/core-win32-arm64-msvc': private
  '@swc/core-win32-ia32-msvc@1.10.12':
    '@swc/core-win32-ia32-msvc': private
  '@swc/core-win32-x64-msvc@1.10.12':
    '@swc/core-win32-x64-msvc': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/types@0.1.17':
    '@swc/types': private
  '@szmarczak/http-timer@4.0.6':
    '@szmarczak/http-timer': private
  '@tanstack/query-core@5.66.0':
    '@tanstack/query-core': private
  '@testing-library/dom@10.1.0':
    '@testing-library/dom': private
  '@tiptap/extension-blockquote@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-blockquote': private
  '@tiptap/extension-bold@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-bold': private
  '@tiptap/extension-bubble-menu@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    '@tiptap/extension-bubble-menu': private
  '@tiptap/extension-bullet-list@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-bullet-list': private
  '@tiptap/extension-code-block@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    '@tiptap/extension-code-block': private
  '@tiptap/extension-code@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-code': private
  '@tiptap/extension-document@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-document': private
  '@tiptap/extension-dropcursor@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    '@tiptap/extension-dropcursor': private
  '@tiptap/extension-floating-menu@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    '@tiptap/extension-floating-menu': private
  '@tiptap/extension-gapcursor@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    '@tiptap/extension-gapcursor': private
  '@tiptap/extension-hard-break@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-hard-break': private
  '@tiptap/extension-heading@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-heading': private
  '@tiptap/extension-history@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    '@tiptap/extension-history': private
  '@tiptap/extension-horizontal-rule@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))(@tiptap/pm@2.11.5)':
    '@tiptap/extension-horizontal-rule': private
  '@tiptap/extension-italic@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-italic': private
  '@tiptap/extension-list-item@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-list-item': private
  '@tiptap/extension-ordered-list@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-ordered-list': private
  '@tiptap/extension-paragraph@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-paragraph': private
  '@tiptap/extension-strike@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-strike': private
  '@tiptap/extension-text-style@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-text-style': private
  '@tiptap/extension-text@2.11.5(@tiptap/core@2.11.5(@tiptap/pm@2.11.5))':
    '@tiptap/extension-text': private
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@tybys/wasm-util@0.9.0':
    '@tybys/wasm-util': private
  '@types/accepts@1.3.7':
    '@types/accepts': private
  '@types/argparse@1.0.38':
    '@types/argparse': private
  '@types/aria-query@5.0.4':
    '@types/aria-query': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.6.8':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.6':
    '@types/babel__traverse': private
  '@types/body-parser@1.19.5':
    '@types/body-parser': private
  '@types/bonjour@3.5.13':
    '@types/bonjour': private
  '@types/cacheable-request@6.0.3':
    '@types/cacheable-request': private
  '@types/connect-history-api-fallback@1.5.4':
    '@types/connect-history-api-fallback': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/content-disposition@0.5.8':
    '@types/content-disposition': private
  '@types/cookie@0.6.0':
    '@types/cookie': private
  '@types/cookies@0.9.0':
    '@types/cookies': private
  '@types/ejs@3.1.5':
    '@types/ejs': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.6':
    '@types/estree': private
  '@types/express-serve-static-core@4.19.6':
    '@types/express-serve-static-core': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/http-assert@1.5.6':
    '@types/http-assert': private
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': private
  '@types/http-errors@2.0.4':
    '@types/http-errors': private
  '@types/http-proxy@1.17.15':
    '@types/http-proxy': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/jsonwebtoken@9.0.5':
    '@types/jsonwebtoken': private
  '@types/keygrip@1.0.6':
    '@types/keygrip': private
  '@types/keyv@3.1.4':
    '@types/keyv': private
  '@types/koa-compose@3.2.8':
    '@types/koa-compose': private
  '@types/koa@2.14.0':
    '@types/koa': private
  '@types/koa__router@12.0.3':
    '@types/koa__router': private
  '@types/linkify-it@5.0.0':
    '@types/linkify-it': private
  '@types/lodash@4.17.15':
    '@types/lodash': private
  '@types/markdown-it@14.1.2':
    '@types/markdown-it': private
  '@types/mdurl@2.0.0':
    '@types/mdurl': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/mjml-core@4.15.1':
    '@types/mjml-core': private
  '@types/mjml@4.7.4':
    '@types/mjml': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/mysql@2.15.22':
    '@types/mysql': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/node-forge@1.3.11':
    '@types/node-forge': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/oauth@0.9.6':
    '@types/oauth': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/passport-oauth2@1.4.17':
    '@types/passport-oauth2': private
  '@types/passport-strategy@0.2.38':
    '@types/passport-strategy': private
  '@types/pg-pool@2.0.4':
    '@types/pg-pool': private
  '@types/pg@8.6.1':
    '@types/pg': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  '@types/pug@2.0.10':
    '@types/pug': private
  '@types/qs@6.9.18':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/responselike@1.0.3':
    '@types/responselike': private
  '@types/semver@7.5.8':
    '@types/semver': private
  '@types/send@0.17.4':
    '@types/send': private
  '@types/serve-index@1.9.4':
    '@types/serve-index': private
  '@types/serve-static@1.15.7':
    '@types/serve-static': private
  '@types/shimmer@1.2.0':
    '@types/shimmer': private
  '@types/sockjs@0.3.36':
    '@types/sockjs': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/use-sync-external-store@0.0.6':
    '@types/use-sync-external-store': private
  '@types/ws@8.5.14':
    '@types/ws': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@typescript-eslint/scope-manager@8.23.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@8.22.0(eslint@8.57.1)(typescript@5.7.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.23.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.23.0(typescript@5.7.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.22.0(eslint@8.57.1)(typescript@5.7.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.23.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@vitest/expect@2.1.9':
    '@vitest/expect': private
  '@vitest/mocker@2.1.9(vite@5.4.14(@types/node@22.13.0)(less@4.1.3)(sass@1.83.4)(stylus@0.64.0)(terser@5.37.0))':
    '@vitest/mocker': private
  '@vitest/pretty-format@2.1.9':
    '@vitest/pretty-format': private
  '@vitest/runner@2.1.9':
    '@vitest/runner': private
  '@vitest/snapshot@2.1.9':
    '@vitest/snapshot': private
  '@vitest/spy@2.1.9':
    '@vitest/spy': private
  '@vitest/utils@2.1.9':
    '@vitest/utils': private
  '@volar/language-core@2.4.11':
    '@volar/language-core': private
  '@volar/source-map@2.4.11':
    '@volar/source-map': private
  '@volar/typescript@2.4.11':
    '@volar/typescript': private
  '@vue/compiler-core@3.5.13':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.13':
    '@vue/compiler-dom': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/language-core@2.2.0(typescript@5.7.3)':
    '@vue/language-core': private
  '@vue/shared@3.5.13':
    '@vue/shared': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  '@yarnpkg/lockfile@1.1.0':
    '@yarnpkg/lockfile': private
  '@yarnpkg/parsers@3.0.0-rc.46':
    '@yarnpkg/parsers': private
  '@zkochan/js-yaml@0.0.7':
    '@zkochan/js-yaml': private
  '@zxing/text-encoding@0.9.0':
    '@zxing/text-encoding': private
  abbrev@2.0.0:
    abbrev: private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@1.3.8:
    accepts: private
  acorn-import-assertions@1.9.0(acorn@8.14.0):
    acorn-import-assertions: private
  acorn-import-attributes@1.9.5(acorn@8.14.0):
    acorn-import-attributes: private
  acorn-jsx@5.3.2(acorn@8.14.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.14.0:
    acorn: private
  address@1.2.2:
    address: private
  adm-zip@0.5.16:
    adm-zip: private
  agent-base@7.1.3:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  ajv-draft-04@1.0.0(ajv@8.13.0):
    ajv-draft-04: private
  ajv-formats@2.1.1(ajv@8.9.0):
    ajv-formats: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@8.17.1:
    ajv: private
  alce@1.2.0:
    alce: private
  alien-signals@0.4.14:
    alien-signals: private
  ansi-align@3.0.1:
    ansi-align: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-html-community@0.0.8:
    ansi-html-community: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  append-field@1.0.0:
    append-field: private
  arch@2.2.0:
    arch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  arr-union@3.1.0:
    arr-union: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-flatten@1.1.1:
    array-flatten: private
  array-includes@3.1.8:
    array-includes: private
  array-timsort@1.0.3:
    array-timsort: private
  array-union@3.0.1:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.5:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  asap@2.0.6:
    asap: private
  assert-never@1.4.0:
    assert-never: private
  assertion-error@2.0.1:
    assertion-error: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  ast-types@0.13.4:
    ast-types: private
  async-function@1.0.0:
    async-function: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.2:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  b4a@1.6.7:
    b4a: private
  babel-jest@29.7.0(@babel/core@7.26.7):
    babel-jest: private
  babel-loader@9.2.1(@babel/core@7.26.7)(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    babel-loader: private
  babel-plugin-const-enum@1.2.0(@babel/core@7.26.7):
    babel-plugin-const-enum: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-plugin-polyfill-corejs2@0.4.12(@babel/core@7.26.7):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.10.6(@babel/core@7.26.7):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.3(@babel/core@7.26.7):
    babel-plugin-polyfill-regenerator: private
  babel-plugin-transform-typescript-metadata@0.3.2(@babel/core@7.26.7)(@babel/traverse@7.26.7):
    babel-plugin-transform-typescript-metadata: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.26.7):
    babel-preset-current-node-syntax: private
  babel-preset-jest@29.6.3(@babel/core@7.26.7):
    babel-preset-jest: private
  babel-walk@3.0.0-canary-5:
    babel-walk: private
  backo2@1.0.2:
    backo2: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.5.4:
    bare-events: private
  bare-fs@4.0.1:
    bare-fs: private
  bare-os@3.4.0:
    bare-os: private
  bare-path@3.0.0:
    bare-path: private
  bare-stream@2.6.4(bare-events@2.5.4):
    bare-stream: private
  base64-js@1.5.1:
    base64-js: private
  base64url@3.0.1:
    base64url: private
  basic-auth@2.0.1:
    basic-auth: private
  basic-ftp@5.0.5:
    basic-ftp: private
  batch@0.6.1:
    batch: private
  big.js@5.2.2:
    big.js: private
  bin-check@4.1.0:
    bin-check: private
  bin-version-check@5.1.0:
    bin-version-check: private
  bin-version@6.0.0:
    bin-version: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@4.1.0:
    bl: private
  block-stream2@2.1.0:
    block-stream2: private
  body-parser@1.20.3:
    body-parser: private
  bonjour-service@1.3.0:
    bonjour-service: private
  boolbase@1.0.0:
    boolbase: private
  boxen@5.1.2:
    boxen: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browser-or-node@2.1.1:
    browser-or-node: private
  browserslist@4.24.4:
    browserslist: private
  bs-logger@0.2.6:
    bs-logger: private
  bser@2.1.1:
    bser: private
  btoa@1.2.1:
    btoa: private
  buffer-crc32@1.0.0:
    buffer-crc32: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@5.7.1:
    buffer: private
  builtin-modules@3.3.0:
    builtin-modules: private
  bundle-name@4.1.0:
    bundle-name: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  cac@6.7.14:
    cac: private
  cache-content-type@1.0.1:
    cache-content-type: private
  cacheable-lookup@5.0.4:
    cacheable-lookup: private
  cacheable-request@7.0.4:
    cacheable-request: private
  call-bind-apply-helpers@1.0.1:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.3:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camel-case@3.0.0:
    camel-case: private
  camelcase-css@2.0.1:
    camelcase-css: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-api@3.0.0:
    caniuse-api: private
  caniuse-lite@1.0.30001696:
    caniuse-lite: private
  chai@5.1.2:
    chai: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  character-parser@2.2.0:
    character-parser: private
  chardet@0.7.0:
    chardet: private
  check-disk-space@3.4.0:
    check-disk-space: private
  check-error@2.1.1:
    check-error: private
  cheerio-select@2.1.0:
    cheerio-select: private
  cheerio@1.0.0-rc.12:
    cheerio: private
  chokidar@3.5.1:
    chokidar: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  chromium-bidi@0.11.0(devtools-protocol@0.0.1367902):
    chromium-bidi: private
  ci-info@4.1.0:
    ci-info: private
  cjs-module-lexer@1.4.1:
    cjs-module-lexer: private
  clean-css@4.2.4:
    clean-css: private
  clean-regexp@1.0.0:
    clean-regexp: private
  cli-boxes@2.2.1:
    cli-boxes: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.6.1:
    cli-spinners: private
  cli-table@0.3.11:
    cli-table: private
  cli-width@3.0.0:
    cli-width: private
  cliui@8.0.1:
    cliui: private
  clone-deep@0.2.4:
    clone-deep: private
  clone-response@1.0.3:
    clone-response: private
  clone@1.0.4:
    clone: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  colord@2.9.3:
    colord: private
  colorette@2.0.20:
    colorette: private
  colors@1.0.3:
    colors: private
  columnify@1.6.0:
    columnify: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@10.0.1:
    commander: private
  comment-json@4.2.5:
    comment-json: private
  common-path-prefix@3.0.0:
    common-path-prefix: private
  compare-versions@6.1.1:
    compare-versions: private
  compressible@2.0.18:
    compressible: private
  compression@1.7.5:
    compression: private
  computeds@0.0.1:
    computeds: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@1.6.2:
    concat-stream: private
  confbox@0.1.8:
    confbox: private
  config-chain@1.1.13:
    config-chain: private
  confusing-browser-globals@1.0.11:
    confusing-browser-globals: private
  connect-history-api-fallback@2.0.0:
    connect-history-api-fallback: private
  consola@2.15.3:
    consola: private
  constantinople@4.0.1:
    constantinople: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  cookies@0.9.1:
    cookies: private
  copy-anything@2.0.6:
    copy-anything: private
  copy-webpack-plugin@10.2.4(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    copy-webpack-plugin: private
  core-js-compat@3.40.0:
    core-js-compat: private
  core-util-is@1.0.3:
    core-util-is: private
  cors@2.8.5:
    cors: private
  corser@2.0.1:
    corser: private
  cosmiconfig@8.3.6(typescript@5.7.3):
    cosmiconfig: private
  create-jest@29.7.0(@types/node@22.13.0)(babel-plugin-macros@3.1.0)(ts-node@10.9.2(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(typescript@5.7.3)):
    create-jest: private
  create-require@1.1.1:
    create-require: private
  crelt@1.0.6:
    crelt: private
  cron-parser@4.9.0:
    cron-parser: private
  cross-inspect@1.0.1:
    cross-inspect: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-declaration-sorter@7.2.0(postcss@8.5.1):
    css-declaration-sorter: private
  css-loader@6.11.0(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    css-loader: private
  css-minimizer-webpack-plugin@5.0.1(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    css-minimizer-webpack-plugin: private
  css-select@5.1.0:
    css-select: private
  css-tree@2.3.1:
    css-tree: private
  css-what@6.1.0:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  cssnano-preset-default@6.1.2(postcss@8.5.1):
    cssnano-preset-default: private
  cssnano-utils@4.0.2(postcss@8.5.1):
    cssnano-utils: private
  cssnano@6.1.2(postcss@8.5.1):
    cssnano: private
  csso@5.0.5:
    csso: private
  cssstyle@4.2.1:
    cssstyle: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  data-urls@5.0.0:
    data-urls: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  date-fns@3.6.0:
    date-fns: private
  date-format@4.0.14:
    date-format: private
  de-indent@1.0.2:
    de-indent: private
  debug@4.4.0:
    debug: private
  decimal.js@10.5.0:
    decimal.js: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  decompress-response@6.0.0:
    decompress-response: private
  dedent@1.5.3(babel-plugin-macros@3.1.0):
    dedent: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-equal@1.0.1:
    deep-equal: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  defaults@1.0.4:
    defaults: private
  defer-to-connect@2.0.1:
    defer-to-connect: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@2.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  degenerator@5.0.1:
    degenerator: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  depd@2.0.0:
    depd: private
  dequal@2.0.3:
    dequal: private
  destroy@1.2.0:
    destroy: private
  detect-indent@6.1.0:
    detect-indent: private
  detect-libc@2.0.3:
    detect-libc: private
  detect-newline@3.1.0:
    detect-newline: private
  detect-node-es@1.1.0:
    detect-node-es: private
  detect-node@2.1.0:
    detect-node: private
  detect-port@1.6.1:
    detect-port: private
  devtools-protocol@0.0.1367902:
    devtools-protocol: private
  didyoumean@1.2.2:
    didyoumean: private
  diff-sequences@29.6.3:
    diff-sequences: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  display-notification@2.0.0:
    display-notification: private
  dlv@1.1.3:
    dlv: private
  dns-packet@5.6.1:
    dns-packet: private
  doctrine@2.1.0:
    doctrine: private
  doctypes@1.1.0:
    doctypes: private
  dom-accessibility-api@0.5.16:
    dom-accessibility-api: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dot-case@3.0.4:
    dot-case: private
  dotenv-expand@10.0.0:
    dotenv-expand: private
  dotenv@16.4.5:
    dotenv: private
  dset@3.1.4:
    dset: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  editorconfig@1.0.4:
    editorconfig: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.90:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@9.2.2:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  encodeurl@2.0.0:
    encodeurl: private
  encoding-japanese@2.2.0:
    encoding-japanese: private
  end-of-stream@1.4.4:
    end-of-stream: private
  enhanced-resolve@5.18.0:
    enhanced-resolve: private
  enquirer@2.3.6:
    enquirer: private
  entities@4.5.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  errno@0.1.8:
    errno: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.23.9:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.6.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.0.2:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild@0.17.19:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-goat@3.0.0:
    escape-goat: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-applescript@1.0.0:
    escape-string-applescript: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-module-utils@2.12.0(@typescript-eslint/parser@8.23.0(eslint@8.57.1)(typescript@5.7.3))(eslint-import-resolver-node@0.3.9)(eslint@8.57.1):
    eslint-module-utils: private
  eslint-rule-composer@0.3.0:
    eslint-rule-composer: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter3@5.0.1:
    eventemitter3: private
  events@3.3.0:
    events: private
  execa@0.7.0:
    execa: private
  executable@4.1.1:
    executable: private
  exit@0.1.2:
    exit: private
  expand-tilde@2.0.2:
    expand-tilde: private
  expect-type@1.1.0:
    expect-type: private
  expect@29.7.0:
    expect: private
  express@4.21.2:
    express: private
  ext-list@2.2.2:
    ext-list: private
  ext-name@5.0.0:
    ext-name: private
  extend-object@1.0.0:
    extend-object: private
  external-editor@3.1.0:
    external-editor: private
  extract-zip@2.0.1:
    extract-zip: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.2.7:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-uri@3.0.6:
    fast-uri: private
  fast-xml-parser@4.5.1:
    fast-xml-parser: private
  fastq@1.18.0:
    fastq: private
  faye-websocket@0.11.4:
    faye-websocket: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fdir@6.4.3(picomatch@4.0.2):
    fdir: private
  fflate@0.8.2:
    fflate: private
  figures@3.2.0:
    figures: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  file-loader@6.2.0(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    file-loader: private
  file-type@17.1.6:
    file-type: private
  filelist@1.0.4:
    filelist: private
  filename-reserved-regex@3.0.0:
    filename-reserved-regex: private
  filenamify@5.1.1:
    filenamify: private
  fill-range@7.1.1:
    fill-range: private
  filter-obj@1.1.0:
    filter-obj: private
  finalhandler@1.3.1:
    finalhandler: private
  find-cache-dir@4.0.0:
    find-cache-dir: private
  find-file-up@2.0.1:
    find-file-up: private
  find-pkg@2.0.0:
    find-pkg: private
  find-up@5.0.0:
    find-up: private
  find-versions@5.1.0:
    find-versions: private
  fixpack@4.0.0:
    fixpack: private
  flat-cache@3.2.0:
    flat-cache: private
  flat@5.0.2:
    flat: private
  flatted@3.3.2:
    flatted: private
  follow-redirects@1.15.9(debug@4.4.0):
    follow-redirects: private
  for-each@0.3.4:
    for-each: private
  for-in@1.0.2:
    for-in: private
  for-own@0.1.5:
    for-own: private
  foreground-child@3.3.0:
    foreground-child: private
  fork-ts-checker-webpack-plugin@7.2.13(typescript@5.7.3)(vue-template-compiler@2.7.16)(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    fork-ts-checker-webpack-plugin: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.1:
    form-data: private
  formdata-node@4.4.1:
    formdata-node: private
  forwarded@0.2.0:
    forwarded: private
  fraction.js@4.3.7:
    fraction.js: private
  fresh@0.5.2:
    fresh: private
  front-matter@4.0.2:
    front-matter: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-monkey@1.0.6:
    fs-monkey: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.2.7:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-package-type@0.1.0:
    get-package-type: private
  get-port@5.1.1:
    get-port: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@5.2.0:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-uri@6.0.4:
    get-uri: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@7.2.3:
    glob: private
  global-modules@1.0.0:
    global-modules: private
  global-prefix@1.0.2:
    global-prefix: private
  globals@15.14.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@12.2.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  got@11.8.6:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  graphql-tag@2.12.6(graphql@16.8.1):
    graphql-tag: private
  graphql-ws@5.16.0(graphql@16.8.1):
    graphql-ws: private
  graphql@16.8.1:
    graphql: private
  handle-thing@2.0.1:
    handle-thing: private
  handlebars@4.7.8:
    handlebars: private
  harmony-reflect@1.6.2:
    harmony-reflect: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-own-prop@2.0.0:
    has-own-prop: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  homedir-polyfill@1.0.3:
    homedir-polyfill: private
  hosted-git-info@7.0.2:
    hosted-git-info: private
  hpack.js@2.1.6:
    hpack.js: private
  html-encoding-sniffer@4.0.0:
    html-encoding-sniffer: private
  html-escaper@2.0.2:
    html-escaper: private
  html-minifier@4.0.0:
    html-minifier: private
  html-to-text@9.0.5:
    html-to-text: private
  htmlparser2@8.0.2:
    htmlparser2: private
  http-assert@1.5.0:
    http-assert: private
  http-cache-semantics@4.1.1:
    http-cache-semantics: private
  http-deceiver@1.2.7:
    http-deceiver: private
  http-errors@2.0.0:
    http-errors: private
  http-parser-js@0.5.9:
    http-parser-js: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  http-proxy-middleware@3.0.3:
    http-proxy-middleware: private
  http-proxy@1.18.1(debug@4.4.0):
    http-proxy: private
  http-server@14.1.1:
    http-server: private
  http2-wrapper@1.0.3:
    http2-wrapper: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  humanize-ms@1.2.1:
    humanize-ms: private
  hyperdyperid@1.2.0:
    hyperdyperid: private
  iconv-lite@0.4.24:
    iconv-lite: private
  icss-utils@5.1.0(postcss@8.5.1):
    icss-utils: private
  identity-obj-proxy@3.0.0:
    identity-obj-proxy: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  image-size@0.5.5:
    image-size: private
  immediate@3.0.6:
    immediate: private
  immutable@5.0.3:
    immutable: private
  import-fresh@3.3.0:
    import-fresh: private
  import-in-the-middle@1.7.4:
    import-in-the-middle: private
  import-lazy@4.0.0:
    import-lazy: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  inquirer@7.3.3:
    inquirer: private
  internal-slot@1.1.0:
    internal-slot: private
  invariant@2.2.4:
    invariant: private
  ip-address@9.0.5:
    ip-address: private
  ipaddr.js@2.2.0:
    ipaddr.js: private
  is-arguments@1.2.0:
    is-arguments: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.1:
    is-boolean-object: private
  is-buffer@1.1.6:
    is-buffer: private
  is-builtin-module@3.2.1:
    is-builtin-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-docker@2.2.1:
    is-docker: private
  is-expression@4.0.0:
    is-expression: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-interactive@1.0.0:
    is-interactive: private
  is-map@2.0.3:
    is-map: private
  is-network-error@1.1.0:
    is-network-error: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@3.0.0:
    is-plain-obj: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-promise@2.2.2:
    is-promise: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@1.1.0:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.0:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-what@3.14.1:
    is-what: private
  is-windows@1.0.2:
    is-windows: private
  is-wsl@2.2.0:
    is-wsl: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  isomorphic-rslog@0.0.5:
    isomorphic-rslog: private
  isomorphic-ws@5.0.0(ws@8.18.0):
    isomorphic-ws: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@5.0.6:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  iterall@1.3.0:
    iterall: private
  iterare@1.2.1:
    iterare: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@2.3.6:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0(babel-plugin-macros@3.1.0):
    jest-circus: private
  jest-cli@29.7.0(@types/node@22.13.0)(babel-plugin-macros@3.1.0)(ts-node@10.9.2(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(typescript@5.7.3)):
    jest-cli: private
  jest-config@29.7.0(@types/node@22.13.0)(babel-plugin-macros@3.1.0)(ts-node@10.9.2(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(typescript@5.7.3)):
    jest-config: private
  jest-diff@29.7.0:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@29.7.0:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@29.7.0:
    jest-worker: private
  jiti@1.21.7:
    jiti: private
  jju@1.4.0:
    jju: private
  js-beautify@1.15.1:
    js-beautify: private
  js-cookie@3.0.5:
    js-cookie: private
  js-sha256@0.10.1:
    js-sha256: private
  js-stringify@1.0.2:
    js-stringify: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-stream@1.0.0:
    json-stream: private
  json5@2.2.3:
    json5: private
  jsonc-eslint-parser@2.4.0:
    jsonc-eslint-parser: private
  jsonc-parser@3.3.1:
    jsonc-parser: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jstransformer@1.0.0:
    jstransformer: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  juice@10.0.1:
    juice: private
  jwa@1.4.1:
    jwa: private
  jws@3.2.2:
    jws: private
  keygrip@1.1.0:
    keygrip: private
  keyv@4.5.4:
    keyv: private
  kind-of@3.2.2:
    kind-of: private
  kleur@3.0.3:
    kleur: private
  klona@2.0.6:
    klona: private
  koa-compose@4.1.0:
    koa-compose: private
  koa-convert@2.0.0:
    koa-convert: private
  koa@2.15.3:
    koa: private
  kolorist@1.8.0:
    kolorist: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  launch-editor@2.9.1:
    launch-editor: private
  lazy-cache@1.0.4:
    lazy-cache: private
  leac@0.6.0:
    leac: private
  less-loader@11.1.0(less@4.1.3)(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    less-loader: private
  less@4.1.3:
    less: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  libbase64@1.3.0:
    libbase64: private
  libmime@5.3.6:
    libmime: private
  libqp@2.1.1:
    libqp: private
  license-webpack-plugin@4.0.2(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    license-webpack-plugin: private
  lie@3.3.0:
    lie: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@2.0.3:
    lines-and-columns: private
  linkify-it@5.0.0:
    linkify-it: private
  linkifyjs@4.2.0:
    linkifyjs: private
  liquidjs@10.20.2:
    liquidjs: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@2.0.4:
    loader-utils: private
  local-pkg@0.5.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash.castarray@4.4.0:
    lodash.castarray: private
  lodash.clonedeepwith@4.5.0:
    lodash.clonedeepwith: private
  lodash.deburr@4.1.0:
    lodash.deburr: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  log4js@6.9.1:
    log4js: private
  long-timeout@0.1.1:
    long-timeout: private
  loose-envify@1.4.0:
    loose-envify: private
  loupe@3.1.3:
    loupe: private
  lower-case@1.1.4:
    lower-case: private
  lowercase-keys@2.0.0:
    lowercase-keys: private
  lru-cache@5.1.1:
    lru-cache: private
  luxon@3.5.0:
    luxon: private
  lz-string@1.5.0:
    lz-string: private
  magic-string@0.30.17:
    magic-string: private
  magicast@0.3.5:
    magicast: private
  mailparser@3.7.2:
    mailparser: private
  mailsplit@5.4.2:
    mailsplit: private
  make-dir@4.0.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  makeerror@1.0.12:
    makeerror: private
  markdown-it@14.1.0:
    markdown-it: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdn-data@2.0.30:
    mdn-data: private
  mdurl@2.0.0:
    mdurl: private
  media-typer@0.3.0:
    media-typer: private
  memfs@3.5.3:
    memfs: private
  mensch@0.3.4:
    mensch: private
  merge-deep@3.0.3:
    merge-deep: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-response@3.1.0:
    mimic-response: private
  min-indent@1.0.1:
    min-indent: private
  mini-css-extract-plugin@2.4.7(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    mini-css-extract-plugin: private
  mini-svg-data-uri@1.4.4:
    mini-svg-data-uri: private
  minimalistic-assert@1.0.1:
    minimalistic-assert: private
  minimatch@9.0.3:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mitt@3.0.1:
    mitt: private
  mixin-object@2.0.1:
    mixin-object: private
  mjml-accordion@4.15.3:
    mjml-accordion: private
  mjml-body@4.15.3:
    mjml-body: private
  mjml-button@4.15.3:
    mjml-button: private
  mjml-carousel@4.15.3:
    mjml-carousel: private
  mjml-cli@4.15.3:
    mjml-cli: private
  mjml-column@4.15.3:
    mjml-column: private
  mjml-core@4.15.3:
    mjml-core: private
  mjml-divider@4.15.3:
    mjml-divider: private
  mjml-group@4.15.3:
    mjml-group: private
  mjml-head-attributes@4.15.3:
    mjml-head-attributes: private
  mjml-head-breakpoint@4.15.3:
    mjml-head-breakpoint: private
  mjml-head-font@4.15.3:
    mjml-head-font: private
  mjml-head-html-attributes@4.15.3:
    mjml-head-html-attributes: private
  mjml-head-preview@4.15.3:
    mjml-head-preview: private
  mjml-head-style@4.15.3:
    mjml-head-style: private
  mjml-head-title@4.15.3:
    mjml-head-title: private
  mjml-head@4.15.3:
    mjml-head: private
  mjml-hero@4.15.3:
    mjml-hero: private
  mjml-image@4.15.3:
    mjml-image: private
  mjml-migrate@4.15.3:
    mjml-migrate: private
  mjml-navbar@4.15.3:
    mjml-navbar: private
  mjml-parser-xml@4.15.3:
    mjml-parser-xml: private
  mjml-preset-core@4.15.3:
    mjml-preset-core: private
  mjml-raw@4.15.3:
    mjml-raw: private
  mjml-section@4.15.3:
    mjml-section: private
  mjml-social@4.15.3:
    mjml-social: private
  mjml-spacer@4.15.3:
    mjml-spacer: private
  mjml-table@4.15.3:
    mjml-table: private
  mjml-text@4.15.3:
    mjml-text: private
  mjml-validator@4.15.3:
    mjml-validator: private
  mjml-wrapper@4.15.3:
    mjml-wrapper: private
  mjml@4.15.3:
    mjml: private
  mkdirp@0.5.6:
    mkdirp: private
  mlly@1.7.4:
    mlly: private
  module-details-from-path@1.0.3:
    module-details-from-path: private
  moo@0.5.2:
    moo: private
  motion-dom@11.18.1:
    motion-dom: private
  motion-utils@11.18.1:
    motion-utils: private
  mrmime@2.0.0:
    mrmime: private
  ms@2.0.0:
    ms: private
  muggle-string@0.4.1:
    muggle-string: private
  multer@1.4.4-lts.1:
    multer: private
  multicast-dns@7.2.5:
    multicast-dns: private
  mute-stream@0.0.8:
    mute-stream: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.8:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  needle@3.3.1:
    needle: private
  negotiator@0.6.3:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  netmask@2.0.2:
    netmask: private
  nice-try@1.0.5:
    nice-try: private
  no-case@2.3.2:
    no-case: private
  node-abort-controller@3.1.1:
    node-abort-controller: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch@2.7.0:
    node-fetch: private
  node-forge@1.3.1:
    node-forge: private
  node-int64@0.4.0:
    node-int64: private
  node-machine-id@1.1.12:
    node-machine-id: private
  node-releases@2.0.19:
    node-releases: private
  node-schedule@2.1.1:
    node-schedule: private
  nopt@7.2.1:
    nopt: private
  normalize-package-data@2.5.0:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-url@6.1.0:
    normalize-url: private
  npm-package-arg@11.0.1:
    npm-package-arg: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  nwsapi@2.2.16:
    nwsapi: private
  oauth@0.10.0:
    oauth: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.3:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.8:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  obuf@1.1.2:
    obuf: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  only@0.0.2:
    only: private
  open@8.4.2:
    open: private
  opener@1.5.2:
    opener: private
  opentelemetry-instrumentation-fetch-node@1.2.0:
    opentelemetry-instrumentation-fetch-node: private
  optionator@0.9.4:
    optionator: private
  ora@5.4.1:
    ora: private
  orderedmap@2.1.1:
    orderedmap: private
  os-filter-obj@2.0.0:
    os-filter-obj: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  own-keys@1.0.1:
    own-keys: private
  oxc-resolver@1.12.0:
    oxc-resolver: private
  p-cancelable@2.1.1:
    p-cancelable: private
  p-event@4.2.0:
    p-event: private
  p-finally@1.0.0:
    p-finally: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-retry@6.2.1:
    p-retry: private
  p-timeout@3.2.0:
    p-timeout: private
  p-try@2.2.0:
    p-try: private
  p-wait-for@3.2.0:
    p-wait-for: private
  pac-proxy-agent@7.1.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  pako@1.0.11:
    pako: private
  param-case@2.1.1:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse-node-version@1.0.1:
    parse-node-version: private
  parse-passwd@1.0.0:
    parse-passwd: private
  parse-srcset@1.0.2:
    parse-srcset: private
  parse5-htmlparser2-tree-adapter@7.1.0:
    parse5-htmlparser2-tree-adapter: private
  parse5@4.0.0:
    parse5: private
  parseley@0.12.1:
    parseley: private
  parseurl@1.3.3:
    parseurl: private
  passport-oauth2@1.8.0:
    passport-oauth2: private
  passport-strategy@1.0.0:
    passport-strategy: private
  path-browserify@1.0.1:
    path-browserify: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@3.3.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pathe@1.1.2:
    pathe: private
  pathval@2.0.0:
    pathval: private
  pause@0.0.1:
    pause: private
  peberminta@0.9.0:
    peberminta: private
  peek-readable@5.4.2:
    peek-readable: private
  pend@1.2.0:
    pend: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-protocol@1.7.0:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.6:
    pirates: private
  piscina@4.8.0:
    piscina: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pkg-types@1.3.1:
    pkg-types: private
  pkg-up@3.1.0:
    pkg-up: private
  pluralize@8.0.0:
    pluralize: private
  pofile@1.1.4:
    pofile: private
  portfinder@1.0.32:
    portfinder: private
  possible-typed-array-names@1.0.0:
    possible-typed-array-names: private
  postcss-calc@9.0.1(postcss@8.5.1):
    postcss-calc: private
  postcss-colormin@6.1.0(postcss@8.5.1):
    postcss-colormin: private
  postcss-convert-values@6.1.0(postcss@8.5.1):
    postcss-convert-values: private
  postcss-discard-comments@6.0.2(postcss@8.5.1):
    postcss-discard-comments: private
  postcss-discard-duplicates@6.0.3(postcss@8.5.1):
    postcss-discard-duplicates: private
  postcss-discard-empty@6.0.3(postcss@8.5.1):
    postcss-discard-empty: private
  postcss-discard-overridden@6.0.2(postcss@8.5.1):
    postcss-discard-overridden: private
  postcss-js@4.0.1(postcss@8.5.1):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.1)(ts-node@10.9.2(@swc/core@1.10.12(@swc/helpers@0.5.15))(@types/node@22.13.0)(typescript@5.7.3)):
    postcss-load-config: private
  postcss-loader@6.2.1(postcss@8.5.1)(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    postcss-loader: private
  postcss-merge-longhand@6.0.5(postcss@8.5.1):
    postcss-merge-longhand: private
  postcss-merge-rules@6.1.1(postcss@8.5.1):
    postcss-merge-rules: private
  postcss-minify-font-values@6.1.0(postcss@8.5.1):
    postcss-minify-font-values: private
  postcss-minify-gradients@6.0.3(postcss@8.5.1):
    postcss-minify-gradients: private
  postcss-minify-params@6.1.0(postcss@8.5.1):
    postcss-minify-params: private
  postcss-minify-selectors@6.0.4(postcss@8.5.1):
    postcss-minify-selectors: private
  postcss-modules-extract-imports@3.1.0(postcss@8.5.1):
    postcss-modules-extract-imports: private
  postcss-modules-local-by-default@4.2.0(postcss@8.5.1):
    postcss-modules-local-by-default: private
  postcss-modules-scope@3.2.1(postcss@8.5.1):
    postcss-modules-scope: private
  postcss-modules-values@4.0.0(postcss@8.5.1):
    postcss-modules-values: private
  postcss-normalize-charset@6.0.2(postcss@8.5.1):
    postcss-normalize-charset: private
  postcss-normalize-display-values@6.0.2(postcss@8.5.1):
    postcss-normalize-display-values: private
  postcss-normalize-positions@6.0.2(postcss@8.5.1):
    postcss-normalize-positions: private
  postcss-normalize-repeat-style@6.0.2(postcss@8.5.1):
    postcss-normalize-repeat-style: private
  postcss-normalize-string@6.0.2(postcss@8.5.1):
    postcss-normalize-string: private
  postcss-normalize-timing-functions@6.0.2(postcss@8.5.1):
    postcss-normalize-timing-functions: private
  postcss-normalize-unicode@6.1.0(postcss@8.5.1):
    postcss-normalize-unicode: private
  postcss-normalize-url@6.0.2(postcss@8.5.1):
    postcss-normalize-url: private
  postcss-normalize-whitespace@6.0.2(postcss@8.5.1):
    postcss-normalize-whitespace: private
  postcss-ordered-values@6.0.2(postcss@8.5.1):
    postcss-ordered-values: private
  postcss-reduce-initial@6.1.0(postcss@8.5.1):
    postcss-reduce-initial: private
  postcss-reduce-transforms@6.0.2(postcss@8.5.1):
    postcss-reduce-transforms: private
  postcss-selector-parser@6.0.10:
    postcss-selector-parser: private
  postcss-svgo@6.0.3(postcss@8.5.1):
    postcss-svgo: private
  postcss-unique-selectors@6.0.4(postcss@8.5.1):
    postcss-unique-selectors: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  pretty-format@29.7.0:
    pretty-format: private
  preview-email@3.1.0:
    preview-email: private
  proc-log@3.0.0:
    proc-log: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  progress@2.0.3:
    progress: private
  promise@7.3.1:
    promise: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  prosemirror-changeset@2.2.1:
    prosemirror-changeset: private
  prosemirror-collab@1.3.1:
    prosemirror-collab: private
  prosemirror-commands@1.6.2:
    prosemirror-commands: private
  prosemirror-dropcursor@1.8.1:
    prosemirror-dropcursor: private
  prosemirror-gapcursor@1.3.2:
    prosemirror-gapcursor: private
  prosemirror-history@1.4.1:
    prosemirror-history: private
  prosemirror-inputrules@1.4.0:
    prosemirror-inputrules: private
  prosemirror-keymap@1.2.2:
    prosemirror-keymap: private
  prosemirror-markdown@1.13.1:
    prosemirror-markdown: private
  prosemirror-menu@1.2.4:
    prosemirror-menu: private
  prosemirror-model@1.24.1:
    prosemirror-model: private
  prosemirror-schema-basic@1.2.3:
    prosemirror-schema-basic: private
  prosemirror-schema-list@1.5.0:
    prosemirror-schema-list: private
  prosemirror-state@1.4.3:
    prosemirror-state: private
  prosemirror-tables@1.6.3:
    prosemirror-tables: private
  prosemirror-trailing-node@3.0.0(prosemirror-model@1.24.1)(prosemirror-state@1.4.3)(prosemirror-view@1.37.2):
    prosemirror-trailing-node: private
  prosemirror-transform@1.10.2:
    prosemirror-transform: private
  prosemirror-view@1.37.2:
    prosemirror-view: private
  proto-list@1.2.4:
    proto-list: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-agent@6.5.0:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  prr@1.0.1:
    prr: private
  pseudolocale@2.1.0:
    pseudolocale: private
  pseudomap@1.0.2:
    pseudomap: private
  pug-attrs@3.0.0:
    pug-attrs: private
  pug-code-gen@3.0.3:
    pug-code-gen: private
  pug-error@2.1.0:
    pug-error: private
  pug-filters@4.0.0:
    pug-filters: private
  pug-lexer@5.0.1:
    pug-lexer: private
  pug-linker@4.0.0:
    pug-linker: private
  pug-load@3.0.0:
    pug-load: private
  pug-parser@6.0.0:
    pug-parser: private
  pug-runtime@3.0.1:
    pug-runtime: private
  pug-strip-comments@2.0.0:
    pug-strip-comments: private
  pug-walk@2.0.0:
    pug-walk: private
  pug@3.0.3:
    pug: private
  pump@3.0.2:
    pump: private
  punycode.js@2.3.1:
    punycode.js: private
  punycode@2.3.1:
    punycode: private
  puppeteer-core@23.11.1:
    puppeteer-core: private
  pure-rand@6.1.0:
    pure-rand: private
  qs@6.13.0:
    qs: private
  query-string@7.1.3:
    query-string: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-lru@5.1.1:
    quick-lru: private
  rambda@9.4.2:
    rambda: private
  ramda@0.27.2:
    ramda: private
  random-bytes@1.0.0:
    random-bytes: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  rc@1.2.8:
    rc: private
  react-fast-compare@3.2.2:
    react-fast-compare: private
  react-is@18.3.1:
    react-is: private
  react-refresh@0.14.2:
    react-refresh: private
  react-remove-scroll-bar@2.3.8(@types/react@18.3.18)(react@18.3.1):
    react-remove-scroll-bar: private
  react-remove-scroll@2.6.3(@types/react@18.3.18)(react@18.3.1):
    react-remove-scroll: private
  react-style-singleton@2.2.3(@types/react@18.3.18)(react@18.3.1):
    react-style-singleton: private
  read-cache@1.0.0:
    read-cache: private
  read-pkg-up@7.0.1:
    read-pkg-up: private
  read-pkg@5.2.0:
    read-pkg: private
  readable-stream@2.3.8:
    readable-stream: private
  readable-web-to-node-stream@3.0.2:
    readable-web-to-node-stream: private
  readdirp@3.5.0:
    readdirp: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regenerator-transform@0.15.2:
    regenerator-transform: private
  regexp-tree@0.1.27:
    regexp-tree: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@6.2.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.10.0:
    regjsparser: private
  relateurl@0.2.7:
    relateurl: private
  repeat-string@1.6.1:
    repeat-string: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  require-in-the-middle@7.5.0:
    require-in-the-middle: private
  requires-port@1.0.0:
    requires-port: private
  resolve-alpn@1.2.1:
    resolve-alpn: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-dir@1.0.1:
    resolve-dir: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve.exports@1.1.0:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  responselike@2.0.1:
    responselike: private
  restore-cursor@3.1.0:
    restore-cursor: private
  retry@0.13.1:
    retry: private
  reusify@1.0.4:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.32.1:
    rollup: private
  rope-sequence@1.3.4:
    rope-sequence: private
  rrweb-cssom@0.7.1:
    rrweb-cssom: private
  run-applescript@3.2.0:
    run-applescript: private
  run-async@2.4.1:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sass-loader@12.6.0(sass@1.83.4)(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    sass-loader: private
  sass@1.83.4:
    sass: private
  sax@1.4.1:
    sax: private
  saxes@6.0.0:
    saxes: private
  scheduler@0.23.2:
    scheduler: private
  schema-utils@4.3.0:
    schema-utils: private
  secure-compare@3.0.1:
    secure-compare: private
  selderee@0.11.0:
    selderee: private
  select-hose@2.0.0:
    select-hose: private
  selfsigned@2.4.1:
    selfsigned: private
  semver-regex@4.0.5:
    semver-regex: private
  semver-truncate@3.0.0:
    semver-truncate: private
  semver@6.3.1:
    semver: private
  send@0.19.0:
    send: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-index@1.9.1:
    serve-index: private
  serve-static@1.16.2:
    serve-static: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setimmediate@1.0.5:
    setimmediate: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shallow-clone@0.1.2:
    shallow-clone: private
  shallowequal@1.1.0:
    shallowequal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.2:
    shell-quote: private
  shimmer@1.2.1:
    shimmer: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sirv@3.0.0:
    sirv: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  slick@1.12.2:
    slick: private
  smart-buffer@4.2.0:
    smart-buffer: private
  snake-case@3.0.4:
    snake-case: private
  sockjs@0.3.24:
    sockjs: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.3:
    socks: private
  sort-keys-length@1.0.1:
    sort-keys-length: private
  sort-keys@1.1.2:
    sort-keys: private
  sorted-array-functions@1.3.0:
    sorted-array-functions: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-loader@5.0.0(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    source-map-loader: private
  source-map-support@0.5.19:
    source-map-support: private
  source-map@0.8.0-beta.0:
    source-map: private
  sourcemap-codec@1.4.8:
    sourcemap-codec: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  spdy-transport@3.0.0:
    spdy-transport: private
  spdy@4.0.2:
    spdy: private
  split-on-first@1.1.0:
    split-on-first: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stack-utils@2.0.6:
    stack-utils: private
  stackback@0.0.2:
    stackback: private
  statuses@2.0.1:
    statuses: private
  std-env@3.8.0:
    std-env: private
  stream-chain@2.2.5:
    stream-chain: private
  stream-json@1.9.1:
    stream-json: private
  streamroller@3.1.5:
    streamroller: private
  streamsearch@1.1.0:
    streamsearch: private
  streamx@2.22.0:
    streamx: private
  strict-uri-encode@2.0.0:
    strict-uri-encode: private
  string-argv@0.3.2:
    string-argv: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.1.1:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-eof@1.0.0:
    strip-eof: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-indent@3.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-outer@2.0.0:
    strip-outer: private
  strnum@1.0.5:
    strnum: private
  strong-log-transformer@2.1.0:
    strong-log-transformer: private
  strtok3@7.1.1:
    strtok3: private
  style-loader@3.3.4(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    style-loader: private
  stylehacks@6.1.1(postcss@8.5.1):
    stylehacks: private
  stylus-loader@7.1.3(stylus@0.64.0)(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    stylus-loader: private
  stylus@0.64.0:
    stylus: private
  subscriptions-transport-ws@0.11.0(graphql@16.8.1):
    subscriptions-transport-ws: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-parser@2.0.4:
    svg-parser: private
  svgo@3.3.2:
    svgo: private
  swagger-ui-dist@5.17.14:
    swagger-ui-dist: private
  symbol-observable@1.2.0:
    symbol-observable: private
  symbol-tree@3.2.4:
    symbol-tree: private
  synckit@0.9.2:
    synckit: private
  tapable@2.2.1:
    tapable: private
  tar-fs@3.0.8:
    tar-fs: private
  tar-stream@2.2.0:
    tar-stream: private
  terser-webpack-plugin@5.3.11(@swc/core@1.10.12(@swc/helpers@0.5.15))(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    terser-webpack-plugin: private
  terser@5.37.0:
    terser: private
  test-exclude@7.0.1:
    test-exclude: private
  text-decoder@1.2.3:
    text-decoder: private
  text-table@0.2.0:
    text-table: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  thingies@1.21.0(tslib@2.8.1):
    thingies: private
  thirty-two@1.0.2:
    thirty-two: private
  through2@4.0.2:
    through2: private
  through@2.3.8:
    through: private
  thunky@1.1.0:
    thunky: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.10:
    tinyglobby: private
  tinypool@1.0.2:
    tinypool: private
  tinyrainbow@1.2.0:
    tinyrainbow: private
  tinyspy@3.0.2:
    tinyspy: private
  tippy.js@6.3.7:
    tippy.js: private
  tlds@1.255.0:
    tlds: private
  tldts-core@6.1.75:
    tldts-core: private
  tldts@6.1.75:
    tldts: private
  tmp@0.2.3:
    tmp: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  token-stream@1.0.0:
    token-stream: private
  token-types@5.0.1:
    token-types: private
  totalist@3.0.1:
    totalist: private
  tough-cookie@5.1.0:
    tough-cookie: private
  tr46@5.0.0:
    tr46: private
  tree-dump@1.0.2(tslib@2.8.1):
    tree-dump: private
  trim-repeated@2.0.0:
    trim-repeated: private
  ts-api-utils@2.0.1(typescript@5.7.3):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  ts-loader@9.5.2(typescript@5.7.3)(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    ts-loader: private
  tsconfig-paths-webpack-plugin@4.0.0:
    tsconfig-paths-webpack-plugin: private
  tsconfig-paths@4.2.0:
    tsconfig-paths: private
  tsscmp@1.0.6:
    tsscmp: private
  turbo-stream@2.4.0:
    turbo-stream: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@0.20.2:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typed-assert@1.0.9:
    typed-assert: private
  typed-query-selector@2.12.0:
    typed-query-selector: private
  typedarray@0.0.6:
    typedarray: private
  uc.micro@2.1.0:
    uc.micro: private
  ufo@1.5.4:
    ufo: private
  uglify-js@3.19.3:
    uglify-js: private
  uid-safe@2.1.5:
    uid-safe: private
  uid2@0.0.4:
    uid2: private
  uid@2.0.2:
    uid: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  unbzip2-stream@1.4.3:
    unbzip2-stream: private
  undici-types@6.20.0:
    undici-types: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  union@0.5.0:
    union: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  unraw@3.0.0:
    unraw: private
  upath@2.0.1:
    upath: private
  update-browserslist-db@1.1.2(browserslist@4.24.4):
    update-browserslist-db: private
  upper-case@1.1.3:
    upper-case: private
  uri-js@4.4.1:
    uri-js: private
  url-join@4.0.1:
    url-join: private
  use-callback-ref@1.3.3(@types/react@18.3.18)(react@18.3.1):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@18.3.18)(react@18.3.1):
    use-sidecar: private
  use-sync-external-store@1.4.0(react@18.3.1):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util@0.12.5:
    util: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@11.0.3:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  valid-data-url@3.0.1:
    valid-data-url: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  validate-npm-package-name@5.0.1:
    validate-npm-package-name: private
  value-or-promise@1.0.12:
    value-or-promise: private
  vary@1.1.2:
    vary: private
  vite-node@2.1.9(@types/node@22.13.0)(less@4.1.3)(sass@1.83.4)(stylus@0.64.0)(terser@5.37.0):
    vite-node: private
  void-elements@3.1.0:
    void-elements: private
  vscode-uri@3.0.8:
    vscode-uri: private
  vue-template-compiler@2.7.16:
    vue-template-compiler: private
  vue-tsc@2.0.29(typescript@5.7.3):
    vue-tsc: private
  w3c-keyname@2.2.8:
    w3c-keyname: private
  w3c-xmlserializer@5.0.0:
    w3c-xmlserializer: private
  walker@1.0.8:
    walker: private
  watchpack@2.4.2:
    watchpack: private
  wbuf@1.7.3:
    wbuf: private
  wcwidth@1.0.1:
    wcwidth: private
  web-encoding@1.1.5:
    web-encoding: private
  web-resource-inliner@6.0.1:
    web-resource-inliner: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  webpack-dev-middleware@7.4.2(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    webpack-dev-middleware: private
  webpack-dev-server@5.2.0(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    webpack-dev-server: private
  webpack-node-externals@3.0.0:
    webpack-node-externals: private
  webpack-sources@3.2.3:
    webpack-sources: private
  webpack-subresource-integrity@5.1.0(webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15))):
    webpack-subresource-integrity: private
  webpack@5.97.1(@swc/core@1.10.12(@swc/helpers@0.5.15)):
    webpack: private
  websocket-driver@0.7.4:
    websocket-driver: private
  websocket-extensions@0.1.4:
    websocket-extensions: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@14.1.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.18:
    which-typed-array: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  widest-line@3.1.0:
    widest-line: private
  with@7.0.2:
    with: private
  word-wrap@1.2.5:
    word-wrap: private
  wordwrap@1.0.0:
    wordwrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  ws@8.18.0:
    ws: private
  xml-name-validator@5.0.0:
    xml-name-validator: private
  xml2js@0.6.2:
    xml2js: private
  xml@1.0.1:
    xml: private
  xmlbuilder@11.0.1:
    xmlbuilder: private
  xmlchars@2.2.0:
    xmlchars: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yaml@1.10.2:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  ylru@1.4.0:
    ylru: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - esbuild
  - '@swc/core'
  - '@parcel/watcher'
  - '@nestjs/core'
  - '@prisma/engines'
  - puppeteer
  - sharp
  - nx
  - prisma
  - '@prisma/client'
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.1
pendingBuilds: []
prunedAt: Sun, 08 Jun 2025 07:26:49 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@css-inline/css-inline-android-arm-eabi@0.14.1'
  - '@css-inline/css-inline-android-arm64@0.14.1'
  - '@css-inline/css-inline-darwin-arm64@0.14.1'
  - '@css-inline/css-inline-darwin-x64@0.14.1'
  - '@css-inline/css-inline-linux-arm-gnueabihf@0.14.1'
  - '@css-inline/css-inline-linux-arm64-gnu@0.14.1'
  - '@css-inline/css-inline-linux-arm64-musl@0.14.1'
  - '@css-inline/css-inline-linux-x64-gnu@0.14.1'
  - '@css-inline/css-inline-linux-x64-musl@0.14.1'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.17.19'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.17.19'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.17.19'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-arm64@0.17.19'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-x64@0.17.19'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.17.19'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.17.19'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.17.19'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.17.19'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.17.19'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.17.19'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.17.19'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.17.19'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.17.19'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.17.19'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.17.19'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.17.19'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.17.19'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.17.19'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.17.19'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.17.19'
  - '@esbuild/win32-ia32@0.21.5'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@napi-rs/nice-android-arm-eabi@1.0.1'
  - '@napi-rs/nice-android-arm64@1.0.1'
  - '@napi-rs/nice-darwin-arm64@1.0.1'
  - '@napi-rs/nice-darwin-x64@1.0.1'
  - '@napi-rs/nice-freebsd-x64@1.0.1'
  - '@napi-rs/nice-linux-arm-gnueabihf@1.0.1'
  - '@napi-rs/nice-linux-arm64-gnu@1.0.1'
  - '@napi-rs/nice-linux-arm64-musl@1.0.1'
  - '@napi-rs/nice-linux-ppc64-gnu@1.0.1'
  - '@napi-rs/nice-linux-riscv64-gnu@1.0.1'
  - '@napi-rs/nice-linux-s390x-gnu@1.0.1'
  - '@napi-rs/nice-linux-x64-gnu@1.0.1'
  - '@napi-rs/nice-linux-x64-musl@1.0.1'
  - '@napi-rs/nice-win32-arm64-msvc@1.0.1'
  - '@napi-rs/nice-win32-ia32-msvc@1.0.1'
  - '@napi-rs/wasm-runtime@0.2.6'
  - '@nx/nx-darwin-arm64@19.8.14'
  - '@nx/nx-darwin-x64@19.8.14'
  - '@nx/nx-freebsd-x64@19.8.14'
  - '@nx/nx-linux-arm-gnueabihf@19.8.14'
  - '@nx/nx-linux-arm64-gnu@19.8.14'
  - '@nx/nx-linux-arm64-musl@19.8.14'
  - '@nx/nx-linux-x64-gnu@19.8.14'
  - '@nx/nx-linux-x64-musl@19.8.14'
  - '@nx/nx-win32-arm64-msvc@19.8.14'
  - '@oxc-resolver/binding-darwin-arm64@1.12.0'
  - '@oxc-resolver/binding-darwin-x64@1.12.0'
  - '@oxc-resolver/binding-freebsd-x64@1.12.0'
  - '@oxc-resolver/binding-linux-arm-gnueabihf@1.12.0'
  - '@oxc-resolver/binding-linux-arm64-gnu@1.12.0'
  - '@oxc-resolver/binding-linux-arm64-musl@1.12.0'
  - '@oxc-resolver/binding-linux-x64-gnu@1.12.0'
  - '@oxc-resolver/binding-linux-x64-musl@1.12.0'
  - '@oxc-resolver/binding-wasm32-wasi@1.12.0'
  - '@oxc-resolver/binding-win32-arm64-msvc@1.12.0'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.32.1'
  - '@rollup/rollup-android-arm64@4.32.1'
  - '@rollup/rollup-darwin-arm64@4.32.1'
  - '@rollup/rollup-darwin-x64@4.32.1'
  - '@rollup/rollup-freebsd-arm64@4.32.1'
  - '@rollup/rollup-freebsd-x64@4.32.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.32.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.32.1'
  - '@rollup/rollup-linux-arm64-gnu@4.32.1'
  - '@rollup/rollup-linux-arm64-musl@4.32.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.32.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.32.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.32.1'
  - '@rollup/rollup-linux-s390x-gnu@4.32.1'
  - '@rollup/rollup-linux-x64-gnu@4.32.1'
  - '@rollup/rollup-linux-x64-musl@4.32.1'
  - '@rollup/rollup-win32-arm64-msvc@4.32.1'
  - '@rollup/rollup-win32-ia32-msvc@4.32.1'
  - '@swc/core-darwin-arm64@1.10.12'
  - '@swc/core-darwin-x64@1.10.12'
  - '@swc/core-linux-arm-gnueabihf@1.10.12'
  - '@swc/core-linux-arm64-gnu@1.10.12'
  - '@swc/core-linux-arm64-musl@1.10.12'
  - '@swc/core-linux-x64-gnu@1.10.12'
  - '@swc/core-linux-x64-musl@1.10.12'
  - '@swc/core-win32-arm64-msvc@1.10.12'
  - '@swc/core-win32-ia32-msvc@1.10.12'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm
virtualStoreDirMaxLength: 60
