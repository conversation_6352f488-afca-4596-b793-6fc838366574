{"version": 3, "file": "UbjsonDecoder.js", "sourceRoot": "", "sources": ["../../src/ubjson/UbjsonDecoder.ts"], "names": [], "mappings": ";;;AAAA,iEAA4D;AAC5D,4DAAuD;AAIvD,MAAa,aAAa;IAA1B;QACS,WAAM,GAAG,IAAI,eAAM,EAAE,CAAC;IAqH/B,CAAC;IAnHQ,IAAI,CAAC,KAAiB;QAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAEM,MAAM,CAAC,KAAiB;QAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAEM,OAAO;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;QAC1B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC;YACd,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC;YACd,KAAK,IAAI;gBACP,OAAO,KAAK,CAAC;YACf,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,EAAE,EAAE,CAAC;YACrB,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,EAAE,EAAE,CAAC;YACrB,KAAK,IAAI,CAAC,CAAC,CAAC;gBACV,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAClD,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;gBACd,OAAO,GAAG,CAAC;YACb,CAAC;YACD,KAAK,IAAI,CAAC,CAAC,CAAC;gBACV,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAClD,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;gBACd,OAAO,GAAG,CAAC;YACb,CAAC;YACD,KAAK,IAAI,CAAC,CAAC,CAAC;gBACV,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACpD,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;gBACd,OAAO,GAAG,CAAC;YACb,CAAC;YACD,KAAK,IAAI,CAAC,CAAC,CAAC;gBACV,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACpD,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;gBACd,OAAO,GAAG,CAAC;YACb,CAAC;YACD,KAAK,IAAI,CAAC,CAAC,CAAC;gBACV,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBACrD,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;gBACd,OAAO,GAAG,CAAC;YACb,CAAC;YACD,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,IAAI,CAAC,CAAE,IAAI,CAAC,OAAO,EAAa,CAAC,CAAC;YAClD,KAAK,IAAI;gBACP,OAAO,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAC1C,KAAK,IAAI,CAAC,CAAC,CAAC;gBACV,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;gBAC3B,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;gBACnB,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBACxE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;oBACd,MAAM,IAAI,GAAG,CAAE,IAAI,CAAC,OAAO,EAAa,CAAC;oBACzC,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC1B,CAAC;gBACD,IAAI,IAAI,GAAW,CAAC,CAAC,CAAC;gBACtB,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBACtB,MAAM,CAAC,CAAC,EAAE,CAAC;oBACX,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;gBACrB,CAAC;gBACD,IAAI,KAAK,GAAW,CAAC,CAAC,CAAC;gBACvB,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBACtB,MAAM,CAAC,CAAC,EAAE,CAAC;oBACX,KAAK,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;gBACtB,CAAC;gBACD,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBACtB,MAAM,CAAC,CAAC,EAAE,CAAC;oBACX,IAAI,GAAG,MAAM,CAAC,EAAE,EAAE,CAAC;gBACrB,CAAC;gBACD,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;oBACf,IAAI,QAAQ,GAAW,CAAC,CAAC;oBACzB,QAAQ,IAAI,EAAE,CAAC;wBACb,KAAK,IAAI;4BACP,QAAQ,GAAG,CAAC,CAAC;4BACb,MAAM;wBACR,KAAK,IAAI,CAAC;wBACV,KAAK,IAAI;4BACP,QAAQ,GAAG,CAAC,CAAC;4BACb,MAAM;wBACR,KAAK,IAAI,CAAC;wBACV,KAAK,IAAI;4BACP,QAAQ,GAAG,CAAC,CAAC;4BACb,MAAM;oBACV,CAAC;oBACD,OAAO,IAAI,qCAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC;gBACnE,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,GAAgB,EAAE,CAAC;oBAC5B,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI;wBAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;oBAC1D,MAAM,CAAC,CAAC,EAAE,CAAC;oBACX,OAAO,GAAG,CAAC;gBACb,CAAC;YACH,CAAC;YACD,KAAK,IAAI,CAAC,CAAC,CAAC;gBACV,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;gBAC3B,MAAM,GAAG,GAA8B,EAAE,CAAC;gBAC1C,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBAChC,MAAM,OAAO,GAAG,CAAE,IAAI,CAAC,OAAO,EAAa,CAAC;oBAC5C,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACjC,IAAI,GAAG,KAAK,WAAW;wBAAE,QAA+B;oBACxD,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,CAAC;gBACD,MAAM,CAAC,CAAC,EAAE,CAAC;gBACX,OAAO,GAAG,CAAC;YACb,CAAC;YACD,KAAK,IAAI;gBACP,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,OAAO;IACT,CAAC;CACF;AAtHD,sCAsHC"}