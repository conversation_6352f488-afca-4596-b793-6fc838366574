node:internal/modules/cjs/loader:1408
  throw err;
  ^

Error: Cannot find module 'chalk'
Require stack:
- C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\nx\src\utils\output.js
- C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\nx\src\daemon\server\start.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1405:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)
    at Module._load (node:internal/modules/cjs/loader:1215:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1491:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\nx\src\utils\output.js:4:15)
    at Module._compile (node:internal/modules/cjs/loader:1734:14) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    'C:\\Users\\<USER>\\Documents\\GitHubRepositories\\Reactive-Resume\\node_modules\\nx\\src\\utils\\output.js',
    'C:\\Users\\<USER>\\Documents\\GitHubRepositories\\Reactive-Resume\\node_modules\\nx\\src\\daemon\\server\\start.js'
  ]
}

Node.js v24.1.0
[NX Daemon Server] - 2025-06-07T13:51:15.102Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\6e0f1713483b29e8c40c\d.sock
[NX Daemon Server] - 2025-06-07T13:51:15.131Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume (native)
[NX Daemon Server] - 2025-06-07T13:51:15.137Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-07T13:51:15.139Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-07T13:51:15.143Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-07T13:51:15.147Z - [REQUEST]: Responding to the client. Shutdown initiated
[NX Daemon Server] - 2025-06-07T13:51:15.150Z - Done responding to the client Shutdown initiated
[NX Daemon Server] - 2025-06-07T13:51:15.150Z - Handled FORCE_SHUTDOWN. Handling time: 0. Response time: 3.
[NX Daemon Server] - 2025-06-07T13:51:15.151Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-06-07T13:51:15.152Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume (sources)
[NX Daemon Server] - 2025-06-07T13:51:15.152Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume (outputs)
[NX Daemon Server] - 2025-06-08T07:30:17.315Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\6e0f1713483b29e8c40c\d.sock
[NX Daemon Server] - 2025-06-08T07:30:17.335Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume (native)
[NX Daemon Server] - 2025-06-08T07:30:17.345Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-08T07:30:17.347Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-08T07:30:17.353Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-08T07:30:17.386Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-06-08T07:30:20.655Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules\nx\src\plugins\package-json' 2540.7149999999997ms
[NX Daemon Server] - 2025-06-08T07:30:20.847Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules\nx\src\plugins\js' 3448.24ms
[NX Daemon Server] - 2025-06-08T07:30:21.024Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-08T07:30:21.024Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-08T07:30:21.025Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-08T07:30:21.068Z - Time taken for 'loadNxPlugins' 3620.8782ms
[NX Daemon Server] - 2025-06-08T07:30:27.237Z - Time taken for 'build-project-configs' 6013.946800000001ms
[NX Daemon Server] - 2025-06-08T07:30:32.024Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-08T07:30:32.030Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-06-08T07:30:32.036Z - Time taken for 'total for creating and serializing project graph' 14645.506100000002ms
[NX Daemon Server] - 2025-06-08T07:30:32.040Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-06-08T07:30:32.040Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 14648. Response time: 10.
[NX Daemon Server] - 2025-06-08T07:30:32.761Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-06-08T07:30:32.765Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-06-08T07:30:32.766Z - Handled HASH_TASKS. Handling time: 175. Response time: 5.
[NX Daemon Server] - 2025-06-08T07:30:38.168Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-06-08T07:30:38.174Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-06-08T07:30:38.175Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 29. Response time: 8.
[NX Daemon Server] - 2025-06-08T07:32:32.868Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-08T07:41:14.953Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-08T07:41:14.959Z - [WATCHER]: apps/server/webpack.config.js was modified
[NX Daemon Server] - 2025-06-08T07:41:15.082Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-08T07:41:15.082Z - [REQUEST]: apps/server/webpack.config.js
[NX Daemon Server] - 2025-06-08T07:41:15.082Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-08T07:41:15.106Z - Time taken for 'hash changed files from watcher' 3.0561999999918044ms
[NX Daemon Server] - 2025-06-08T07:41:15.196Z - Time taken for 'build-project-configs' 84.99810000008438ms
[NX Daemon Server] - 2025-06-08T07:41:15.463Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-08T07:41:15.465Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-06-08T07:41:15.465Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-06-08T07:41:15.465Z - Time taken for 'total execution time for createProjectGraph()' 244.38730000006035ms
[NX Daemon Server] - 2025-06-08T08:06:48.935Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-08T08:06:48.939Z - [WATCHER]: _tmp_33816_d34690f83706d63699b24955fd8d6990 was deleted
[NX Daemon Server] - 2025-06-08T08:06:49.156Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-08T08:06:49.156Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-08T08:06:49.156Z - [REQUEST]: _tmp_33816_d34690f83706d63699b24955fd8d6990
[NX Daemon Server] - 2025-06-08T08:06:49.170Z - Time taken for 'hash changed files from watcher' 1.8141999999061227ms
[NX Daemon Server] - 2025-06-08T08:06:49.230Z - Time taken for 'build-project-configs' 47.36269999993965ms
[NX Daemon Server] - 2025-06-08T08:06:49.324Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-08T08:06:49.327Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-06-08T08:06:49.327Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-06-08T08:06:49.327Z - Time taken for 'total execution time for createProjectGraph()' 83.3379000001587ms
[NX Daemon Server] - 2025-06-08T08:07:37.676Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-08T08:07:37.683Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume (sources)
[NX Daemon Server] - 2025-06-08T08:07:37.684Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-06-08T08:07:37.685Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume (outputs)

> NX runtime error err=cannot send event from fs watcher: sending into a closed channel


> NX runtime error err=cannot send event from fs watcher: sending into a closed channel


> NX runtime error err=cannot send event from fs watcher: sending into a closed channel


> NX runtime error err=cannot send event from fs watcher: sending into a closed channel

[NX Daemon Server] - 2025-06-08T08:12:14.137Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\6e0f1713483b29e8c40c\d.sock
[NX Daemon Server] - 2025-06-08T08:12:14.147Z - [WATCHER]: Subscribed to changes within: C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume (native)
[NX Daemon Server] - 2025-06-08T08:12:14.154Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-08T08:12:14.155Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-06-08T08:12:14.159Z - Closed a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-08T08:12:14.166Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-06-08T08:12:15.120Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules\nx\src\plugins\package-json' 813.306ms
[NX Daemon Server] - 2025-06-08T08:12:15.184Z - Time taken for 'Load Nx Plugin: C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\nx@19.8.14_@swc-node+regist_f204a2d323cd53090a77efd1bb8f4b66\node_modules\nx\src\plugins\js' 1012.9675000000001ms
[NX Daemon Server] - 2025-06-08T08:12:15.221Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-08T08:12:15.221Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-08T08:12:15.221Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-08T08:12:15.234Z - Time taken for 'loadNxPlugins' 1049.5003ms
[NX Daemon Server] - 2025-06-08T08:12:16.288Z - Time taken for 'build-project-configs' 1027.1806000000001ms
[NX Daemon Server] - 2025-06-08T08:12:16.395Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-08T08:12:16.397Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-06-08T08:12:16.399Z - Time taken for 'total for creating and serializing project graph' 2230.7545ms
[NX Daemon Server] - 2025-06-08T08:12:16.399Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-06-08T08:12:16.400Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2231. Response time: 3.
[NX Daemon Server] - 2025-06-08T08:12:16.611Z - [REQUEST]: Responding to the client. handleHashTasks
[NX Daemon Server] - 2025-06-08T08:12:16.613Z - Done responding to the client handleHashTasks
[NX Daemon Server] - 2025-06-08T08:12:16.613Z - Handled HASH_TASKS. Handling time: 63. Response time: 2.
[NX Daemon Server] - 2025-06-08T08:12:21.249Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-06-08T08:12:21.250Z - Done responding to the client handleGetEstimatedTaskTimings
[NX Daemon Server] - 2025-06-08T08:12:21.250Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 8. Response time: 1.
[NX Daemon Server] - 2025-06-08T08:12:25.110Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-08T08:12:30.711Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-06-08T08:12:30.712Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-06-08T08:12:30.713Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 2.
[NX Daemon Server] - 2025-06-08T08:13:32.950Z - [WATCHER]: Processing file changes in outputs
[NX Daemon Server] - 2025-06-08T08:13:32.955Z - [WATCHER]: apps/server/webpack.config.js was modified
[NX Daemon Server] - 2025-06-08T08:13:33.060Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-06-08T08:13:33.060Z - [REQUEST]: apps/server/webpack.config.js
[NX Daemon Server] - 2025-06-08T08:13:33.060Z - [REQUEST]: 
[NX Daemon Server] - 2025-06-08T08:13:33.072Z - Time taken for 'hash changed files from watcher' 1.780799999993178ms
[NX Daemon Server] - 2025-06-08T08:13:33.137Z - Time taken for 'build-project-configs' 57.53060000001278ms
[NX Daemon Server] - 2025-06-08T08:13:34.320Z - [SYNC]: collect registered sync generators
[NX Daemon Server] - 2025-06-08T08:13:34.322Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX Daemon Server] - 2025-06-08T08:13:34.322Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX Daemon Server] - 2025-06-08T08:13:34.322Z - Time taken for 'total execution time for createProjectGraph()' 1167.7091999999975ms
[NX Daemon Server] - 2025-06-08T08:13:43.244Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-06-08T08:14:22.418Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-08T08:14:22.419Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-06-08T08:14:22.420Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-06-08T08:14:22.424Z - [REQUEST]: Responding to the client. Shutdown initiated
[NX Daemon Server] - 2025-06-08T08:14:22.425Z - Done responding to the client Shutdown initiated
[NX Daemon Server] - 2025-06-08T08:14:22.425Z - Handled FORCE_SHUTDOWN. Handling time: 0. Response time: 1.
[NX Daemon Server] - 2025-06-08T08:14:22.426Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume (sources)
[NX Daemon Server] - 2025-06-08T08:14:22.427Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-06-08T08:14:22.427Z - [WATCHER]: Stopping the watcher for C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume (outputs)
