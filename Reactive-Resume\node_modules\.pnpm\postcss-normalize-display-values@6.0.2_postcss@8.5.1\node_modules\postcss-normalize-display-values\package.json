{"name": "postcss-normalize-display-values", "version": "6.0.2", "description": "Normalize multiple value display syntaxes into single values.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["LICENSE-MIT", "src", "types"], "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "homepage": "https://github.com/cssnano/cssnano", "engines": {"node": "^14 || ^16 || >=18.0"}, "devDependencies": {"postcss": "^8.4.35"}, "peerDependencies": {"postcss": "^8.4.31"}}