{"version": 3, "file": "maxEncodingCapacity.js", "sourceRoot": "", "sources": ["../../src/json-size/maxEncodingCapacity.ts"], "names": [], "mappings": ";;;AAeO,MAAM,mBAAmB,GAAG,CAAC,KAAc,EAAU,EAAE;IAC5D,QAAQ,OAAO,KAAK,EAAE,CAAC;QACrB,KAAK,QAAQ;YACX,UAAkC;QACpC,KAAK,QAAQ;YACX,OAAO,IAA6B,KAAK,CAAC,MAAM,IAA6C,CAAC;QAChG,KAAK,SAAS;YACZ,SAAmC;QACrC,KAAK,QAAQ,CAAC,CAAC,CAAC;YACd,IAAI,CAAC,KAAK;gBAAE,SAAgC;YAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;YACtC,QAAQ,WAAW,EAAE,CAAC;gBACpB,KAAK,KAAK,CAAC,CAAC,CAAC;oBACX,MAAM,GAAG,GAAG,KAAkB,CAAC;oBAC/B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;oBAC1B,IAAI,IAAI,GAAG,IAA4B,MAAM,IAAmC,CAAC;oBACjF,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;wBAAE,IAAI,IAAI,IAAA,2BAAmB,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9E,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,KAAK,UAAU,CAAC,CAAC,CAAC;oBAChB,OAAO,KAA8B,KAAoB,CAAC,MAAM,IAA6C,CAAC;gBAChH,CAAC;gBACD,KAAK,MAAM,CAAC,CAAC,CAAC;oBACZ,IAAI,IAAI,IAA6B,CAAC;oBACtC,MAAM,GAAG,GAAG,KAAgC,CAAC;oBAC7C,KAAK,MAAM,GAAG,IAAI,GAAG;wBACnB,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC;4BACzB,IAAI,IAAI,IAAoC,IAAA,2BAAmB,EAAC,GAAG,CAAC,GAAG,IAAA,2BAAmB,EAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;oBACzG,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD;oBACE,UAAqC;YACzC,CAAC;QACH,CAAC;QACD,KAAK,QAAQ;YACX,UAAkC;QACpC;YACE,UAAqC;IACzC,CAAC;AACH,CAAC,CAAC;AAvCW,QAAA,mBAAmB,uBAuC9B"}