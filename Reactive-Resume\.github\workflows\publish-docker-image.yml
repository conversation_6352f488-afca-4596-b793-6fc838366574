name: Publish Docker Image

on:
  workflow_dispatch:
  push:
    tags:
      - "*"

concurrency:
  group: "${{ github.workflow }}-${{ github.ref }}"
  cancel-in-progress: true

env:
  IMAGE: amruthpillai/reactive-resume

jobs:
  build:
    runs-on: ubuntu-latest

    outputs:
      version: ${{ steps.version.outputs.version }}

    strategy:
      matrix:
        platform:
          - linux/amd64
          - linux/arm64

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4.1.4

      - name: Extract version from package.json
        id: version
        run: echo "version=$(jq -r '.version' package.json)" >> "$GITHUB_OUTPUT"

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3.0.0

      - name: Login to Docker Hub
        uses: docker/login-action@v3.1.0
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Login to GitHub Container Registery
        uses: docker/login-action@v3.1.0
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ github.token }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3.3.0

      - name: Extract Docker Metadata
        id: meta
        uses: docker/metadata-action@v5.5.1
        with:
          tags: type=semver,pattern={{version}},prefix=v,value=${{ steps.version.outputs.version }}
          images: |
            ${{ env.IMAGE }}
            ghcr.io/${{ env.IMAGE }}

      - name: Prepare a unique name for Artifacts
        id: artifact_name
        run: |
          name=$(echo -n "${{ matrix.platform }}" | sed -e 's/[ \t:\/\\"<>|*?]/-/g' -e 's/--*/-/g')
          echo "name=$name" >> "$GITHUB_OUTPUT"

      - name: Build and Push by Digest
        uses: docker/build-push-action@v5.3.0
        id: build
        with:
          context: .
          platforms: ${{ matrix.platform }}
          labels: ${{ steps.meta.outputs.labels }}
          outputs: type=image,name=${{ env.IMAGE }},push-by-digest=true,name-canonical=true,push=true
          build-args: |
            NX_CLOUD_ACCESS_TOKEN=${{ secrets.NX_CLOUD_ACCESS_TOKEN }}

      - name: Export Digest
        run: |
          mkdir -p /tmp/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"

      - name: Upload Digest
        uses: actions/upload-artifact@v4.3.3
        with:
          name: digests-${{ steps.artifact_name.outputs.name }}
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1

  merge:
    runs-on: ubuntu-latest

    needs:
      - build

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4.1.4

      - name: Download Digest
        uses: actions/download-artifact@v4.1.7
        with:
          path: /tmp/digests
          pattern: digests-*
          merge-multiple: true

      - name: Login to Docker Hub
        uses: docker/login-action@v3.1.0
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Login to GitHub Container Registery
        uses: docker/login-action@v3.1.0
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ github.token }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3.3.0

      - name: Extract Docker Metadata
        id: meta
        uses: docker/metadata-action@v5.5.1
        with:
          tags: type=semver,pattern={{version}},prefix=v,value=${{ needs.build.outputs.version }}
          images: |
            ${{ env.IMAGE }}
            ghcr.io/${{ env.IMAGE }}

      - name: Create Docker Manifest List and Push
        working-directory: /tmp/digests
        run: |
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf '${{ env.IMAGE }}@sha256:%s ' *)

      - name: Inspect Image
        run: |
          docker buildx imagetools inspect ${{ env.IMAGE }}:${{ steps.meta.outputs.version }}

      - name: Update Repository Description
        uses: peter-evans/dockerhub-description@v4.0.0
        with:
          repository: ${{ github.repository }}
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Deploy the latest image on rxresu.me
        run: curl -kX POST ${{ secrets.SERVICE_WEBHOOK }}

      - name: Inform about the release on Discord
        uses: sarisia/actions-status-discord@v1.14.3
        if: always()
        with:
          username: ReleaseBot
          webhook: ${{ secrets.DISCORD_WEBHOOK }}
          status: ${{ job.status }}
          title: "Release `${{ steps.meta.outputs.version }}`"
          description: "A new version of Reactive Resume just dropped! 🚀"
          url: "https://github.com/AmruthPillai/Reactive-Resume"
