import "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/nestjs-zod@3.0.0_@nestjs+co_ff15043dbe9c004b45cb1419a483e175/node_modules/nestjs-zod/dist/dto.mjs
function createZodDto(schema) {
  class AugmentedZodDto {
    static create(input) {
      return this.schema.parse(input);
    }
  }
  AugmentedZodDto.isZodDto = true;
  AugmentedZodDto.schema = schema;
  return AugmentedZodDto;
}
function isZodDto(metatype) {
  return metatype == null ? void 0 : metatype.isZodDto;
}
export {
  createZodDto,
  isZodDto
};
//# sourceMappingURL=nestjs-zod_dto.js.map
