import {
  produce
} from "./chunk-7KFJDXYF.js";
import "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/zustand@4.5.6_@types+react@18.3.18_immer@10.1.1_react@18.3.1/node_modules/zustand/esm/middleware/immer.mjs
var immerImpl = (initializer) => (set, get, store) => {
  store.setState = (updater, replace, ...a) => {
    const nextState = typeof updater === "function" ? produce(updater) : updater;
    return set(nextState, replace, ...a);
  };
  return initializer(store.setState, get, store);
};
var immer = immerImpl;
export {
  immer
};
//# sourceMappingURL=zustand_middleware_immer.js.map
