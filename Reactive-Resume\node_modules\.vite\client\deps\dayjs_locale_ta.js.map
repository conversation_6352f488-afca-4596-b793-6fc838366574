{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/ta.js"], "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_ta=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"ta\",weekdays:\"ஞாயிற்றுக்கிழமை_திங்கட்கிழமை_செவ்வாய்கிழமை_புதன்கிழமை_வியாழக்கிழமை_வெள்ளிக்கிழமை_சனிக்கிழமை\".split(\"_\"),months:\"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்\".split(\"_\"),weekdaysShort:\"ஞாயிறு_திங்கள்_செவ்வாய்_புதன்_வியாழன்_வெள்ளி_சனி\".split(\"_\"),monthsShort:\"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்\".split(\"_\"),weekdaysMin:\"ஞா_தி_செ_பு_வி_வெ_ச\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, HH:mm\",LLLL:\"dddd, D MMMM YYYY, HH:mm\"},relativeTime:{future:\"%s இல்\",past:\"%s முன்\",s:\"ஒரு சில விநாடிகள்\",m:\"ஒரு நிமிடம்\",mm:\"%d நிமிடங்கள்\",h:\"ஒரு மணி நேரம்\",hh:\"%d மணி நேரம்\",d:\"ஒரு நாள்\",dd:\"%d நாட்கள்\",M:\"ஒரு மாதம்\",MM:\"%d மாதங்கள்\",y:\"ஒரு வருடம்\",yy:\"%d ஆண்டுகள்\"}};return t.default.locale(d,null,!0),d}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,8FAA8F,MAAM,GAAG,GAAE,QAAO,0FAA0F,MAAM,GAAG,GAAE,eAAc,mDAAmD,MAAM,GAAG,GAAE,aAAY,0FAA0F,MAAM,GAAG,GAAE,aAAY,sBAAsB,MAAM,GAAG,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA;AAAA,MAAC,GAAE,SAAQ,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,eAAc,KAAI,sBAAqB,MAAK,2BAA0B,GAAE,cAAa,EAAC,QAAO,UAAS,MAAK,WAAU,GAAE,qBAAoB,GAAE,eAAc,IAAG,iBAAgB,GAAE,iBAAgB,IAAG,gBAAe,GAAE,YAAW,IAAG,cAAa,GAAE,aAAY,IAAG,eAAc,GAAE,cAAa,IAAG,cAAa,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["_"]}