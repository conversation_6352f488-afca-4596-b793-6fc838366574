@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\tailwindcss@3.4.17_ts-node@_d4a492b6323c572a6effae2c5548eede\node_modules\tailwindcss\lib\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\tailwindcss@3.4.17_ts-node@_d4a492b6323c572a6effae2c5548eede\node_modules\tailwindcss\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\tailwindcss@3.4.17_ts-node@_d4a492b6323c572a6effae2c5548eede\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\tailwindcss@3.4.17_ts-node@_d4a492b6323c572a6effae2c5548eede\node_modules\tailwindcss\lib\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\tailwindcss@3.4.17_ts-node@_d4a492b6323c572a6effae2c5548eede\node_modules\tailwindcss\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\tailwindcss@3.4.17_ts-node@_d4a492b6323c572a6effae2c5548eede\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\tailwindcss@3.4.17_ts-node@_d4a492b6323c572a6effae2c5548eede\node_modules\tailwindcss\lib\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\tailwindcss@3.4.17_ts-node@_d4a492b6323c572a6effae2c5548eede\node_modules\tailwindcss\lib\cli.js" %*
)
