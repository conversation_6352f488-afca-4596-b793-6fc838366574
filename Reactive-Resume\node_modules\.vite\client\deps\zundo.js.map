{"version": 3, "sources": ["../../../.pnpm/zundo@2.3.0_zustand@4.5.6_@_06bb7f4e76ea0ac514c57617851ac37e/node_modules/zundo/src/index.ts", "../../../.pnpm/zundo@2.3.0_zustand@4.5.6_@_06bb7f4e76ea0ac514c57617851ac37e/node_modules/zundo/src/temporal.ts"], "sourcesContent": ["import { createStore } from 'zustand';\nimport { temporalStateCreator } from './temporal';\nimport type {\n  StateCreator,\n  StoreMutatorIdentifier,\n  Mutate,\n  StoreApi,\n} from 'zustand';\nimport type {\n  TemporalState,\n  _TemporalState,\n  Write,\n  ZundoOptions,\n} from './types';\n\ntype Zundo = <\n  TState,\n  <PERSON><PERSON> extends [StoreMutatorIdentifier, unknown][] = [],\n  <PERSON><PERSON> extends [StoreMutatorIdentifier, unknown][] = [],\n  UState = TState,\n>(\n  config: StateCreator<TState, [...Mps, ['temporal', unknown]], Mcs>,\n  options?: ZundoOptions<TState, UState>,\n) => StateCreator<\n  TState,\n  Mps,\n  [['temporal', StoreApi<TemporalState<UState>>], ...Mcs]\n>;\n\ndeclare module 'zustand/vanilla' {\n  interface StoreMutators<S, A> {\n    temporal: Write<S, { temporal: A }>;\n  }\n}\n\nexport const temporal = (<TState>(\n  config: StateCreator<TState, [], []>,\n  options?: ZundoOptions<TState>,\n): StateCreator<TState, [], []> => {\n  const configWithTemporal = (\n    set: StoreApi<TState>['setState'],\n    get: StoreApi<TState>['getState'],\n    store: Mutate<\n      StoreApi<TState>,\n      [['temporal', StoreApi<TemporalState<TState>>]]\n    >,\n  ) => {\n    store.temporal = createStore(\n      options?.wrapTemporal?.(temporalStateCreator(set, get, options)) ||\n        temporalStateCreator(set, get, options),\n    );\n\n    const curriedHandleSet =\n      options?.handleSet?.(\n        (store.temporal.getState() as _TemporalState<TState>)\n          ._handleSet as StoreApi<TState>['setState'],\n      ) || (store.temporal.getState() as _TemporalState<TState>)._handleSet;\n\n    const temporalHandleSet = (pastState: TState) => {\n      if (!store.temporal.getState().isTracking) return;\n\n      const currentState = options?.partialize?.(get()) || get();\n      const deltaState = options?.diff?.(pastState, currentState);\n      if (\n        // Don't call handleSet if state hasn't changed, as determined by diff fn or equality fn\n        !(\n          // If the user has provided a diff function but nothing has been changed, deltaState will be null\n          (\n            deltaState === null ||\n            // If the user has provided an equality function, use it\n            options?.equality?.(pastState, currentState)\n          )\n        )\n      ) {\n        curriedHandleSet(\n          pastState,\n          undefined as unknown as Parameters<typeof set>[1],\n          currentState,\n          deltaState,\n        );\n      }\n    };\n\n    const setState = store.setState;\n    // Modify the setState function to call the userlandSet function\n    store.setState = (...args) => {\n      // Get most up to date state. The state from the callback might be a partial state.\n      // The order of the get() and set() calls is important here.\n      const pastState = options?.partialize?.(get()) || get();\n      setState(...(args as Parameters<typeof setState>));\n      temporalHandleSet(pastState);\n    };\n\n    return config(\n      // Modify the set function to call the userlandSet function\n      (...args) => {\n        // Get most up-to-date state. The state from the callback might be a partial state.\n        // The order of the get() and set() calls is important here.\n        const pastState = options?.partialize?.(get()) || get();\n        set(...(args as Parameters<typeof set>));\n        temporalHandleSet(pastState);\n      },\n      get,\n      store,\n    );\n  };\n  return configWithTemporal as StateCreator<TState, [], []>;\n}) as unknown as Zundo;\n\nexport type { ZundoOptions, Zundo, TemporalState };\n", "import type { StateCreator, StoreApi } from 'zustand';\nimport type { _TemporalState, ZundoOptions } from './types';\n\nexport const temporalStateCreator = <TState>(\n  userSet: StoreApi<TState>['setState'],\n  userGet: StoreApi<TState>['getState'],\n  options?: ZundoOptions<TState>,\n) => {\n  const stateCreator: StateCreator<_TemporalState<TState>, [], []> = (\n    set,\n    get,\n  ) => {\n    return {\n      pastStates: options?.pastStates || [],\n      futureStates: options?.futureStates || [],\n      undo: (steps = 1) => {\n        if (get().pastStates.length) {\n          // userGet must be called before userSet\n          const currentState = options?.partialize?.(userGet()) || userGet();\n\n          const statesToApply = get().pastStates.splice(-steps, steps);\n\n          // If there is length, we know that statesToApply is not empty\n          const nextState = statesToApply.shift()!;\n          userSet(nextState);\n          set({\n            pastStates: get().pastStates,\n            futureStates: get().futureStates.concat(\n              options?.diff?.(currentState, nextState) || currentState,\n              statesToApply.reverse(),\n            ),\n          });\n        }\n      },\n      redo: (steps = 1) => {\n        if (get().futureStates.length) {\n          // userGet must be called before userSet\n          const currentState = options?.partialize?.(userGet()) || userGet();\n\n          const statesToApply = get().futureStates.splice(-steps, steps);\n\n          // If there is length, we know that statesToApply is not empty\n          const nextState = statesToApply.shift()!;\n          userSet(nextState);\n          set({\n            pastStates: get().pastStates.concat(\n              options?.diff?.(currentState, nextState) || currentState,\n              statesToApply.reverse(),\n            ),\n            futureStates: get().futureStates,\n          });\n        }\n      },\n      clear: () => set({ pastStates: [], futureStates: [] }),\n      isTracking: true,\n      pause: () => set({ isTracking: false }),\n      resume: () => set({ isTracking: true }),\n      setOnSave: (_onSave) => set({ _onSave }),\n      // Internal properties\n      _onSave: options?.onSave,\n      _handleSet: (pastState, replace, currentState, deltaState) => {\n        // This naively assumes that only one new state can be added at a time\n        if (options?.limit && get().pastStates.length >= options?.limit) {\n          get().pastStates.shift();\n        }\n\n        get()._onSave?.(pastState, currentState);\n        set({\n          pastStates: get().pastStates.concat(deltaState || pastState),\n          futureStates: [],\n        });\n      },\n    };\n  };\n\n  // Cast to a version of the store that does not include \"temporal\" addition\n  return stateCreator as StateCreator<_TemporalState<TState>, [], []>;\n};\n"], "mappings": ";;;;;;;;;ACGO,IAAM,uBAAuB,CAClC,SACA,SACA,YACG;AACH,QAAM,eAA6D,CACjE,KACA,QACG;AACH,WAAO;MACL,aAAY,mCAAS,eAAc,CAAC;MACpC,eAAc,mCAAS,iBAAgB,CAAC;MACxC,MAAM,CAAC,QAAQ,MAAM;;AACnB,YAAI,IAAI,EAAE,WAAW,QAAQ;AAE3B,gBAAM,iBAAe,wCAAS,eAAT,iCAAsB,QAAQ,OAAM,QAAQ;AAEjE,gBAAM,gBAAgB,IAAI,EAAE,WAAW,OAAO,CAAC,OAAO,KAAK;AAG3D,gBAAM,YAAY,cAAc,MAAM;AACtC,kBAAQ,SAAS;AACjB,cAAI;YACF,YAAY,IAAI,EAAE;YAClB,cAAc,IAAI,EAAE,aAAa;gBAC/B,wCAAS,SAAT,iCAAgB,cAAc,eAAc;cAC5C,cAAc,QAAQ;YACxB;UACF,CAAC;QACH;MACF;MACA,MAAM,CAAC,QAAQ,MAAM;;AACnB,YAAI,IAAI,EAAE,aAAa,QAAQ;AAE7B,gBAAM,iBAAe,wCAAS,eAAT,iCAAsB,QAAQ,OAAM,QAAQ;AAEjE,gBAAM,gBAAgB,IAAI,EAAE,aAAa,OAAO,CAAC,OAAO,KAAK;AAG7D,gBAAM,YAAY,cAAc,MAAM;AACtC,kBAAQ,SAAS;AACjB,cAAI;YACF,YAAY,IAAI,EAAE,WAAW;gBAC3B,wCAAS,SAAT,iCAAgB,cAAc,eAAc;cAC5C,cAAc,QAAQ;YACxB;YACA,cAAc,IAAI,EAAE;UACtB,CAAC;QACH;MACF;MACA,OAAO,MAAM,IAAI,EAAE,YAAY,CAAC,GAAG,cAAc,CAAC,EAAE,CAAC;MACrD,YAAY;MACZ,OAAO,MAAM,IAAI,EAAE,YAAY,MAAM,CAAC;MACtC,QAAQ,MAAM,IAAI,EAAE,YAAY,KAAK,CAAC;MACtC,WAAW,CAAC,YAAY,IAAI,EAAE,QAAQ,CAAC;;MAEvC,SAAS,mCAAS;MAClB,YAAY,CAAC,WAAW,SAAS,cAAc,eAAe;;AAE5D,aAAI,mCAAS,UAAS,IAAI,EAAE,WAAW,WAAU,mCAAS,QAAO;AAC/D,cAAI,EAAE,WAAW,MAAM;QACzB;AAEA,wBAAI,GAAE,YAAN,4BAAgB,WAAW;AAC3B,YAAI;UACF,YAAY,IAAI,EAAE,WAAW,OAAO,cAAc,SAAS;UAC3D,cAAc,CAAC;QACjB,CAAC;MACH;IACF;EACF;AAGA,SAAO;AACT;AD1CO,IAAM,WAAY,CACvB,QACA,YACiC;AACjC,QAAM,qBAAqB,CACzB,KACA,KACA,UAIG;;AACH,UAAM,WAAW;QACf,wCAAS,iBAAT,iCAAwB,qBAAqB,KAAK,KAAK,OAAO,OAC5D,qBAAqB,KAAK,KAAK,OAAO;IAC1C;AAEA,UAAM,qBACJ,wCAAS,cAAT;;MACG,MAAM,SAAS,SAAS,EACtB;UACC,MAAM,SAAS,SAAS,EAA6B;AAE7D,UAAM,oBAAoB,CAAC,cAAsB;;AAC/C,UAAI,CAAC,MAAM,SAAS,SAAS,EAAE,WAAY;AAE3C,YAAM,iBAAeA,MAAA,mCAAS,eAAT,gBAAAA,IAAA,cAAsB,IAAI,OAAM,IAAI;AACzD,YAAM,cAAaC,MAAA,mCAAS,SAAT,gBAAAA,IAAA,cAAgB,WAAW;AAC9C;;QAEE;SAGI,eAAe;UAEf,wCAAS,aAAT,iCAAoB,WAAW;QAGnC;AACA;UACE;UACA;UACA;UACA;QACF;MACF;IACF;AAEA,UAAM,WAAW,MAAM;AAEvB,UAAM,WAAW,IAAI,SAAS;;AAG5B,YAAM,cAAYD,MAAA,mCAAS,eAAT,gBAAAA,IAAA,cAAsB,IAAI,OAAM,IAAI;AACtD,eAAS,GAAI,IAAoC;AACjD,wBAAkB,SAAS;IAC7B;AAEA,WAAO;;MAEL,IAAI,SAAS;;AAGX,cAAM,cAAYA,MAAA,mCAAS,eAAT,gBAAAA,IAAA,cAAsB,IAAI,OAAM,IAAI;AACtD,YAAI,GAAI,IAA+B;AACvC,0BAAkB,SAAS;MAC7B;MACA;MACA;IACF;EACF;AACA,SAAO;AACT;", "names": ["_a", "_b"]}