{"Language": "English", "Translation": {"Login Required": "<PERSON><PERSON> Required", "Please login to access settings": "Please login to access settings", "Username": "Username", "Password": "Password", "Login": "<PERSON><PERSON>", "Login Error": "<PERSON><PERSON>", "Incorrect username or password": "Incorrect username or password", "Please enter your username and password": "Please enter your username and password", "Video Script Settings": "**Video Script Settings**", "Video Subject": "Video Subject (Provide a keyword, :red[AI will automatically generate] video script)", "Script Language": "Language for Generating Video Script (AI will automatically output based on the language of your subject)", "Generate Video Script and Keywords": "Click to use AI to generate [Video Script] and [Video Keywords] based on **subject**", "Auto Detect": "Auto Detect", "Video Script": "Video Script (:blue[① Optional, AI generated  ② Proper punctuation helps with subtitle generation])", "Generate Video Keywords": "Click to use AI to generate [Video Keywords] based on **script**", "Please Enter the Video Subject": "Please Enter the Video Script First", "Generating Video Script and Keywords": "AI is generating video script and keywords...", "Generating Video Keywords": "AI is generating video keywords...", "Video Keywords": "Video Keywords (:blue[① Optional, AI generated ② Use **English commas** for separation, English only])", "Video Settings": "**Video Settings**", "Video Concat Mode": "Video Concatenation Mode", "Random": "Random Concatenation (Recommended)", "Sequential": "Sequential Concatenation", "Video Transition Mode": "Video Transition Mode", "None": "None", "Shuffle": "Shuffle", "FadeIn": "FadeIn", "FadeOut": "FadeOut", "SlideIn": "SlideIn", "SlideOut": "SlideOut", "Video Ratio": "Video Aspect Ratio", "Portrait": "Portrait 9:16", "Landscape": "Landscape 16:9", "Clip Duration": "Maximum Duration of Video Clips (seconds)", "Number of Videos Generated Simultaneously": "Number of Videos Generated Simultaneously", "Audio Settings": "**Audio Settings**", "Speech Synthesis": "Speech Synthesis Voice", "Speech Region": "Region(:red[Required，[Get Region](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Key": "API Key(:red[Required，[Get API Key](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Volume": "Speech Volume (1.0 represents 100%)", "Speech Rate": "Speech Rate (1.0 means 1x speed)", "Male": "Male", "Female": "Female", "Background Music": "Background Music", "No Background Music": "No Background Music", "Random Background Music": "Random Background Music", "Custom Background Music": "Custom Background Music", "Custom Background Music File": "Please enter the file path for custom background music:", "Background Music Volume": "Background Music Volume (0.2 represents 20%, background music should not be too loud)", "Subtitle Settings": "**Subtitle Settings**", "Enable Subtitles": "Enable Subtitles (If unchecked, the settings below will not take effect)", "Font": "Subtitle Font", "Position": "Subtitle Position", "Top": "Top", "Center": "Center", "Bottom": "Bottom (Recommended)", "Custom": "Custom position (70, indicating 70% down from the top)", "Font Size": "Subtitle Font Size", "Font Color": "Subtitle Font Color", "Stroke Color": "Subtitle Outline Color", "Stroke Width": "Subtitle Outline Width", "Generate Video": "Generate Video", "Video Script and Subject Cannot Both Be Empty": "Video Subject and Video Script cannot both be empty", "Generating Video": "Generating video, please wait...", "Start Generating Video": "Start Generating Video", "Video Generation Completed": "Video Generation Completed", "Video Generation Failed": "Video Generation Failed", "You can download the generated video from the following links": "You can download the generated video from the following links", "Pexels API Key": "Pexels API Key ([Get API Key](https://www.pexels.com/api/))", "Pixabay API Key": "Pixabay API Key ([Get API Key](https://pixabay.com/api/docs/#api_search_videos))", "Basic Settings": "**Basic Settings** (:blue[Click to expand])", "Language": "Language", "LLM Provider": "LLM Provider", "API Key": "API Key (:red[Required])", "Base Url": "Base Url", "Account ID": "Account ID (Get from Cloudflare dashboard)", "Model Name": "Model Name", "Please Enter the LLM API Key": "Please Enter the **LLM API Key**", "Please Enter the Pexels API Key": "Please Enter the **Pexels API Key**", "Please Enter the Pixabay API Key": "Please Enter the **Pixabay API Key**", "Get Help": "If you need help, or have any questions, you can join discord for help: https://harryai.cc", "Video Source": "Video Source", "TikTok": "TikTok (TikTok support is coming soon)", "Bilibili": "Bilibili (Bilibili support is coming soon)", "Xiaohongshu": "Xiaohongshu (Xiaohongshu support is coming soon)", "Local file": "Local file", "Play Voice": "Play Voice", "Voice Example": "This is an example text for testing speech synthesis", "Synthesizing Voice": "Synthesizing voice, please wait...", "TTS Provider": "Select the voice synthesis provider", "TTS Servers": "TTS Servers", "No voices available for the selected TTS server. Please select another server.": "No voices available for the selected TTS server. Please select another server.", "SiliconFlow API Key": "SiliconFlow API Key [Click to get](https://cloud.siliconflow.cn/account/ak)", "SiliconFlow TTS Settings": "SiliconFlow TTS Settings", "Speed: Range [0.25, 4.0], default is 1.0": "Speed: Range [0.25, 4.0], default is 1.0", "Volume: Uses Speech Volume setting, default 1.0 maps to gain 0": "Volume: Uses Speech Volume setting, default 1.0 maps to gain 0", "Hide Log": "<PERSON>de Log", "Hide Basic Settings": "Hide Basic Settings\n\nHidden, the basic settings panel will not be displayed on the page.\n\nIf you need to display it again, please set `hide_config = false` in `config.toml`", "LLM Settings": "**<PERSON><PERSON>**", "Video Source Settings": "**Video Source Settings**"}}