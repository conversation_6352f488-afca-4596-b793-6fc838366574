import {
  require_dayjs_min
} from "./chunk-M2A4HNR4.js";
import {
  __commonJS
} from "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/km.js
var require_km = __commonJS({
  "node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/km.js"(exports, module) {
    !function(_, e) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = e(require_dayjs_min()) : "function" == typeof define && define.amd ? define(["dayjs"], e) : (_ = "undefined" != typeof globalThis ? globalThis : _ || self).dayjs_locale_km = e(_.dayjs);
    }(exports, function(_) {
      "use strict";
      function e(_2) {
        return _2 && "object" == typeof _2 && "default" in _2 ? _2 : { default: _2 };
      }
      var t = e(_), d = { name: "km", weekdays: "អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍".split("_"), months: "មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"), weekStart: 1, weekdaysShort: "អា_ច_អ_ព_ព្រ_សុ_ស".split("_"), monthsShort: "មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"), weekdaysMin: "អា_ច_អ_ព_ព្រ_សុ_ស".split("_"), ordinal: function(_2) {
        return _2;
      }, formats: { LT: "HH:mm", LTS: "HH:mm:ss", L: "DD/MM/YYYY", LL: "D MMMM YYYY", LLL: "D MMMM YYYY HH:mm", LLLL: "dddd, D MMMM YYYY HH:mm" }, relativeTime: { future: "%sទៀត", past: "%sមុន", s: "ប៉ុន្មានវិនាទី", m: "មួយនាទី", mm: "%d នាទី", h: "មួយម៉ោង", hh: "%d ម៉ោង", d: "មួយថ្ងៃ", dd: "%d ថ្ងៃ", M: "មួយខែ", MM: "%d ខែ", y: "មួយឆ្នាំ", yy: "%d ឆ្នាំ" } };
      return t.default.locale(d, null, true), d;
    });
  }
});
export default require_km();
//# sourceMappingURL=dayjs_locale_km.js.map
