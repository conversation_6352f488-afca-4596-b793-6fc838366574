{"version": 3, "sources": ["../../../.pnpm/fuzzy@0.1.3/node_modules/fuzzy/lib/fuzzy.js"], "sourcesContent": ["/*\n * Fuzzy\n * https://github.com/myork/fuzzy\n *\n * Copyright (c) 2012 <PERSON>\n * Licensed under the MIT license.\n */\n\n(function() {\n\nvar root = this;\n\nvar fuzzy = {};\n\n// Use in node or in browser\nif (typeof exports !== 'undefined') {\n  module.exports = fuzzy;\n} else {\n  root.fuzzy = fuzzy;\n}\n\n// Return all elements of `array` that have a fuzzy\n// match against `pattern`.\nfuzzy.simpleFilter = function(pattern, array) {\n  return array.filter(function(str) {\n    return fuzzy.test(pattern, str);\n  });\n};\n\n// Does `pattern` fuzzy match `str`?\nfuzzy.test = function(pattern, str) {\n  return fuzzy.match(pattern, str) !== null;\n};\n\n// If `pattern` matches `str`, wrap each matching character\n// in `opts.pre` and `opts.post`. If no match, return null\nfuzzy.match = function(pattern, str, opts) {\n  opts = opts || {};\n  var patternIdx = 0\n    , result = []\n    , len = str.length\n    , totalScore = 0\n    , currScore = 0\n    // prefix\n    , pre = opts.pre || ''\n    // suffix\n    , post = opts.post || ''\n    // String to compare against. This might be a lowercase version of the\n    // raw string\n    , compareString =  opts.caseSensitive && str || str.toLowerCase()\n    , ch;\n\n  pattern = opts.caseSensitive && pattern || pattern.toLowerCase();\n\n  // For each character in the string, either add it to the result\n  // or wrap in template if it's the next string in the pattern\n  for(var idx = 0; idx < len; idx++) {\n    ch = str[idx];\n    if(compareString[idx] === pattern[patternIdx]) {\n      ch = pre + ch + post;\n      patternIdx += 1;\n\n      // consecutive characters should increase the score more than linearly\n      currScore += 1 + currScore;\n    } else {\n      currScore = 0;\n    }\n    totalScore += currScore;\n    result[result.length] = ch;\n  }\n\n  // return rendered string if we have a match for every char\n  if(patternIdx === pattern.length) {\n    // if the string is an exact match with pattern, totalScore should be maxed\n    totalScore = (compareString === pattern) ? Infinity : totalScore;\n    return {rendered: result.join(''), score: totalScore};\n  }\n\n  return null;\n};\n\n// The normal entry point. Filters `arr` for matches against `pattern`.\n// It returns an array with matching values of the type:\n//\n//     [{\n//         string:   '<b>lah' // The rendered string\n//       , index:    2        // The index of the element in `arr`\n//       , original: 'blah'   // The original element in `arr`\n//     }]\n//\n// `opts` is an optional argument bag. Details:\n//\n//    opts = {\n//        // string to put before a matching character\n//        pre:     '<b>'\n//\n//        // string to put after matching character\n//      , post:    '</b>'\n//\n//        // Optional function. Input is an entry in the given arr`,\n//        // output should be the string to test `pattern` against.\n//        // In this example, if `arr = [{crying: 'koala'}]` we would return\n//        // 'koala'.\n//      , extract: function(arg) { return arg.crying; }\n//    }\nfuzzy.filter = function(pattern, arr, opts) {\n  if(!arr || arr.length === 0) {\n    return [];\n  }\n  if (typeof pattern !== 'string') {\n    return arr;\n  }\n  opts = opts || {};\n  return arr\n    .reduce(function(prev, element, idx, arr) {\n      var str = element;\n      if(opts.extract) {\n        str = opts.extract(element);\n      }\n      var rendered = fuzzy.match(pattern, str, opts);\n      if(rendered != null) {\n        prev[prev.length] = {\n            string: rendered.rendered\n          , score: rendered.score\n          , index: idx\n          , original: element\n        };\n      }\n      return prev;\n    }, [])\n\n    // Sort by score. Browsers are inconsistent wrt stable/unstable\n    // sorting, so force stable by using the index in the case of tie.\n    // See http://ofb.net/~sethml/is-sort-stable.html\n    .sort(function(a,b) {\n      var compare = b.score - a.score;\n      if(compare) return compare;\n      return a.index - b.index;\n    });\n};\n\n\n}());\n\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,KAAC,WAAW;AAEZ,UAAI,OAAO;AAEX,UAAI,QAAQ,CAAC;AAGb,UAAI,OAAO,YAAY,aAAa;AAClC,eAAO,UAAU;AAAA,MACnB,OAAO;AACL,aAAK,QAAQ;AAAA,MACf;AAIA,YAAM,eAAe,SAAS,SAAS,OAAO;AAC5C,eAAO,MAAM,OAAO,SAAS,KAAK;AAChC,iBAAO,MAAM,KAAK,SAAS,GAAG;AAAA,QAChC,CAAC;AAAA,MACH;AAGA,YAAM,OAAO,SAAS,SAAS,KAAK;AAClC,eAAO,MAAM,MAAM,SAAS,GAAG,MAAM;AAAA,MACvC;AAIA,YAAM,QAAQ,SAAS,SAAS,KAAK,MAAM;AACzC,eAAO,QAAQ,CAAC;AAChB,YAAI,aAAa,GACb,SAAS,CAAC,GACV,MAAM,IAAI,QACV,aAAa,GACb,YAAY,GAEZ,MAAM,KAAK,OAAO,IAElB,OAAO,KAAK,QAAQ,IAGpB,gBAAiB,KAAK,iBAAiB,OAAO,IAAI,YAAY,GAC9D;AAEJ,kBAAU,KAAK,iBAAiB,WAAW,QAAQ,YAAY;AAI/D,iBAAQ,MAAM,GAAG,MAAM,KAAK,OAAO;AACjC,eAAK,IAAI,GAAG;AACZ,cAAG,cAAc,GAAG,MAAM,QAAQ,UAAU,GAAG;AAC7C,iBAAK,MAAM,KAAK;AAChB,0BAAc;AAGd,yBAAa,IAAI;AAAA,UACnB,OAAO;AACL,wBAAY;AAAA,UACd;AACA,wBAAc;AACd,iBAAO,OAAO,MAAM,IAAI;AAAA,QAC1B;AAGA,YAAG,eAAe,QAAQ,QAAQ;AAEhC,uBAAc,kBAAkB,UAAW,WAAW;AACtD,iBAAO,EAAC,UAAU,OAAO,KAAK,EAAE,GAAG,OAAO,WAAU;AAAA,QACtD;AAEA,eAAO;AAAA,MACT;AA0BA,YAAM,SAAS,SAAS,SAAS,KAAK,MAAM;AAC1C,YAAG,CAAC,OAAO,IAAI,WAAW,GAAG;AAC3B,iBAAO,CAAC;AAAA,QACV;AACA,YAAI,OAAO,YAAY,UAAU;AAC/B,iBAAO;AAAA,QACT;AACA,eAAO,QAAQ,CAAC;AAChB,eAAO,IACJ,OAAO,SAAS,MAAM,SAAS,KAAKA,MAAK;AACxC,cAAI,MAAM;AACV,cAAG,KAAK,SAAS;AACf,kBAAM,KAAK,QAAQ,OAAO;AAAA,UAC5B;AACA,cAAI,WAAW,MAAM,MAAM,SAAS,KAAK,IAAI;AAC7C,cAAG,YAAY,MAAM;AACnB,iBAAK,KAAK,MAAM,IAAI;AAAA,cAChB,QAAQ,SAAS;AAAA,cACjB,OAAO,SAAS;AAAA,cAChB,OAAO;AAAA,cACP,UAAU;AAAA,YACd;AAAA,UACF;AACA,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC,EAKJ,KAAK,SAAS,GAAE,GAAG;AAClB,cAAI,UAAU,EAAE,QAAQ,EAAE;AAC1B,cAAG,QAAS,QAAO;AACnB,iBAAO,EAAE,QAAQ,EAAE;AAAA,QACrB,CAAC;AAAA,MACL;AAAA,IAGA,GAAE;AAAA;AAAA;", "names": ["arr"]}