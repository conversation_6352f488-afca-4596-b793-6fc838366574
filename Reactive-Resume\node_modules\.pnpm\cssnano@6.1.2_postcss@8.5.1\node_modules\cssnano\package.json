{"name": "cssnano", "version": "6.1.2", "description": "A modular minifier, built on top of the PostCSS ecosystem.", "main": "src/index.js", "types": "types/index.d.ts", "funding": {"type": "opencollective", "url": "https://opencollective.com/cssnano"}, "keywords": ["css", "compress", "minify", "optimise", "optimisation", "postcss", "postcss-plugin"], "license": "MIT", "dependencies": {"lilconfig": "^3.1.1", "cssnano-preset-default": "^6.1.2"}, "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "files": ["src", "LICENSE-MIT", "types"], "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "devDependencies": {"autoprefixer": "^10.4.19", "postcss": "^8.4.38", "cssnano-preset-advanced": "^6.1.2", "cssnano-preset-lite": "^3.1.0"}, "peerDependencies": {"postcss": "^8.4.31"}}