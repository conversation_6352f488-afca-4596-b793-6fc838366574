{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/el.js"], "sourcesContent": ["!function(e,_){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=_(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],_):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_el=_(e.dayjs)}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),d={name:\"el\",weekdays:\"Κυριακή_Δευτέρα_Τρίτη_Τετάρτη_Πέμπτη_Παρασκευή_Σάββατο\".split(\"_\"),weekdaysShort:\"Κυρ_Δευ_Τρι_Τετ_Πεμ_Παρ_Σαβ\".split(\"_\"),weekdaysMin:\"Κυ_Δε_Τρ_Τε_Πε_Πα_Σα\".split(\"_\"),months:\"Ιανουάριος_Φεβρουάριος_Μάρτιος_Απρίλιος_Μάιος_Ιούνιος_Ιούλιος_Αύγουστος_Σεπτέμβριος_Οκτώβριος_Νοέμβριος_Δεκέμβριος\".split(\"_\"),monthsShort:\"Ιαν_Φεβ_Μαρ_Απρ_Μαι_Ιουν_Ιουλ_Αυγ_Σεπτ_Οκτ_Νοε_Δεκ\".split(\"_\"),ordinal:function(e){return e},weekStart:1,relativeTime:{future:\"σε %s\",past:\"πριν %s\",s:\"μερικά δευτερόλεπτα\",m:\"ένα λεπτό\",mm:\"%d λεπτά\",h:\"μία ώρα\",hh:\"%d ώρες\",d:\"μία μέρα\",dd:\"%d μέρες\",M:\"ένα μήνα\",MM:\"%d μήνες\",y:\"ένα χρόνο\",yy:\"%d χρόνια\"},formats:{LT:\"h:mm A\",LTS:\"h:mm:ss A\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY h:mm A\",LLLL:\"dddd, D MMMM YYYY h:mm A\"}};return t.default.locale(d,null,!0),d}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,yDAAyD,MAAM,GAAG,GAAE,eAAc,8BAA8B,MAAM,GAAG,GAAE,aAAY,uBAAuB,MAAM,GAAG,GAAE,QAAO,qHAAqH,MAAM,GAAG,GAAE,aAAY,qDAAqD,MAAM,GAAG,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA;AAAA,MAAC,GAAE,WAAU,GAAE,cAAa,EAAC,QAAO,SAAQ,MAAK,WAAU,GAAE,uBAAsB,GAAE,aAAY,IAAG,YAAW,GAAE,WAAU,IAAG,WAAU,GAAE,YAAW,IAAG,YAAW,GAAE,YAAW,IAAG,YAAW,GAAE,aAAY,IAAG,YAAW,GAAE,SAAQ,EAAC,IAAG,UAAS,KAAI,aAAY,GAAE,cAAa,IAAG,eAAc,KAAI,sBAAqB,MAAK,2BAA0B,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e"]}