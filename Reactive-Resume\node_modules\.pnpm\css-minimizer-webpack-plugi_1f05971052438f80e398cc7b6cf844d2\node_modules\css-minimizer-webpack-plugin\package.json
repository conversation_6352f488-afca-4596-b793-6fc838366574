{"name": "css-minimizer-webpack-plugin", "version": "5.0.1", "description": "CSS minimizer (minifier) plugin for Webpack", "license": "MIT", "repository": "webpack-contrib/css-minimizer-webpack-plugin", "author": "<PERSON><PERSON>", "homepage": "https://github.com/webpack-contrib/css-minimizer-webpack-plugin", "bugs": "https://github.com/webpack-contrib/css-minimizer-webpack-plugin/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "types/index.d.ts", "engines": {"node": ">= 14.15.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean types", "build:types": "tsc --declaration --emitDeclarationOnly --outDir types && prettier \"types/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint:spelling": "cspell \"**/*.*\"", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "files": ["dist", "types"], "peerDependencies": {"webpack": "^5.0.0"}, "peerDependenciesMeta": {"clean-css": {"optional": true}, "csso": {"optional": true}, "esbuild": {"optional": true}, "@parcel/css": {"optional": true}, "lightningcss": {"optional": true}, "@swc/css": {"optional": true}}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.18", "cssnano": "^6.0.1", "jest-worker": "^29.4.3", "postcss": "^8.4.24", "schema-utils": "^4.0.1", "serialize-javascript": "^6.0.1"}, "devDependencies": {"@babel/cli": "^7.21.5", "@babel/core": "^7.22.1", "@babel/preset-env": "^7.22.4", "@commitlint/cli": "^17.6.5", "@commitlint/config-conventional": "^17.6.5", "@parcel/css": "^1.8.3", "@swc/css": "^0.0.20", "@types/clean-css": "^4.2.5", "@types/csso": "^5.0.0", "@types/node": "^18.16.3", "@types/serialize-javascript": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.4.3", "clean-css": "^5.3.2", "copy-webpack-plugin": "^9.1.0", "cross-env": "^7.0.3", "cspell": "^6.27.0", "css-loader": "^6.8.1", "cssnano-preset-simple": "^4.0.0", "csso": "^5.0.3", "del": "^6.1.0", "del-cli": "^5.0.0", "esbuild": "^0.17.19", "eslint": "^8.41.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "husky": "^8.0.3", "jest": "^29.4.3", "lightningcss": "^1.20.0", "lint-staged": "^13.2.2", "memfs": "^3.5.1", "mini-css-extract-plugin": "^2.7.6", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "sass": "^1.62.1", "sass-loader": "^13.3.1", "source-map": "^0.6.1", "standard-version": "^9.5.0", "sugarss": "^4.0.1", "typescript": "^5.0.4", "webpack": "^5.85.0"}, "keywords": ["cssnano", "css", "csso", "clean-css", "swc", "esbuild", "webpack", "webpack-plugin", "minimize", "minimizer", "minify", "minifier", "optimize", "optimizer"]}