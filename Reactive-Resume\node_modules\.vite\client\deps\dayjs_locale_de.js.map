{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/de.js"], "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_de=n(e.dayjs)}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=n(e),a={s:\"ein paar Sekunden\",m:[\"eine Minute\",\"einer Minute\"],mm:\"%d Minuten\",h:[\"eine Stunde\",\"einer Stunde\"],hh:\"%d Stunden\",d:[\"ein Tag\",\"einem Tag\"],dd:[\"%d Tage\",\"%d Tagen\"],M:[\"ein Monat\",\"einem Monat\"],MM:[\"%d <PERSON><PERSON>\",\"%d <PERSON><PERSON>\"],y:[\"ein <PERSON>ah<PERSON>\",\"einem Jahr\"],yy:[\"%d Jahre\",\"%d Jahren\"]};function i(e,n,t){var i=a[t];return Array.isArray(i)&&(i=i[n?0:1]),i.replace(\"%d\",e)}var r={name:\"de\",weekdays:\"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag\".split(\"_\"),weekdaysShort:\"So._Mo._Di._Mi._Do._Fr._Sa.\".split(\"_\"),weekdaysMin:\"So_Mo_Di_Mi_Do_Fr_Sa\".split(\"_\"),months:\"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember\".split(\"_\"),monthsShort:\"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sept._Okt._Nov._Dez.\".split(\"_\"),ordinal:function(e){return e+\".\"},weekStart:1,yearStart:4,formats:{LTS:\"HH:mm:ss\",LT:\"HH:mm\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY HH:mm\",LLLL:\"dddd, D. MMMM YYYY HH:mm\"},relativeTime:{future:\"in %s\",past:\"vor %s\",s:i,m:i,mm:i,h:i,hh:i,d:i,dd:i,M:i,MM:i,y:i,yy:i}};return t.default.locale(r,null,!0),r}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,GAAE,qBAAoB,GAAE,CAAC,eAAc,cAAc,GAAE,IAAG,cAAa,GAAE,CAAC,eAAc,cAAc,GAAE,IAAG,cAAa,GAAE,CAAC,WAAU,WAAW,GAAE,IAAG,CAAC,WAAU,UAAU,GAAE,GAAE,CAAC,aAAY,aAAa,GAAE,IAAG,CAAC,aAAY,YAAY,GAAE,GAAE,CAAC,YAAW,YAAY,GAAE,IAAG,CAAC,YAAW,WAAW,EAAC;AAAE,eAAS,EAAEA,IAAEC,IAAEC,IAAE;AAAC,YAAIC,KAAE,EAAED,EAAC;AAAE,eAAO,MAAM,QAAQC,EAAC,MAAIA,KAAEA,GAAEF,KAAE,IAAE,CAAC,IAAGE,GAAE,QAAQ,MAAKH,EAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAC,MAAK,MAAK,UAAS,8DAA8D,MAAM,GAAG,GAAE,eAAc,8BAA8B,MAAM,GAAG,GAAE,aAAY,uBAAuB,MAAM,GAAG,GAAE,QAAO,qFAAqF,MAAM,GAAG,GAAE,aAAY,8DAA8D,MAAM,GAAG,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA,KAAE;AAAA,MAAG,GAAE,WAAU,GAAE,WAAU,GAAE,SAAQ,EAAC,KAAI,YAAW,IAAG,SAAQ,GAAE,cAAa,IAAG,gBAAe,KAAI,sBAAqB,MAAK,2BAA0B,GAAE,cAAa,EAAC,QAAO,SAAQ,MAAK,UAAS,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,IAAG,EAAC,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["e", "n", "t", "i"]}