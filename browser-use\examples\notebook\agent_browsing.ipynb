{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {"id": "ZRGlUb8O4fPV"}, "outputs": [], "source": ["%pip install -U langgraph langchain_google_genai langchain_community langgraph-checkpoint-postgres  openai langchain_groq"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "cMfPUmHIxqTi"}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --upgrade --quiet  playwright > /dev/null\n", "%pip install --upgrade --quiet  lxml browser-use langchain_openai"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kkZ7jVUOUV7Q"}, "outputs": [], "source": ["!playwright install"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-_T1MhnGUl2q"}, "outputs": [], "source": ["!pip install \"anyio<4\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yARYirp1UhDR"}, "outputs": [], "source": ["# This import is required only for jupyter notebooks, since they have their own eventloop\n", "import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "jyVP10O_5Qck"}, "outputs": [], "source": ["from google.colab import userdata\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model='gpt-4o-mini', temperature=0, api_key=userdata.get('Open_api_key'))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "e9duizdv5cOH", "outputId": "a07b1702-d485-4641-c307-601e6ab34b9b"}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello! How can I assist you today?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 10, 'prompt_tokens': 8, 'total_tokens': 18, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_bd83329f63', 'finish_reason': 'stop', 'logprobs': None}, id='run-28a9088f-7539-412a-aa80-1663be40e74f-0', usage_metadata={'input_tokens': 8, 'output_tokens': 10, 'total_tokens': 18, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["llm.invoke('hi')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "wS8ouhiVQ2dL", "outputId": "653879a8-b3ac-4178-edee-5cd834e3404a"}, "outputs": [], "source": ["import asyncio\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "from browser_use import Agent\n", "from browser_use.browser import BrowserProfile, BrowserSession\n", "\n", "# Basic configuration for the browser\n", "browser_profile = BrowserProfile(\n", "\theadless=True,  # Run in headless mode\n", "\t# disable_security=True  # Uncomment if you want to disable security\n", ")\n", "\n", "# Initialize the browser session with the specified profile\n", "browser_session = BrowserSession(browser_profile=browser_profile)\n", "\n", "\n", "async def main():\n", "\t# Initialize the agent with the task and language model\n", "\tagent = Agent(\n", "\t\ttask='What is <PERSON><PERSON>',\n", "\t\tllm=llm,  # Replace with your LLM configuration\n", "\t\tbrowser_session=browser_session,\n", "\t\tgenerate_gif=False,  # Disable GIF generation\n", "\t)\n", "\n", "\t# Run the agent and get results asynchronously\n", "\tresult = await agent.run()\n", "\n", "\t# Process results token-wise\n", "\tfor action in result.action_results():\n", "\t\tprint(action.extracted_content, end='\\r', flush=True)\n", "\t\tprint('\\n\\n')\n", "\t\t# if action.is_done:\n", "\t\t#     print(action.extracted_content)\n", "\n", "\t# Close the browser after completion\n", "\tawait browser_session.close()\n", "\n", "\n", "# Run the asynchronous main function\n", "asyncio.run(main())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TFK-fNoLDFcF", "outputId": "d78fbeae-c8f0-4c26-e0e3-7a0a683d3fc1"}, "outputs": [], "source": ["# from browser_use import Agent\n", "import asyncio\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "from browser_use.browser import BrowserProfile, BrowserSession\n", "\n", "# Basic configuration\n", "browser_profile = BrowserProfile(\n", "\theadless=True,\n", "\t# disable_security=True\n", ")\n", "# Reuse existing browser\n", "browser_session = BrowserSession(browser_profile=browser_profile)\n", "# async def main():\n", "agent = Agent(\n", "\ttask='what is langchain',\n", "\tllm=llm,\n", "\tbrowser_session=browser_session,\n", "\tgenerate_gif=False,  # Browser instance will be reused\n", ")\n", "\n", "result = await agent.run()\n", "print(result)\n", "# Manually close the browser\n", "# asyncio.run(main())\n", "await browser_session.close()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nKGC936xODry", "outputId": "de70d715-c30a-4d5b-9d25-40bd79d410de"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LangChain is a composable framework designed for building applications with large language models (LLMs). It simplifies the integration of language models with external data sources and is open-source, supported by an active community. LangChain provides tools for developers to streamline the application lifecycle of LLMs.\n"]}], "source": ["# display(result.action_results())\n", "for action in result.action_results():\n", "\tif action.is_done:\n", "\t\tprint(action.extracted_content)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}