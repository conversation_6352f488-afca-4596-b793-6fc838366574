{
  "extends": ["plugin:@nx/react", "../../.eslintrc.json"],
  "ignorePatterns": ["!**/*"],
  "overrides": [
    {
      "files": ["*.ts", "*.tsx", "*.js", "*.jsx"],
      "extends": ["plugin:tailwindcss/recommended"],
      "settings": {
        "tailwindcss": {
          "callees": ["cn", "clsx", "cva"],
          "config": "tailwind.config.js"
        }
      },
      "rules": {
        // eslint
        "@typescript-eslint/no-require-imports": "off",

        // react
        "react/no-unescaped-entities": "off",
        "react/jsx-sort-props": [
          "error",
          {
            "reservedFirst": true,
            "callbacksLast": true,
            "shorthandFirst": true,
            "noSortAlphabetically": true
          }
        ]
      }
    },
    {
      "files": ["*.ts", "*.tsx"],
      "rules": {}
    },
    {
      "files": ["*.js", "*.jsx"],
      "rules": {}
    }
  ]
}
