@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@lingui+cli@4.14.1_typescript@5.7.3\node_modules\@lingui\cli\dist\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@lingui+cli@4.14.1_typescript@5.7.3\node_modules\@lingui\cli\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@lingui+cli@4.14.1_typescript@5.7.3\node_modules\@lingui\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@lingui+cli@4.14.1_typescript@5.7.3\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@lingui+cli@4.14.1_typescript@5.7.3\node_modules\@lingui\cli\dist\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@lingui+cli@4.14.1_typescript@5.7.3\node_modules\@lingui\cli\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@lingui+cli@4.14.1_typescript@5.7.3\node_modules\@lingui\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\@lingui+cli@4.14.1_typescript@5.7.3\node_modules;C:\Users\<USER>\Documents\GitHubRepositories\Reactive-Resume\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\@lingui+cli@4.14.1_typescript@5.7.3\node_modules\@lingui\cli\dist\lingui.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\@lingui+cli@4.14.1_typescript@5.7.3\node_modules\@lingui\cli\dist\lingui.js" %*
)
