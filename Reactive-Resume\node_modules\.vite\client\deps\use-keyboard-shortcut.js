import {
  require_react
} from "./chunk-4ALVB3Y3.js";
import {
  __toESM
} from "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/use-keyboard-shortcut@1.1.6_58fb67bdbd06d513d87d5e86e1e63075/node_modules/use-keyboard-shortcut/lib/useKeyboardShortcut.js
var import_react = __toESM(require_react());

// node_modules/.pnpm/use-keyboard-shortcut@1.1.6_58fb67bdbd06d513d87d5e86e1e63075/node_modules/use-keyboard-shortcut/lib/utils.js
var overrideSystemHandling = (e) => {
  if (e) {
    if (e.preventDefault) e.preventDefault();
    if (e.stopPropagation) {
      e.stopPropagation();
    } else if (window.event) {
      window.event.cancelBubble = true;
    }
  }
};
var uniq_fast = (a) => {
  var seen = {};
  var out = [];
  var len = a.length;
  var j = 0;
  for (var i = 0; i < len; i++) {
    var item = a[i];
    if (seen[item] !== 1) {
      seen[item] = 1;
      out[j++] = item;
    }
  }
  return out;
};
var checkHeldKeysRecursive = (shortcutKey, shortcutKeyRecursionIndex = 0, shortcutArray, heldKeysArray) => {
  const shortcutIndexOfKey = shortcutArray.indexOf(shortcutKey);
  const keyPartOfShortCut = shortcutArray.indexOf(shortcutKey) >= 0;
  if (!keyPartOfShortCut) return false;
  const comparisonIndex = Math.max(heldKeysArray.length - 1, 0);
  if (heldKeysArray.length && heldKeysArray[comparisonIndex] !== shortcutArray[comparisonIndex]) {
    return false;
  }
  if (shortcutIndexOfKey === 0) {
    if (shortcutKeyRecursionIndex > 0)
      return heldKeysArray.indexOf(shortcutKey) >= 0;
    return true;
  }
  const previousShortcutKeyIndex = shortcutIndexOfKey - 1;
  const previousShortcutKey = shortcutArray[previousShortcutKeyIndex];
  const previousShortcutKeyHeld = heldKeysArray[previousShortcutKeyIndex] === previousShortcutKey;
  if (!previousShortcutKeyHeld) return false;
  return checkHeldKeysRecursive(
    previousShortcutKey,
    shortcutIndexOfKey,
    shortcutArray,
    heldKeysArray
  );
};

// node_modules/.pnpm/use-keyboard-shortcut@1.1.6_58fb67bdbd06d513d87d5e86e1e63075/node_modules/use-keyboard-shortcut/lib/useKeyboardShortcut.js
var BLACKLISTED_DOM_TARGETS = ["TEXTAREA", "INPUT"];
var DEFAULT_OPTIONS = {
  overrideSystem: false,
  ignoreInputFields: true,
  repeatOnHold: true
};
var useKeyboardShortcut = (shortcutKeys, callback, userOptions) => {
  const options = { ...DEFAULT_OPTIONS, ...userOptions };
  if (!Array.isArray(shortcutKeys))
    throw new Error(
      "The first parameter to `useKeyboardShortcut` must be an ordered array of `KeyboardEvent.key` strings."
    );
  if (!shortcutKeys.length)
    throw new Error(
      "The first parameter to `useKeyboardShortcut` must contain atleast one `KeyboardEvent.key` string."
    );
  if (!callback || typeof callback !== "function")
    throw new Error(
      "The second parameter to `useKeyboardShortcut` must be a function that will be envoked when the keys are pressed."
    );
  const shortcutKeysId = (0, import_react.useMemo)(() => shortcutKeys.join(), [shortcutKeys]);
  const shortcutArray = (0, import_react.useMemo)(
    () => uniq_fast(shortcutKeys).map((key) => String(key).toLowerCase()),
    // While using .join() is bad for most larger objects, this shortcut
    // array is fine as it's small, according to the answer below.
    // https://github.com/facebook/react/issues/14476#issuecomment-471199055
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [shortcutKeysId]
  );
  const heldKeys = (0, import_react.useRef)([]);
  const keydownListener = (0, import_react.useCallback)(
    (keydownEvent) => {
      const loweredKey = String(keydownEvent.key).toLowerCase();
      if (!(shortcutArray.indexOf(loweredKey) >= 0)) return;
      if (options.ignoreInputFields && BLACKLISTED_DOM_TARGETS.indexOf(keydownEvent.target.tagName) >= 0) {
        return;
      }
      if (keydownEvent.repeat && !options.repeatOnHold) return;
      if (options.overrideSystem) {
        overrideSystemHandling(keydownEvent);
      }
      const isHeldKeyCombinationValid = checkHeldKeysRecursive(
        loweredKey,
        null,
        shortcutArray,
        heldKeys.current
      );
      if (!isHeldKeyCombinationValid) {
        return;
      }
      const nextHeldKeys = [...heldKeys.current, loweredKey];
      if (nextHeldKeys.join() === shortcutArray.join()) {
        callback(shortcutKeys);
        return false;
      }
      heldKeys.current = nextHeldKeys;
      return false;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      shortcutKeysId,
      callback,
      options.overrideSystem,
      options.ignoreInputFields
    ]
  );
  const keyupListener = (0, import_react.useCallback)(
    (keyupEvent) => {
      const raisedKey = String(keyupEvent.key).toLowerCase();
      if (!(shortcutArray.indexOf(raisedKey) >= 0)) return;
      const raisedKeyHeldIndex = heldKeys.current.indexOf(raisedKey);
      if (!(raisedKeyHeldIndex >= 0)) return;
      let nextHeldKeys = [];
      let loopIndex;
      for (loopIndex = 0; loopIndex < heldKeys.current.length; ++loopIndex) {
        if (loopIndex !== raisedKeyHeldIndex) {
          nextHeldKeys.push(heldKeys.current[loopIndex]);
        }
      }
      heldKeys.current = nextHeldKeys;
      return false;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [shortcutKeysId]
  );
  const flushHeldKeys = (0, import_react.useCallback)(() => {
    heldKeys.current = [];
  }, []);
  (0, import_react.useEffect)(() => {
    window.addEventListener("keydown", keydownListener);
    window.addEventListener("keyup", keyupListener);
    return () => {
      window.removeEventListener("keydown", keydownListener);
      window.removeEventListener("keyup", keyupListener);
    };
  }, [keydownListener, keyupListener, shortcutKeysId]);
  (0, import_react.useEffect)(() => {
    flushHeldKeys();
  }, [shortcutKeysId, flushHeldKeys]);
  return {
    flushHeldKeys
  };
};
var useKeyboardShortcut_default = useKeyboardShortcut;

// node_modules/.pnpm/use-keyboard-shortcut@1.1.6_58fb67bdbd06d513d87d5e86e1e63075/node_modules/use-keyboard-shortcut/index.js
var use_keyboard_shortcut_default = useKeyboardShortcut_default;
export {
  use_keyboard_shortcut_default as default
};
//# sourceMappingURL=use-keyboard-shortcut.js.map
