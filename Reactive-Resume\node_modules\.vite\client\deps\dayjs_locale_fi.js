import {
  require_dayjs_min
} from "./chunk-M2A4HNR4.js";
import {
  __commonJS
} from "./chunk-SNAQBZPT.js";

// node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/fi.js
var require_fi = __commonJS({
  "node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/fi.js"(exports, module) {
    !function(u, e) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = e(require_dayjs_min()) : "function" == typeof define && define.amd ? define(["dayjs"], e) : (u = "undefined" != typeof globalThis ? globalThis : u || self).dayjs_locale_fi = e(u.dayjs);
    }(exports, function(u) {
      "use strict";
      function e(u2) {
        return u2 && "object" == typeof u2 && "default" in u2 ? u2 : { default: u2 };
      }
      var t = e(u);
      function n(u2, e2, t2, n2) {
        var i2 = { s: "muutama sekunti", m: "minuutti", mm: "%d minuuttia", h: "tunti", hh: "%d tuntia", d: "päivä", dd: "%d päivää", M: "kuukausi", MM: "%d kuukautta", y: "vuosi", yy: "%d vuotta", numbers: "nolla_yksi_kaksi_kolme_neljä_viisi_kuusi_seitsemän_kahdeksan_yhdeksän".split("_") }, a = { s: "muutaman sekunnin", m: "minuutin", mm: "%d minuutin", h: "tunnin", hh: "%d tunnin", d: "päivän", dd: "%d päivän", M: "kuukauden", MM: "%d kuukauden", y: "vuoden", yy: "%d vuoden", numbers: "nollan_yhden_kahden_kolmen_neljän_viiden_kuuden_seitsemän_kahdeksan_yhdeksän".split("_") }, s = n2 && !e2 ? a : i2, _ = s[t2];
        return u2 < 10 ? _.replace("%d", s.numbers[u2]) : _.replace("%d", u2);
      }
      var i = { name: "fi", weekdays: "sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai".split("_"), weekdaysShort: "su_ma_ti_ke_to_pe_la".split("_"), weekdaysMin: "su_ma_ti_ke_to_pe_la".split("_"), months: "tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesäkuu_heinäkuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu".split("_"), monthsShort: "tammi_helmi_maalis_huhti_touko_kesä_heinä_elo_syys_loka_marras_joulu".split("_"), ordinal: function(u2) {
        return u2 + ".";
      }, weekStart: 1, yearStart: 4, relativeTime: { future: "%s päästä", past: "%s sitten", s: n, m: n, mm: n, h: n, hh: n, d: n, dd: n, M: n, MM: n, y: n, yy: n }, formats: { LT: "HH.mm", LTS: "HH.mm.ss", L: "DD.MM.YYYY", LL: "D. MMMM[ta] YYYY", LLL: "D. MMMM[ta] YYYY, [klo] HH.mm", LLLL: "dddd, D. MMMM[ta] YYYY, [klo] HH.mm", l: "D.M.YYYY", ll: "D. MMM YYYY", lll: "D. MMM YYYY, [klo] HH.mm", llll: "ddd, D. MMM YYYY, [klo] HH.mm" } };
      return t.default.locale(i, null, true), i;
    });
  }
});
export default require_fi();
//# sourceMappingURL=dayjs_locale_fi.js.map
