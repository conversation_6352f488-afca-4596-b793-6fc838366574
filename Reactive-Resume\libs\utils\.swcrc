{"jsc": {"target": "es2017", "parser": {"syntax": "typescript", "decorators": true, "dynamicImport": true}, "transform": {"decoratorMetadata": true, "legacyDecorator": true}, "keepClassNames": true, "externalHelpers": true, "loose": true}, "module": {"type": "commonjs"}, "sourceMaps": true, "exclude": ["jest.config.ts", ".*\\.spec.tsx?$", ".*\\.test.tsx?$", "./src/jest-setup.ts$", "./**/jest-setup.ts$", ".*.js$"]}