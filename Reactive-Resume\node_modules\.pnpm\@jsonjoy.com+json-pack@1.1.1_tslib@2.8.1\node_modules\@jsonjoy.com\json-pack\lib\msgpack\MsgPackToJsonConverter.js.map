{"version": 3, "file": "MsgPackToJsonConverter.js", "sourceRoot": "", "sources": ["../../src/msgpack/MsgPackToJsonConverter.ts"], "names": [], "mappings": ";;;AACA,qEAAgE;AAChE,yDAAoD;AAKpD,MAAa,sBAAsB;IAQjC;QANU,UAAK,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QAE3B,SAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEvC,MAAC,GAAG,CAAC,CAAC;IAEM,CAAC;IAEhB,KAAK,CAAC,KAAiB;QAC5B,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACzE,CAAC;IAQM,OAAO,CAAc,KAAiB;QAC3C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC,GAAG,EAAoB,CAAC;IACtC,CAAC;IAGS,GAAG;QACX,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACvB,IAAI,IAAI,IAAI,IAAI;YAAE,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;QACnD,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC;gBAChB,IAAI,IAAI,IAAI,SAAS;oBAAE,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC9C,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,GAAG,IAAI;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;;oBAE3C,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QACD,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;oBACjB,IAAI,IAAI,IAAI,IAAI;wBAAE,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;;wBACrD,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBAC3D,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,IAAI,IAAI;wBAAE,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;;wBAChF,OAAO,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;gBACzE,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,IAAI,IAAI;oBACjB,CAAC,CAAC,IAAI,IAAI,IAAI;wBACZ,CAAC,CAAC,IAAI,KAAK,IAAI;4BACb,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;4BACvB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;wBACxB,CAAC,CAAC,IAAI,KAAK,IAAI;4BACb,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE;4BACtB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;oBAC3B,CAAC,CAAC,IAAI,IAAI,IAAI;wBACZ,CAAC,CAAC,IAAI,KAAK,IAAI;4BACb,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;4BACvB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;wBACzB,CAAC,CAAC,IAAI,KAAK,IAAI;4BACb,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE;4BACtB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC5D,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACxB,OAAO,IAAI,IAAI,IAAI;gBACjB,CAAC,CAAC,IAAI,IAAI,IAAI;oBACZ,CAAC,CAAC,IAAI,KAAK,IAAI;wBACb,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;wBACvB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;oBACzB,CAAC,CAAC,IAAI,KAAK,IAAI;wBACb,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;wBACb,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;gBACvD,CAAC,CAAC,IAAI,IAAI,IAAI;oBACZ,CAAC,CAAC,IAAI,KAAK,IAAI;wBACb,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;wBACb,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,IAAI,KAAK,IAAI;wBACb,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;wBACd,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,IAAI;oBACP,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC7B,KAAK,IAAI;oBACP,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC9B,KAAK,IAAI;oBACP,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC9B,KAAK,IAAI;oBACP,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC9B,KAAK,IAAI;oBACP,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC9B,KAAK,IAAI;oBACP,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC9B,KAAK,IAAI;oBACP,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAGS,GAAG,CAAC,IAAY;QACxB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACf,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC;YACf,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,CAAE,CAAC;YACvB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtB,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;gBAC/B,SAAS;YACX,CAAC;iBAAM,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBAChC,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC,CAAC,CAAC;YACxE,CAAC;iBAAM,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBAChC,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC,CAAC,CAAC;YACvG,CAAC;iBAAM,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBAChC,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC;gBAC9B,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC;gBAC9B,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,CAAE,GAAG,IAAI,CAAC;gBAC9B,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;gBACpE,IAAI,IAAI,GAAG,MAAM,EAAE,CAAC;oBAClB,IAAI,IAAI,OAAO,CAAC;oBAChB,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;oBAC7D,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;gBACjC,CAAC;gBACD,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;QACD,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QACb,OAAO,IAAA,mBAAQ,EAAC,GAAG,CAAC,CAAC;IACvB,CAAC;IAGS,GAAG,CAAC,IAAY;QACxB,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,IAAI,CAAC,GAAG,CAAC;gBAAE,GAAG,IAAI,GAAG,CAAC;YACtB,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YAClB,GAAG,IAAI,GAAG,CAAC;YACX,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;QACpB,CAAC;QACD,OAAO,CAAC,GAAG,GAAG,GAAG,CAAwB,CAAC;IAC5C,CAAC;IAGS,GAAG;QACX,OAAO,IAAI,CAAC,GAAG,EAAyB,CAAC;IAC3C,CAAC;IAGS,GAAG,CAAC,IAAY;QACxB,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,IAAI,CAAC,GAAG,CAAC;gBAAE,GAAG,IAAI,GAAG,CAAC;YACtB,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;QACpB,CAAC;QACD,OAAO,CAAC,GAAG,GAAG,GAAG,CAA2B,CAAC;IAC/C,CAAC;IAGS,GAAG,CAAC,IAAY;QACxB,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QACb,OAAO,GAAG,GAAG,IAAA,qBAAS,EAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IACpC,CAAC;IAGS,GAAG,CAAC,IAAY;QACxB,MAAM,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACtB,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QACb,OAAO,GAAG,GAAG,IAAA,qBAAS,EAAC,GAAG,EAAE,EAAC,GAAG,EAAC,CAAC,GAAG,GAAG,CAAC;IAC3C,CAAC;IAGS,EAAE;QACV,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACtC,CAAC;IAGS,GAAG;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAGS,GAAG;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAGS,EAAE;QACV,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;IAGS,GAAG;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAGS,GAAG;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC;IACb,CAAC;IAGS,GAAG;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAGS,GAAG;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;CACF;AAzOD,wDAyOC"}