{"name": "@reactive-resume/source", "description": "A free and open-source resume builder that simplifies the process of creating, updating, and sharing your resume.", "version": "4.4.6", "license": "MIT", "private": true, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://amruthpillai.com", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/AmruthPillai/Reactive-Resume.git"}, "scripts": {"dev": "nx run-many -t serve", "test": "pnpm vitest run", "prebuild": "pnpm prisma:generate", "build": "nx run-many -t build", "prestart": "pnpm prisma:migrate", "start": "node dist/apps/server/main", "lint": "nx run-many -t lint", "lint:fix": "nx run-many -t lint --fix", "format": "pnpm exec prettier -c --log-level error .", "format:fix": "pnpm exec prettier -w --log-level error .", "crowdin:sync": "crowdin push && crowdin pull", "prisma:generate": "pnpm exec prisma generate", "prisma:migrate": "pnpm exec prisma migrate deploy", "prisma:migrate:dev": "pnpm exec prisma migrate dev", "messages:extract": "pnpm exec lingui extract --clean --overwrite"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/preset-react": "^7.26.3", "@lingui/cli": "^4.14.1", "@lingui/conf": "^4.14.1", "@lingui/swc-plugin": "^4.1.0", "@lingui/vite-plugin": "^4.14.1", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.15", "@nx/eslint": "^19.8.14", "@nx/eslint-plugin": "^19.8.14", "@nx/jest": "^19.8.14", "@nx/js": "^19.8.14", "@nx/nest": "^19.8.14", "@nx/node": "^19.8.14", "@nx/react": "^19.8.14", "@nx/vite": "^19.8.14", "@nx/web": "^19.8.14", "@nx/webpack": "^19.8.14", "@nx/workspace": "^19.8.14", "@swc-node/register": "^1.10.9", "@swc/cli": "^0.4.0", "@swc/core": "^1.10.12", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-plugin-query": "^5.66.0", "@testing-library/react": "^16.2.0", "@tiptap/core": "^2.11.5", "@types/async-retry": "^1.4.9", "@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/express": "^4.17.21", "@types/express-session": "^1.18.1", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/lodash.debounce": "^4.0.9", "@types/lodash.get": "^4.4.9", "@types/lodash.set": "^4.3.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.0", "@types/nodemailer": "^6.4.17", "@types/papaparse": "^5.3.15", "@types/passport": "^1.0.17", "@types/passport-github2": "^1.2.9", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-local": "^1.0.38", "@types/passport-openidconnect": "^0.1.3", "@types/prismjs": "^1.26.5", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-is": "^18.3.1", "@types/retry": "^0.12.5", "@types/sanitize-html": "^2.13.0", "@types/webfontloader": "^1.6.38", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.7.2", "@vitest/coverage-v8": "^2.1.9", "@vitest/ui": "^2.1.9", "autoprefixer": "^10.4.20", "babel-plugin-macros": "^3.1.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-lingui": "^0.9.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-plugin-unused-imports": "^3.2.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jsdom": "^25.0.1", "nx": "^19.8.14", "postcss": "^8.5.1", "postcss-import": "^16.1.0", "postcss-nested": "^6.2.0", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.3", "vite": "^5.4.14", "vite-plugin-dts": "^4.5.0", "vitest": "^2.1.9"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@fontsource/ibm-plex-sans": "^5.1.1", "@hookform/resolvers": "^3.10.0", "@lingui/core": "^4.14.1", "@lingui/detect-locale": "^4.14.1", "@lingui/macro": "^4.14.1", "@lingui/react": "^4.14.1", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^3.1.3", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^7.4.2", "@nestjs/terminus": "^10.3.0", "@paralleldrive/cuid2": "^2.2.2", "@phosphor-icons/react": "^2.1.7", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.5", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-hover-card": "^1.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-portal": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.7", "@radix-ui/react-visually-hidden": "^1.1.1", "@sindresorhus/slugify": "^1.1.2", "@swc/helpers": "^0.5.15", "@tanstack/react-query": "^5.66.0", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/passport-jwt": "^4.0.1", "async-retry": "^1.3.3", "axios": "^1.7.9", "axios-auth-refresh": "^3.3.6", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cookie-parser": "^1.4.7", "dayjs": "^1.11.13", "deepmerge": "^4.3.1", "express-session": "^1.18.1", "file-saver": "^2.0.5", "framer-motion": "^11.18.2", "fuzzy": "^0.1.3", "helmet": "^7.2.0", "immer": "^10.1.1", "jszip": "^3.10.1", "lodash.debounce": "^4.0.8", "lodash.get": "^4.4.2", "lodash.set": "^4.3.2", "minio": "^8.0.4", "nest-raven": "^10.1.0", "nestjs-minio-client": "^2.2.0", "nestjs-prisma": "^0.24.0", "nestjs-zod": "^3.0.0", "nodemailer": "^6.10.0", "openai": "^4.82.0", "otplib": "^12.0.1", "papaparse": "^5.5.2", "passport": "^0.7.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-openidconnect": "^0.1.2", "pdf-lib": "^1.17.1", "prisma": "^5.22.0", "prismjs": "^1.29.0", "puppeteer": "^23.11.1", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-parallax-tilt": "^1.7.277", "react-resizable-panels": "^2.1.7", "react-router": "^7.1.5", "react-simple-code-editor": "^0.14.1", "react-zoom-pan-pinch": "^3.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sanitize-html": "^2.14.0", "sharp": "^0.33.5", "tailwind-merge": "^2.6.0", "tslib": "^2.8.1", "unique-names-generator": "^4.7.1", "use-breakpoint": "^4.0.6", "use-keyboard-shortcut": "^1.1.6", "usehooks-ts": "^3.1.0", "webfontloader": "^1.6.28", "zod": "^3.24.1", "zod-to-json-schema": "^3.24.1", "zundo": "^2.3.0", "zustand": "^4.5.6"}, "engines": {"node": ">=22.13.1"}, "prisma": {"schema": "tools/prisma/schema.prisma"}}