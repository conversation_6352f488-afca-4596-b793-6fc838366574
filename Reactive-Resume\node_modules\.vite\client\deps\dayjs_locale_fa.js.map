{"version": 3, "sources": ["../../../.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/fa.js"], "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_fa=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"fa\",weekdays:\"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه\".split(\"_\"),weekdaysShort:\"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه\".split(\"_\"),weekdaysMin:\"ی_د_س_چ_پ_ج_ش\".split(\"_\"),weekStart:6,months:\"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر\".split(\"_\"),monthsShort:\"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"در %s\",past:\"%s پیش\",s:\"چند ثانیه\",m:\"یک دقیقه\",mm:\"%d دقیقه\",h:\"یک ساعت\",hh:\"%d ساعت\",d:\"یک روز\",dd:\"%d روز\",M:\"یک ماه\",MM:\"%d ماه\",y:\"یک سال\",yy:\"%d سال\"}};return t.default.locale(d,null,!0),d}));"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,mBAAgB,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,OAAO,GAAE,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,kBAAgB,EAAE,EAAE,KAAK;AAAA,IAAC,EAAE,SAAM,SAAS,GAAE;AAAC;AAAa,eAAS,EAAEA,IAAE;AAAC,eAAOA,MAAG,YAAU,OAAOA,MAAG,aAAYA,KAAEA,KAAE,EAAC,SAAQA,GAAC;AAAA,MAAC;AAAC,UAAI,IAAE,EAAE,CAAC,GAAE,IAAE,EAAC,MAAK,MAAK,UAAS,qDAAqD,MAAM,GAAG,GAAE,eAAc,qDAAqD,MAAM,GAAG,GAAE,aAAY,gBAAgB,MAAM,GAAG,GAAE,WAAU,GAAE,QAAO,wEAAwE,MAAM,GAAG,GAAE,aAAY,wEAAwE,MAAM,GAAG,GAAE,SAAQ,SAASA,IAAE;AAAC,eAAOA;AAAA,MAAC,GAAE,SAAQ,EAAC,IAAG,SAAQ,KAAI,YAAW,GAAE,cAAa,IAAG,eAAc,KAAI,qBAAoB,MAAK,0BAAyB,GAAE,cAAa,EAAC,QAAO,SAAQ,MAAK,UAAS,GAAE,aAAY,GAAE,YAAW,IAAG,YAAW,GAAE,WAAU,IAAG,WAAU,GAAE,UAAS,IAAG,UAAS,GAAE,UAAS,IAAG,UAAS,GAAE,UAAS,IAAG,SAAQ,EAAC;AAAE,aAAO,EAAE,QAAQ,OAAO,GAAE,MAAK,IAAE,GAAE;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["_"]}