import { describe, expect, it } from "vitest";

import {
  extractUrl,
  generateRandomName,
  getInitials,
  isEmptyString,
  isUrl,
  processUsername,
} from "../string";

describe("getInitials", () => {
  it("returns the initials of a name", () => {
    expect(getInitials("<PERSON> Do<PERSON>")).toBe("JD");
    expect(getInitials("<PERSON>")).toBe("MW");
  });
});

describe("isUrl", () => {
  it("checks if a string is a URL", () => {
    expect(isUrl("https://example.com")).toBe(true);
    expect(isUrl("not a url")).toBe(false);
  });
});

describe("isEmptyString", () => {
  it("checks if a string is empty or only contains whitespace", () => {
    expect(isEmptyString("")).toBe(true);
    expect(isEmptyString(" ")).toBe(true);
    expect(isEmptyString("<p></p>")).toBe(true);
    expect(isEmptyString("not empty")).toBe(false);
  });
});

describe("extractUrl", () => {
  it("extracts a URL from a string", () => {
    expect(extractUrl("Visit https://example.com today!")).toBe("https://example.com");
    expect(extractUrl("No URL here.")).toBeNull();
  });
});

describe("generateRandomName", () => {
  it("generates a random name", () => {
    const name = generateRandomName();
    expect(name).toMatch(/^(?:[A-Z][a-z]+ ){2}[A-Z][a-z]+$/);
  });
});

describe("processUsername", () => {
  it("processes a username by removing non-alphanumeric characters", () => {
    expect(processUsername("User@Name!")).toBe("username");
    expect(processUsername("")).toBe("");
    expect(processUsername(null)).toBe("");
  });
});
