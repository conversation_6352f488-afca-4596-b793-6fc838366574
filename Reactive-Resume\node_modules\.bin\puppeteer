#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/lib/cjs/puppeteer/node/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/lib/cjs/puppeteer/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/lib/cjs/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/lib/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/lib/cjs/puppeteer/node/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/lib/cjs/puppeteer/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/lib/cjs/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/lib/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules:/mnt/c/Users/<USER>/Documents/GitHubRepositories/Reactive-Resume/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/lib/cjs/puppeteer/node/cli.js" "$@"
else
  exec node  "$basedir/../.pnpm/puppeteer@23.11.1_typescript@5.7.3/node_modules/puppeteer/lib/cjs/puppeteer/node/cli.js" "$@"
fi
