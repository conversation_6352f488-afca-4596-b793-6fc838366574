{"version": 3, "sources": ["../../../.pnpm/use-keyboard-shortcut@1.1.6_58fb67bdbd06d513d87d5e86e1e63075/node_modules/use-keyboard-shortcut/lib/useKeyboardShortcut.js", "../../../.pnpm/use-keyboard-shortcut@1.1.6_58fb67bdbd06d513d87d5e86e1e63075/node_modules/use-keyboard-shortcut/lib/utils.js", "../../../.pnpm/use-keyboard-shortcut@1.1.6_58fb67bdbd06d513d87d5e86e1e63075/node_modules/use-keyboard-shortcut/index.js"], "sourcesContent": ["import { useEffect, useCallback, useRef, useMemo } from \"react\";\nimport {\n  overrideSystemHandling,\n  checkHeldKeysRecursive,\n  uniq_fast,\n} from \"./utils\";\n\nconst BLACKLISTED_DOM_TARGETS = [\"TEXTAREA\", \"INPUT\"];\n\nconst DEFAULT_OPTIONS = {\n  overrideSystem: false,\n  ignoreInputFields: true,\n  repeatOnHold: true,\n};\n\nconst useKeyboardShortcut = (shortcutKeys, callback, userOptions) => {\n  const options = { ...DEFAULT_OPTIONS, ...userOptions };\n  if (!Array.isArray(shortcutKeys))\n    throw new Error(\n      \"The first parameter to `useKeyboardShortcut` must be an ordered array of `KeyboardEvent.key` strings.\"\n    );\n\n  if (!shortcutKeys.length)\n    throw new Error(\n      \"The first parameter to `useKeyboardShortcut` must contain atleast one `KeyboardEvent.key` string.\"\n    );\n\n  if (!callback || typeof callback !== \"function\")\n    throw new Error(\n      \"The second parameter to `useKeyboardShortcut` must be a function that will be envoked when the keys are pressed.\"\n    );\n\n  const shortcutKeysId = useMemo(() => shortcutKeys.join(), [shortcutKeys]);\n\n  // Normalizes the shortcut keys a deduplicated array of lowercased keys.\n  const shortcutArray = useMemo(\n    () => uniq_fast(shortcutKeys).map((key) => String(key).toLowerCase()),\n    // While using .join() is bad for most larger objects, this shortcut\n    // array is fine as it's small, according to the answer below.\n    // https://github.com/facebook/react/issues/14476#issuecomment-471199055\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [shortcutKeysId]\n  );\n  // useRef to avoid a constant re-render on keydown and keyup.\n  const heldKeys = useRef([]);\n\n  const keydownListener = useCallback(\n    (keydownEvent) => {\n      const loweredKey = String(keydownEvent.key).toLowerCase();\n      if (!(shortcutArray.indexOf(loweredKey) >= 0)) return;\n\n      if (\n        options.ignoreInputFields &&\n        BLACKLISTED_DOM_TARGETS.indexOf(keydownEvent.target.tagName) >= 0\n      ) {\n        return;\n      }\n\n      if (keydownEvent.repeat && !options.repeatOnHold) return;\n\n      if (options.overrideSystem) {\n        overrideSystemHandling(keydownEvent);\n      }\n      // This needs to be checked as soon as possible to avoid\n      // all option checks that might prevent default behavior\n      // of the key press.\n      //\n      // I.E If shortcut is \"Shift + A\", we shouldn't prevent the\n      // default browser behavior of Select All Text just because\n      // \"A\" is being observed for our custom behavior shortcut.\n      const isHeldKeyCombinationValid = checkHeldKeysRecursive(\n        loweredKey,\n        null,\n        shortcutArray,\n        heldKeys.current\n      );\n\n      if (!isHeldKeyCombinationValid) {\n        return;\n      }\n\n      const nextHeldKeys = [...heldKeys.current, loweredKey];\n      if (nextHeldKeys.join() === shortcutArray.join()) {\n        callback(shortcutKeys);\n        return false;\n      }\n\n      heldKeys.current = nextHeldKeys;\n\n      return false;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      shortcutKeysId,\n      callback,\n      options.overrideSystem,\n      options.ignoreInputFields,\n    ]\n  );\n\n  const keyupListener = useCallback(\n    (keyupEvent) => {\n      const raisedKey = String(keyupEvent.key).toLowerCase();\n      if (!(shortcutArray.indexOf(raisedKey) >= 0)) return;\n\n      const raisedKeyHeldIndex = heldKeys.current.indexOf(raisedKey);\n      if (!(raisedKeyHeldIndex >= 0)) return;\n\n      let nextHeldKeys = [];\n      let loopIndex;\n      for (loopIndex = 0; loopIndex < heldKeys.current.length; ++loopIndex) {\n        if (loopIndex !== raisedKeyHeldIndex) {\n          nextHeldKeys.push(heldKeys.current[loopIndex]);\n        }\n      }\n      heldKeys.current = nextHeldKeys;\n\n      return false;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [shortcutKeysId]\n  );\n\n  const flushHeldKeys = useCallback(() => {\n    heldKeys.current = [];\n  }, []);\n\n  useEffect(() => {\n    window.addEventListener(\"keydown\", keydownListener);\n    window.addEventListener(\"keyup\", keyupListener);\n    return () => {\n      window.removeEventListener(\"keydown\", keydownListener);\n      window.removeEventListener(\"keyup\", keyupListener);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [keydownListener, keyupListener, shortcutKeysId]);\n\n  // Resets the held keys array if the shortcut keys are changed.\n  useEffect(() => {\n    flushHeldKeys();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [shortcutKeysId, flushHeldKeys]);\n\n  return {\n    flushHeldKeys,\n  };\n};\n\nexport default useKeyboardShortcut;\n", "export const overrideSystemHandling = (e) => {\n  if (e) {\n    if (e.preventDefault) e.preventDefault();\n    if (e.stopPropagation) {\n      e.stopPropagation();\n    } else if (window.event) {\n      window.event.cancelBubble = true;\n    }\n  }\n};\n\n// Function stolen from this Stack Overflow answer:\n// https: stackoverflow.com/a/9229821\nexport const uniq_fast = (a) => {\n  var seen = {};\n  var out = [];\n  var len = a.length;\n  var j = 0;\n  for (var i = 0; i < len; i++) {\n    var item = a[i];\n    if (seen[item] !== 1) {\n      seen[item] = 1;\n      out[j++] = item;\n    }\n  }\n  return out;\n};\n\n// The goal for this recursive function is to check to ensure\n// that the keys are held down in the correct order of the shortcut.\n// I.E if the shortcut array is [\"Shift\", \"E\", \"A\"], this function will ensure\n// that \"E\" is held down before \"A\", and \"Shift\" is held down before \"E\".\nexport const checkHeldKeysRecursive = (\n  shortcutKey,\n  // Tracks the call interation for the recursive function,\n  // based on the previous index;\n  shortcutKeyRecursionIndex = 0,\n  shortcutArray,\n  heldKeysArray\n) => {\n  const shortcutIndexOfKey = shortcutArray.indexOf(shortcutKey);\n  const keyPartOfShortCut = shortcutArray.indexOf(shortcutKey) >= 0;\n\n  // Early exit if they key isn't even in the shortcut combination.\n  if (!keyPartOfShortCut) return false;\n\n  // While holding down one of the keys, if another is to be let go, the shortcut\n  // should be void. Shortcut keys must be held down in a specifc order.\n  // This function is always called before a key is added to held keys on keydown,\n  // this will ensure that heldKeys only contains the prefixing keys\n  const comparisonIndex = Math.max(heldKeysArray.length - 1, 0);\n  if (\n    heldKeysArray.length &&\n    heldKeysArray[comparisonIndex] !== shortcutArray[comparisonIndex]\n  ) {\n    return false;\n  }\n\n  // Early exit for the first held down key in the shortcut,\n  // except if this is a recursive call\n  if (shortcutIndexOfKey === 0) {\n    // If this isn't the first interation of this recursive function, and we're\n    // recursively calling this function, we should always be checking the\n    // currently held down keys instead of returning true\n    if (shortcutKeyRecursionIndex > 0)\n      return heldKeysArray.indexOf(shortcutKey) >= 0;\n    return true;\n  }\n\n  const previousShortcutKeyIndex = shortcutIndexOfKey - 1;\n  const previousShortcutKey = shortcutArray[previousShortcutKeyIndex];\n  const previousShortcutKeyHeld =\n    heldKeysArray[previousShortcutKeyIndex] === previousShortcutKey;\n\n  // Early exit if the key just before the currently checked shortcut key\n  // isn't being held down.\n  if (!previousShortcutKeyHeld) return false;\n\n  // Recursively call this function with the previous key as the new shortcut key\n  // but the index of the current shortcut key.\n  return checkHeldKeysRecursive(\n    previousShortcutKey,\n    shortcutIndexOfKey,\n    shortcutArray,\n    heldKeysArray\n  );\n};\n", "import useKeyboardShortcut from './lib/useKeyboardShortcut'\n\nexport default useKeyboardShortcut\n"], "mappings": ";;;;;;;;AAAA,mBAAwD;;;ACAjD,IAAM,yBAAyB,CAAC,MAAM;AAC3C,MAAI,GAAG;AACL,QAAI,EAAE,eAAgB,GAAE,eAAe;AACvC,QAAI,EAAE,iBAAiB;AACrB,QAAE,gBAAgB;AAAA,IACpB,WAAW,OAAO,OAAO;AACvB,aAAO,MAAM,eAAe;AAAA,IAC9B;AAAA,EACF;AACF;AAIO,IAAM,YAAY,CAAC,MAAM;AAC9B,MAAI,OAAO,CAAC;AACZ,MAAI,MAAM,CAAC;AACX,MAAI,MAAM,EAAE;AACZ,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,OAAO,EAAE,CAAC;AACd,QAAI,KAAK,IAAI,MAAM,GAAG;AACpB,WAAK,IAAI,IAAI;AACb,UAAI,GAAG,IAAI;AAAA,IACb;AAAA,EACF;AACA,SAAO;AACT;AAMO,IAAM,yBAAyB,CACpC,aAGA,4BAA4B,GAC5B,eACA,kBACG;AACH,QAAM,qBAAqB,cAAc,QAAQ,WAAW;AAC5D,QAAM,oBAAoB,cAAc,QAAQ,WAAW,KAAK;AAGhE,MAAI,CAAC,kBAAmB,QAAO;AAM/B,QAAM,kBAAkB,KAAK,IAAI,cAAc,SAAS,GAAG,CAAC;AAC5D,MACE,cAAc,UACd,cAAc,eAAe,MAAM,cAAc,eAAe,GAChE;AACA,WAAO;AAAA,EACT;AAIA,MAAI,uBAAuB,GAAG;AAI5B,QAAI,4BAA4B;AAC9B,aAAO,cAAc,QAAQ,WAAW,KAAK;AAC/C,WAAO;AAAA,EACT;AAEA,QAAM,2BAA2B,qBAAqB;AACtD,QAAM,sBAAsB,cAAc,wBAAwB;AAClE,QAAM,0BACJ,cAAc,wBAAwB,MAAM;AAI9C,MAAI,CAAC,wBAAyB,QAAO;AAIrC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AD/EA,IAAM,0BAA0B,CAAC,YAAY,OAAO;AAEpD,IAAM,kBAAkB;AAAA,EACtB,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,cAAc;AAChB;AAEA,IAAM,sBAAsB,CAAC,cAAc,UAAU,gBAAgB;AACnE,QAAM,UAAU,EAAE,GAAG,iBAAiB,GAAG,YAAY;AACrD,MAAI,CAAC,MAAM,QAAQ,YAAY;AAC7B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAEF,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAEF,MAAI,CAAC,YAAY,OAAO,aAAa;AACnC,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAEF,QAAM,qBAAiB,sBAAQ,MAAM,aAAa,KAAK,GAAG,CAAC,YAAY,CAAC;AAGxE,QAAM,oBAAgB;AAAA,IACpB,MAAM,UAAU,YAAY,EAAE,IAAI,CAAC,QAAQ,OAAO,GAAG,EAAE,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,IAKpE,CAAC,cAAc;AAAA,EACjB;AAEA,QAAM,eAAW,qBAAO,CAAC,CAAC;AAE1B,QAAM,sBAAkB;AAAA,IACtB,CAAC,iBAAiB;AAChB,YAAM,aAAa,OAAO,aAAa,GAAG,EAAE,YAAY;AACxD,UAAI,EAAE,cAAc,QAAQ,UAAU,KAAK,GAAI;AAE/C,UACE,QAAQ,qBACR,wBAAwB,QAAQ,aAAa,OAAO,OAAO,KAAK,GAChE;AACA;AAAA,MACF;AAEA,UAAI,aAAa,UAAU,CAAC,QAAQ,aAAc;AAElD,UAAI,QAAQ,gBAAgB;AAC1B,+BAAuB,YAAY;AAAA,MACrC;AAQA,YAAM,4BAA4B;AAAA,QAChC;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,MACX;AAEA,UAAI,CAAC,2BAA2B;AAC9B;AAAA,MACF;AAEA,YAAM,eAAe,CAAC,GAAG,SAAS,SAAS,UAAU;AACrD,UAAI,aAAa,KAAK,MAAM,cAAc,KAAK,GAAG;AAChD,iBAAS,YAAY;AACrB,eAAO;AAAA,MACT;AAEA,eAAS,UAAU;AAEnB,aAAO;AAAA,IACT;AAAA;AAAA,IAEA;AAAA,MACE;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AAEA,QAAM,oBAAgB;AAAA,IACpB,CAAC,eAAe;AACd,YAAM,YAAY,OAAO,WAAW,GAAG,EAAE,YAAY;AACrD,UAAI,EAAE,cAAc,QAAQ,SAAS,KAAK,GAAI;AAE9C,YAAM,qBAAqB,SAAS,QAAQ,QAAQ,SAAS;AAC7D,UAAI,EAAE,sBAAsB,GAAI;AAEhC,UAAI,eAAe,CAAC;AACpB,UAAI;AACJ,WAAK,YAAY,GAAG,YAAY,SAAS,QAAQ,QAAQ,EAAE,WAAW;AACpE,YAAI,cAAc,oBAAoB;AACpC,uBAAa,KAAK,SAAS,QAAQ,SAAS,CAAC;AAAA,QAC/C;AAAA,MACF;AACA,eAAS,UAAU;AAEnB,aAAO;AAAA,IACT;AAAA;AAAA,IAEA,CAAC,cAAc;AAAA,EACjB;AAEA,QAAM,oBAAgB,0BAAY,MAAM;AACtC,aAAS,UAAU,CAAC;AAAA,EACtB,GAAG,CAAC,CAAC;AAEL,8BAAU,MAAM;AACd,WAAO,iBAAiB,WAAW,eAAe;AAClD,WAAO,iBAAiB,SAAS,aAAa;AAC9C,WAAO,MAAM;AACX,aAAO,oBAAoB,WAAW,eAAe;AACrD,aAAO,oBAAoB,SAAS,aAAa;AAAA,IACnD;AAAA,EAEF,GAAG,CAAC,iBAAiB,eAAe,cAAc,CAAC;AAGnD,8BAAU,MAAM;AACd,kBAAc;AAAA,EAEhB,GAAG,CAAC,gBAAgB,aAAa,CAAC;AAElC,SAAO;AAAA,IACL;AAAA,EACF;AACF;AAEA,IAAO,8BAAQ;;;AElJf,IAAO,gCAAQ;", "names": []}